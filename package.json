{"name": "reqtool-fe", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/charts": "^1.3.5", "@ant-design/icons": "^4.7.0", "@ant-design/plots": "^2.5.0", "@ant-design/pro-table": "^2.56.7", "@azure/msal-browser": "^2.18.0", "@azure/msal-react": "^1.1.0", "@ckeditor/ckeditor5-react": "^3.0.3", "@craco/craco": "^6.4.2", "@microsoft/fetch-event-source": "^2.0.1", "@redux-saga/core": "^1.3.0", "@reduxjs/toolkit": "^1.6.2", "@svgr/webpack": "^5.5.0", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "@tiptap/extension-character-count": "^2.23.0", "@tiptap/extension-code-block-lowlight": "^2.23.0", "@tiptap/extension-color": "^2.23.0", "@tiptap/extension-highlight": "^2.23.0", "@tiptap/extension-horizontal-rule": "^2.23.0", "@tiptap/extension-image": "^2.23.0", "@tiptap/extension-link": "^2.23.0", "@tiptap/extension-mention": "^2.23.0", "@tiptap/extension-placeholder": "^2.23.0", "@tiptap/extension-strike": "^2.23.0", "@tiptap/extension-subscript": "^2.23.0", "@tiptap/extension-superscript": "^2.23.0", "@tiptap/extension-table": "^2.23.0", "@tiptap/extension-table-cell": "^2.23.0", "@tiptap/extension-table-header": "^2.23.0", "@tiptap/extension-table-of-contents": "^2.23.0", "@tiptap/extension-table-row": "^2.23.0", "@tiptap/extension-task-item": "^2.23.0", "@tiptap/extension-task-list": "^2.23.0", "@tiptap/extension-text-align": "^2.23.0", "@tiptap/extension-typography": "^2.23.0", "@tiptap/extension-underline": "^2.23.0", "@tiptap/react": "^2.23.0", "@tiptap/starter-kit": "^2.23.0", "@types/jest": "^26.0.24", "@types/node": "^12.20.33", "@types/react": "^17.0.30", "@types/react-dom": "^17.0.9", "antd": "^4.16.13", "axios": "^0.23.0", "ckeditor5-custom-build": "./lib/ckeditor5", "compare-versions": "^5.0.1", "craco-less": "^1.20.0", "env-cmd": "^10.1.0", "file": "^0.2.2", "file-saver": "^2.0.5", "lodash.debounce": "^4.0.8", "moment": "^2.30.1", "react": "^17.0.2", "react-cookie": "^4.1.1", "react-custom-scrollbars": "^4.2.1", "react-dom": "^17.0.2", "react-drag-listview": "^0.1.8", "react-draggable": "^4.4.5", "react-highlight-words": "^0.17.0", "react-hotkeys": "^2.0.0", "react-intl": "^5.21.0", "react-markdown": "^8.0.7", "react-redux": "^7.2.5", "react-router": "^5.3.4", "react-router-dom": "^5.3.0", "react-scripts": "4.0.3", "react-select": "^5.3.2", "redux": "^4.2.1", "redux-injectors": "^1.3.0", "redux-saga": "^1.1.3", "remark-gfm": "^3.0.1", "tiptap-markdown": "^0.8.10", "turndown": "^7.2.0", "turndown-plugin-gfm": "^1.0.2", "typescript": "^4.4.4", "web-vitals": "^1.1.2", "yarn": "^1.22.18"}, "scripts": {"start": "env-cmd -f .env craco start", "start-dev": "set PORT=3006 && env-cmd -f .env.development craco start", "build": "env-cmd -f .env craco build", "build-dev": "env-cmd -f .env.development craco build", "build-ba": "env-cmd -f .env.ba craco build", "build-prod": "env-cmd -f .env.prod craco build", "test": "env-cmd -f .env craco test", "eject": "env-cmd -f .env react-scripts eject", "format:src": "yarn prettier --write src/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.1", "@types/turndown": "^5.0.5", "prettier": "2.5.1"}}
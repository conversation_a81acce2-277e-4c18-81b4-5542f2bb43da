import { CommentState } from '@/modules/_shared/comment/type'
import { AIAssistantState } from '@/modules/_shared/ai/types'
import { DoubleLeftOutlined, DoubleRightOutlined } from '@ant-design/icons'
import { Button, Layout, Spin, notification } from 'antd'
import { saveAs } from 'file-saver'
import moment from 'moment'
import { FC, useEffect, useState } from 'react'
import { GlobalHotKeys } from 'react-hotkeys'
import { useSelector } from 'react-redux'
import { useHistory } from 'react-router-dom'
import APP_MENU from '../../config/app.menu.config'
import intl from '../../config/locale.config'
import {
  API_URLS,
  APP_ROLES,
  APP_ROUTES,
  MESSAGE_TYPE,
  PROJECT_PREFIX,
} from '../../constants'
import {
  ShowAppMessage,
  currentUserName,
  extractProjectCode,
  hasRole,
} from '../../helper/share'
import SharedComment from '../../modules/_shared/comment'
import GenerateSrs from '../../modules/generate-srs'
import { GenerateSrsState } from '../../modules/generate-srs/type'
import ValidateScopeButton from '../../modules/validate-srs/button'
import TableService from '../../services/lav-table-service'
import AppState from '../../store/types'
import AppHeader from '../components/header'
import AppMenuComponent from '../components/menu'
import SelectCommonComponentModal from '../components/select-common-component'
import AppVersion from '../components/version'
import { AIAssistant } from '../../modules/_shared/ai';
import '../style.less'

const { Content, Sider } = Layout

type Props = {
  isCommon?: boolean | false
  children?: JSX.Element | JSX.Element[]
  isAdmin?: boolean | false
}

const NormalLayout: FC<Props> = (props: Props) => {
  const [collapsed, setCollapsed] = useState<boolean>(false)
  const [menus, setMenus] = useState<any[]>([])
  const history = useHistory()
  const [showValidateSRSModal, setShowValidateSRSModal] = useState(false)
  const [showSelectCommonComponentModal, setShowSelectCommonComponentModal] =
    useState(false)
  const [showGenerateModal, setShowGenerateModal] = useState(false)
  const [accessToken, setAccessToken] = useState(null)

  const state = useSelector<AppState | null>(
    (s) => s?.generateSrs
  ) as GenerateSrsState

  const commentState = useSelector<AppState | null>(
    (s) => s?.Comment
  ) as CommentState

  const aiAssistantState = useSelector<AppState | null>(
    (s) => s?.aiAssistant
  ) as AIAssistantState

  useEffect(() => {
    setMenus(
      props.isCommon
        ? APP_MENU.CommonMenuConfig
        : props.isAdmin
          ? APP_MENU.AdminMenuConfig
          : APP_MENU.AppMenuConfig
    )
  }, [props])

  const toggleCollapsed = () => {
    setCollapsed(!collapsed)
  }

  const handleCustomMenuClick = (e: any, menu: any) => {
    e.preventDefault()
    switch (menu.label) {
      case 'app.menu.utilities.validate_srs': {
        if (hasRole(APP_ROLES.BA_LEAD) || hasRole(APP_ROLES.PM)) {
          setShowValidateSRSModal(true)
        } else {
          const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.VALIDATION_RESULT
            }/${currentUserName()}/${new Date().getTime()}`
          history.push(href)
        }
        break
      }
      case 'app.menu.utilities.generate_srs': {
        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.GENERATE_SRS
          }`
        history.push(href)
        break

        // (hasRole(APP_ROLES.BA_LEAD) || hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ? setShowGenerateModal(true) : setShowGenerateModal(false);
        // break;
      }
      case 'app.menu.utilities.select_from_common_requirement': {
        setShowSelectCommonComponentModal(true)
        break
      }
      case 'app.menu.utilities.recommend_common_requirement': {
        break
      }
      case 'app.menu.utilities.export_comments': {
        if (hasRole(APP_ROLES.BA_LEAD) || hasRole(APP_ROLES.PM)) {
          const reqNoti = notification
          TableService.export(`${API_URLS.EXPORT_COMMENT}`)
            .then((res: any) => {
              var blob = new Blob([res.data])
              let fName = `${intl.formatMessage({
                id: `${extractProjectCode()}_Comments`,
              })}_${moment().format('DDMMYYYYHHMMSS')}.xlsx`
              saveAs(blob, fName)
            })
            .catch((e) => {
              try {
                let responseMess: any = e.response.data
                const uint8Array: any = new Uint8Array(responseMess)
                const messId = String.fromCharCode.apply(null, uint8Array)
                reqNoti['error']({
                  description: intl.formatMessage({ id: messId }),
                  message: intl.formatMessage({ id: 'common.message.error' }),
                  placement: 'bottomRight',
                })
              } catch (err) {
                ShowAppMessage(MESSAGE_TYPE.ERROR)
              }
            })
          e.stopPropagation()
        }
        break
      }
    }
  }

  const keyMap = { COLLAPSE: '`' }
  const handlers = {
    COLLAPSE: (e) => {
      setCollapsed(!collapsed)
    },
  }

  return (
    <GlobalHotKeys
      allowChanges={true}
      className={collapsed ? 'collapsed' : ''}
      handlers={handlers}
      keyMap={keyMap}
    >
      <Layout>
        <Spin spinning={state.isLoading}>
          <AppHeader
            isAdmin={props.isAdmin || false}
            showDashboard
            isCommon={props.isCommon || false}
            accessTokenReceived={setAccessToken}
          />
          <Layout
            className={`page-wrapper ${collapsed ? `collapsed` : 'expaned'} ${aiAssistantState.isAiPanelOpen ? 'ai-panels-open' : ''}`}
          >
            <Sider
              className={
                collapsed
                  ? `custom-sider-container-collapsed`
                  : 'custom-sider-container'
              }
              trigger={null}
              collapsible
              collapsed={collapsed}
            >
              <div className="sider-menu-wrapper">
                <div className="sidebar-content">
                  {props.isCommon ? (
                    <div
                      className={
                        collapsed
                          ? 'project-and-collapse-collapsed'
                          : 'project-and-collapse'
                      }
                    >
                      {collapsed ? null : (
                        <span className="project-text">
                          {intl.formatMessage({
                            id: 'app.menu.common_requirement',
                          })}
                        </span>
                      )}
                    </div>
                  ) : !props.isAdmin ? (
                    <>
                      <div
                        className={
                          collapsed
                            ? 'project-and-collapse-collapsed'
                            : 'project-and-collapse'
                        }
                      >
                        {collapsed ? null : (
                          <span className="project-text">
                            {intl.formatMessage({ id: 'app.menu.project' })}
                          </span>
                        )}
                      </div>
                      <div className="project-name">
                        <div className="project-logo">
                          <img
                            src="https://insight.fsoft.com.vn/jira9/secure/projectavatar?avatarId=10324"
                            alt=""
                          />
                        </div>
                        <span style={collapsed ? { visibility: 'hidden' } : {}}>
                          {extractProjectCode()}
                        </span>
                      </div>
                    </>
                  ) : (
                    <></>
                  )}
                  <AppMenuComponent
                    collapsed={collapsed}
                    menus={menus}
                    isAdmin={props.isAdmin}
                    isCommon={props.isCommon}
                    onCustomMenuClick={handleCustomMenuClick}
                  />
                </div>
                <div
                  className={`siderbar-fixed-bottom ${collapsed
                    ? 'project-and-collapse-collapsed'
                    : 'project-and-collapse'
                    }`}
                >
                  <AppVersion />
                  <Button
                    type="text"
                    onClick={toggleCollapsed}
                    icon={
                      collapsed ? (
                        <DoubleRightOutlined />
                      ) : (
                        <DoubleLeftOutlined />
                      )
                    }
                  />
                </div>
              </div>
            </Sider>
            <Content
              className="main-content-view"
              style={{ position: 'relative' }}
            >
              {accessToken ? (
                <>
                  {props.children}
                  {showValidateSRSModal ? (
                    <ValidateScopeButton
                      onDismiss={() => setShowValidateSRSModal(false)}
                    />
                  ) : (
                    <></>
                  )}
                  {showSelectCommonComponentModal ? (
                    <SelectCommonComponentModal
                      onDismiss={() => setShowSelectCommonComponentModal(false)}
                    />
                  ) : (
                    <></>
                  )}
                  {/* {showGenerateModal ? <GenerateSrs onDismiss={() => setShowGenerateModal(false)} /> : <></>} */}
                  {showGenerateModal ? (
                    <GenerateSrs
                      onDismiss={() => setShowGenerateModal(false)}
                    />
                  ) : (
                    <></>
                  )}
                </>
              ) : (
                <></>
              )}
            </Content>
          </Layout>
        </Spin>
        <SharedComment
          artefact={commentState.artefact}
          field={commentState.field}
          title={commentState.title}
          index={commentState.index}
          isVisible={commentState.isVisible}
          comments={commentState.comments}
          projectId={commentState.projectId}
          itemId={commentState.itemId}
        />
        {/* AI Assistant Components */}
        <AIAssistant collapsed={collapsed} />
      </Layout>
    </GlobalHotKeys>
  )
}
export default NormalLayout

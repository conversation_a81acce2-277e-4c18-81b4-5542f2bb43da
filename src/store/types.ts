import { ActorState } from '@/modules/actor/type'
import { AdminWorkerAgentState } from '@/modules/admin-worker-agent/type'
import { AdminSupervisorAgentState } from '@/modules/admin-supervisor-agent/type'
import { BusinessRuleState } from '@/modules/business-rule/type'
import { CommonCommitteeState } from '@/modules/common/committee/type'
import { CommonComponentState } from '@/modules/common/component/type'
import { CommonObjectState } from '@/modules/common/object/type'
import { CommonScreenState } from '@/modules/common/screen/type'
import { CommonFunctionState } from '@/modules/common/usecase/type'
import { DashboardState } from '@/modules/dashboard/statistic/type'
import { DataMigrationState } from '@/modules/data-migration/type'
import { EmailTemplateState } from '@/modules/email-templates/type'
import { GenerateSrsState } from '@/modules/generate-srs/type'
import { MeetingMinuteState } from '@/modules/meeting-minutes/type'
import { MessagesState } from '@/modules/messages/type'
import { MockupScreenState } from '@/modules/mockup-screen/type'
import { NonFunctionalRequirementState } from '@/modules/non-functional-requirement/type'
import { ObjectRelationshipDiagramState } from '@/modules/object-relationship-diagram/type'
import { ObjectsState } from '@/modules/objects/type'
import { OtherRequirementState } from '@/modules/other-requirement/type'
import { PermissionMatrixState } from '@/modules/permission-matrix/type'
import { ProjectState } from '@/modules/project-management/type'
import { RecommendCommonComponentState } from '@/modules/recommendedcommonrequirement/type'
import { ReferenceDocumentState } from '@/modules/reference-document/type'
import { StateTransitionState } from '@/modules/state-transition/type'
import { UsecaseDiagramState } from '@/modules/usecase-diagram/type'
import { FunctionState } from '@/modules/usecase/type'
import { UserRequirementState } from '@/modules/user-requirement/type'
import { WorkFlowState } from '@/modules/workflow/type'
import { CommentState } from '@/modules/_shared/comment/type'
import { AIAssistantState } from '@/modules/_shared/ai/types'
import { CommonEmailTemplateState } from '@/modules/common/email-templates/type'
import { CommonNonFunctionalRequirementState } from '@/modules/common/non-functional-requirement/type'
import { CommonMessagesState } from '@/modules/common/messages/type'
import { CommonBusinessRuleState } from '@/modules/common/business-rule/type'
import { EpicManagementState } from '@/modules/epic-management/type'
import { SprintManagementState } from '@/modules/sprint-management/type'
import { UserStoryManagementState } from '@/modules/user-story-management/type'
import { PaginationState } from '../helper/component/lav-table/type'
import { PaginationHistoryState } from '../helper/component/lav-history-table/type'
import { GlossaryState } from '@/modules/glossary/type'
import { LavImpactState } from '../helper/component/lav-impact/type'
import { CommonWorkFlowState } from '@/modules/common/workflow/type'
type AppState = {
  Objects: ObjectsState
  Actor: ActorState
  AdminWorkerAgent: AdminWorkerAgentState
  AdminSupervisorAgent: AdminSupervisorAgentState
  MockupScreen: MockupScreenState
  PermissionMatrix: PermissionMatrixState
  Function: FunctionState
  WorkFlow: WorkFlowState
  StateTransition: StateTransitionState
  Messages: MessagesState
  EmailTemplate: EmailTemplateState
  DataMigration: DataMigrationState
  NonFunctionalRequirement: NonFunctionalRequirementState
  UserRequirement: UserRequirementState
  ObjectRelationship: ObjectRelationshipDiagramState
  MeetingMinute: MeetingMinuteState
  BusinessRule: BusinessRuleState
  UseCaseDiagram: UsecaseDiagramState
  ReferenceDocument: ReferenceDocumentState
  CommonCommittee: CommonCommitteeState
  OtherRequirement: OtherRequirementState
  generateSrs: GenerateSrsState
  Dashboard: DashboardState
  Project: ProjectState,
  CommonComponent: CommonComponentState
  commonObject: CommonObjectState
  CommonScreen: CommonScreenState,
  CommonFunction: CommonFunctionState,
  RecommendCommonComponent: RecommendCommonComponentState
  Comment: CommentState
  CommonEmailTemplate: CommonEmailTemplateState
  CommonNonFunctionalRequirement: CommonNonFunctionalRequirementState
  CommonMessage: CommonMessagesState
  CommonWorkFlow: CommonWorkFlowState
  CommonBusinessRule: CommonBusinessRuleState
  Epic: EpicManagementState
  Sprints: SprintManagementState
  UserStory: UserStoryManagementState
  Pagination: PaginationState
  PaginationHistory: PaginationHistoryState
  Glossary: GlossaryState
  LavImpact: LavImpactState
  aiAssistant: AIAssistantState
}
export default AppState

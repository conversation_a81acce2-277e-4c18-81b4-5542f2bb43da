export default {
    'app.menu.version': 'Version',
    'app.menu.project': 'PROJECT',
    'app.menu.dashboard': 'Dashboard',
    'app.menu.all_projects': 'All Projects',
    'app.menu.common_requirement': 'Common Requirement',
    'app.menu.input_management': 'Input Management',
    'app.menu.meeting_minutes': 'Meeting Minutes',
    'app.menu.user_requirements': 'User Requirements',
    'app.menu.reference_documents': 'Reference Documents',
    'app.menu.high_level_requirements': 'High Level Requirements',
    'app.menu.object_relationship_diagram': 'Object Relationship Diagram',
    'app.menu.object_list': 'Object',
    'app.menu.actor_list': 'Actor',
    'app.menu.project_statistic':'Project Statistic',
    'app.menu.my_dashboard':'My Dashboard',
    'app.menu.workflow': 'Workflow',
    'app.menu.state_transition': 'State Transition',
    'app.menu.use_case_diagram': 'Use Case Diagram',
    'app.menu.permission_matrix': 'Permission Matrix',
    'app.menu.functional_requirements': 'Functional Requirements',
    'app.menu.use_case_specifications': 'Use Case Specifications',
    'app.menu.common_business_rule': 'Common Business Rule',
    'app.menu.mockups_screen': 'Mockups Screen',
    'app.menu.non_functional_requirements': 'Non-Functional Requirements',
    'app.menu.other_requirements': 'Other Requirements',
    'app.menu.data_migration': 'Data Migration',
    'app.menu.appendices': 'Appendices',
    'app.menu.message_list': 'Message List',
    'app.menu.term': 'Glossary',
    'app.menu.email_templates': 'Email Templates',
    'app.menu.report': 'Report',
    'app.menu.utilities': 'Utilities',
    'app.menu.utilities.validate_srs': 'Validate SRS',
    'app.menu.utilities.generate_srs': 'Generate SRS',
    'app.menu.utilities.select_from_common_requirement': 'Select from Common Requirement',
    'app.menu.utilities.recommend_common_requirement': 'Recommend Common Requirement',
    'app.menu.utilities.export_comments': 'Export Comments',
    'app.menu.epic': 'Epic Management',
    'app.menu.agile.project_screen':'Agile Project',
    'app.menu.agile.project_screen.sprint':'Sprint Management',
    'app.menu.agile.user-story':'User Story Management',


    'app.menu.common.common_artefacts': 'Common Artefacts',
    'app.menu.common.object': 'Object',
    'app.menu.common.use_case': 'Use Case',
    'app.menu.common.mockup_screen': 'Screen',
    'app.menu.common.cbr': 'Common Business Rule',
    'app.menu.common.message': 'Message',
    'app.menu.common.emailtemplate': 'Email Template',
    'app.menu.common.workflow': 'Workflow',
    'app.menu.common.nonfunctional': 'Non-Functional Requirement',
    'app.menu.common.bundle': 'Bundle',
    'app.menu.common.component': 'Component',
    'app.menu.common.common_req_tool_committee': 'Common Req. Tool Committee',

    'app.menu.admin.admin_artefacts': 'Projects',
    'app.menu.admin.agent_settings': 'Agent Settings',
    'app.menu.admin.supervisor_agent': 'Supervisor Agents',
    'app.menu.admin.worker_agent': 'Worker Agents'
}

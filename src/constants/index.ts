import intl from '../config/locale.config'
import { extractProjectCode } from '../helper/share'

export interface Reference {
  id: number
  name: string
}

export interface VersionType {
  artefactId: number
  artefactType: number
  createdBy: string
  dateCreated: Date
  description: string
  version: string
}

export const PIORITY = {
  LOW: 1,
  MEDIUM: 2,
  HIGHT: 3,
  URGENT: 4,
}


export const PIORITY_OPTIONS = [
  {
    value: 1,
    text: intl.formatMessage({ id: 'user-requirement.piority-low' }),
  },
  {
    value: 2,
    text: intl.formatMessage({ id: 'user-requirement.piority-medium' }),
  },
  {
    value: 3,
    text: intl.formatMessage({ id: 'user-requirement.piority-high' }),
  },
  {
    value: 4,
    text: intl.formatMessage({ id: 'user-requirement.piority-urgent' }),
  },
]

export const DEFAULT_DATA_SCREEN_COMPONENT = {
  editting: true,
  component: "",
  name: "",
  componentType: "",
  defaultValue: "",
  description: "",
  editable: false,
  mandatory: false,
  objectScreenComponent: null,
  screen: null,
  sourceObjectProperties: null,
  status: 0,
  useCase: null,
}

export const fileConfig = [
  {
    id: 1,
    name: intl.formatMessage({ id: 'project.config.one-file' }),
  },
  {
    id: 2,
    name: intl.formatMessage({ id: 'project.config.two-files' }),
  },
]

export const defaultPagingConfig = [
  {
    id: 10,
    name: 10,
  },
  {
    id: 20,
    name: 20,
  },
  {
    id: 50,
    name: 50,
  },
  {
    id: 100,
    name: 100,
  },
]

export const AUDIT_TRAIL = [
  { key: 1, label: 'Created' },
  { key: 2, label: 'Updated' },
  { key: 3, label: 'Submitted' },
  { key: 4, label: 'Approved' },
  { key: 5, label: 'Rejected' },
  { key: 6, label: 'Removed' },
  { key: 7, label: 'Recommended' }
]
export const SCOPE_TYPE = {
  ORIGINAL: { value: 1, label: 'Original' },
  CHANGE_REQUEST: { value: 2, label: 'Change Request' },
}

export const SCOPE_TYPE_LIST = [
  {
    id: SCOPE_TYPE.ORIGINAL.value,
    name: SCOPE_TYPE.ORIGINAL.label,
  },
  {
    id: SCOPE_TYPE.CHANGE_REQUEST.value,
    name: SCOPE_TYPE.CHANGE_REQUEST.label,
  }
]

export const MEETING_SCOPE_TYPE_LIST = [
  {
    id: SCOPE_TYPE.CHANGE_REQUEST.value,
    name: SCOPE_TYPE.CHANGE_REQUEST.label,
  }
]

export const COVERAGE_TYPE = {
  COVERED: { value: 1, label: 'Covered USR' },
  UNCOVERED: { value: 2, label: 'Uncovered USR' },
}

export const DEFAULT_PAGE_SIZE = 20

export const RESPONSE_STATUS_CODE = {
  OK: 200,
  BAD_REQUESRL: 400,
  SERVER_ERROR: 500,
}

export const APP_ROLES = {
  BA: 'BA',
  PM: 'PM',
  TESTER: 'Tester',
  DEV: 'Dev',
  QA: 'QA',
  CUSTOMER: 'Customer',
  SYSTEM: 'SystemAdmin',
  BA_LEAD: 'BA Lead'
}
export const APP_COMMON_ROLES = {
  BA_MEMBER: 1,
  REVIEWER: 2,
  SYSTEM_ADMIN: 3
}

export enum SCREEN_MODE {
  VIEW = 1,
  EDIT = 2,
  CREATE = 3,
  HISTORY = 4,
  COMPARE = 5,
  VERSION = 6,
}

export enum BUTTON_TYPE {
  ICON = 1,
  TEXT = 2,
}

export enum STATUS {
  DRAFT = 0,
  SUBMITTED = 1,
  CANCELLED = 2,
  CHECK_IN = 3,
  DELETE = 4,
  ENDORSE = 6,
  REJECT = 5,
  APPROVE = 7,
  REJECT_CUSTOMER = 8,
  REJECTED = 9
}
export enum MESSAGE_TYPES {
  CREATE = 0,
  SUBMIT = 1,
  UPDATE = 2,
  APPROVE = 3,
  REJECT = 4,
  ENDORSE = 5,
  CANCEL = 6,
  REMOVE = 7,
  DELETE = 8,
  IMPORT_VALIDATE = 9,
  IMPORT = 10,
  EXPORT = 11,
  SUBMIT_CR = 12,
  VERIFY = 13,
  REOPEN = 14,
  RESTORE = 15,
}
export const PROJECT_STATUS = [
  'On-Going',
  'Tentative',
  'Closed',
  'Cancelled',
  // 'Deleted',
]
export const VALIDATE_TYPE_STATUS = [
  { value: 1, text: 'Error' },
  { value: 2, text: 'Warning' }
]

export const SOURCE_LIST = [
  {
    id: 1,
    name: 'Meeting Minutes',
  },
  {
    id: 2,
    name: 'Reference Document',
  },
  {
    id: 3,
    name: 'Other',
  },
]

export const COMPONENT_SCOPE = [
  {
    id: 0,
    name: 'All Projects',
  },
  {
    id: 1,
    name: 'Current Customer',
  },
]

export const ALL_SCOPE_LIST = [
  {
    id: 1,
    name: 'In-scope',
    type: [
      SCOPE_TYPE.CHANGE_REQUEST.value,
      SCOPE_TYPE.ORIGINAL.value,
    ],
  },
  {
    id: 2,
    name: 'Out-scope',
    type: [
      SCOPE_TYPE.CHANGE_REQUEST.value,
      SCOPE_TYPE.ORIGINAL.value,
    ],
  },
  {
    id: 3,
    name: 'Tentative',
    type: [SCOPE_TYPE.CHANGE_REQUEST.value],
  },
]

export const ISCOVERED = [
  {
    id: true,
    name: 'Yes',
  },
  {
    id: false,
    name: 'No',
  },
]

export const SCOPE_MEETING_LIST = [
  {
    id: 1,
    name: 'In-scope',
  },
  {
    id: 2,
    name: 'Out-scope',
  },
  {
    id: 3,
    name: 'Tentative',
  },
]

export const SCOPE_OTHER_LIST = [
  {
    id: 1,
    name: 'In-scope',
  },
  {
    id: 2,
    name: 'Out-scope',
  },
]

export enum SOURCE_TYPE {
  MEETING = 1,
  REFERENCE_DOCUMENT = 2,
  OTHER = 3,
  COMMON_BUSINESSRULE = 4,
}


export const STATUS_FILTER = [
  {
    text: 'Approved',
    value: STATUS.APPROVE,
  },
  {
    text: 'Cancelled',
    value: STATUS.CANCELLED,
  },
  // {
  //   text: 'Deleted',
  //   value: STATUS.DELETE,
  // },
  {
    text: 'Draft',
    value: STATUS.DRAFT,
  },
  {
    text: 'Endorsed',
    value: STATUS.ENDORSE,
  },
  {
    text: 'Rejected by Reviewer',
    value: STATUS.REJECT,
  },
  {
    text: 'Rejected by Customer',
    value: STATUS.REJECT_CUSTOMER,
  },
  {
    text: 'Submitted',
    value: STATUS.SUBMITTED,
  },

]


export const COMMITTEE_PERMISSION = [
  { value: 1, label: intl.formatMessage({ id: 'common.committee.ba-member' }) },
  { value: 2, label: intl.formatMessage({ id: 'common.committee.reviewer' }) },
  { value: 3, label: intl.formatMessage({ id: 'common.committee.system-admin' }) },
]

export const USER_STORY_PRIORITY = [
  { value: 1, label: intl.formatMessage({ id: 'common.user-story.critical' }) },
  { value: 2, label: intl.formatMessage({ id: 'common.user-story.high' }) },
  { value: 3, label: intl.formatMessage({ id: 'common.user-story.medium' }) },
  { value: 4, label: intl.formatMessage({ id: 'common.user-story.slow' }) },

]

export const COMMITTEE_STATUS = [
  {
    value: 1,
    label: intl.formatMessage({ id: 'common.committee.active' }),
    color: '#00C853',
  },
  {
    value: 0,
    label: intl.formatMessage({ id: 'common.committee.inactive' }),
    color: '#515560',
  },
]

export enum SEARCH_TYPE {
  TEXT = 0,
  DATE = 1,
  CHECK_BOX = 2,
  SINGLE_CHOICE = 3,
  RADIO = 4,
  DROPDOWN_LIST = 5,
  MULTIPLE_CHOICES = 6,
}

export enum ROW_STATUS {
  UNCHANGE = 0,
  CREATE = 1,
  UPDATE = 2,
  DELETE = 0,
}

export enum STATUS_COMMON {
  DRAFT = 0,
  SUBMITTED = 1,
  APPROVED = 2,
  REJECTED = 3,
  REMOVED = 4,
  DELETED = 5,
  RECOMMEND = 6,
}
export const STATUS_COMMON_FILTER = [
  {
    text: 'Draft',
    value: STATUS_COMMON.DRAFT,
  },
  {
    text: 'Submitted',
    value: STATUS_COMMON.SUBMITTED,
  },
  {
    text: 'Approved',
    value: STATUS_COMMON.APPROVED,
  },
  {
    text: 'Rejected',
    value: STATUS_COMMON.REJECTED,
  },
  {
    text: 'Removed',
    value: STATUS_COMMON.REMOVED,
  },
  // {
  //   text: 'Deleted',
  //   value: STATUS_COMMON.DELETED,
  // },
]
export enum SCREEN_UPLOAD {
  REQUIREMENT = 0,
  FUNCTION = 1,
  MESSAGE = 2,
  BUSINESS_RULE = 3,
  CKEDITOR = 4,
  REQUIREMENT_DOCUMENT = 5,
}

export enum ARTERFACT_ID_EMAIL {
  ACTOR = 0,
  OBJECT_PROPERTY = 2,
}

export enum ARTEFACT_ID {
  OBJECT = 0,
  USE_CASE = 1,
  ACTOR = 2,
  SCREEN = 3,
  WORK_FLOW = 4,
  STATE_TRANSITION = 5,
  MESS = 6,
  EMAIL = 7,
  USER_REQUIREMENT = 8,
  BUSINESS_RULE = 9,
  MEETING_MINUTES = 10,
  OTHER_REQUIREMENT = 11,
}

export enum TYPE_REF {
  OBJECT = 1,
  USECASE = 2,
  SCREEN = 3,
}

export enum COMP_TYPE {
  TABLE = 'Table',
}

export enum PRE_CONDITION_TYPE {
  userPermission = 0,
  security = 1,
}

export enum BUSINESS_RULE_TYPE {
  screenDisplayingRules = 0,
  confirmationRules = 1,
  validatingRules = 2,
  creatingRules = 3,
  deletingRules = 4,
  updatingRules = 5,
  sendingEmailNotificationRules = 6,
  sendingMessageNotificationRules = 7,
  loggingInRules = 8,
  loggingOutRules = 9,
}

export const COMP_TYPE_LIST = [
  {
    type: 1,
    value: 'Single line of text',
  },
  {
    type: 2,
    value: 'Multiple line of text',
  },
  {
    type: 3,
    value: 'Rich text',
  },
  {
    type: 4,
    value: 'Single choice dropdown list.',
  },
  {
    type: 5,
    value: 'Single choice dropdown list with Fill in.',
  },
  {
    type: 6,
    value: 'Multiple choices dropdown list.',
  },
  {
    type: 7,
    value: 'Multiple choices dropdown list with Fill in.',
  },
  {
    type: 8,
    value: 'User picker',
  },
  {
    type: 9,
    value: 'Checkbox',
  },
  {
    type: 10,
    value: 'Radio button',
  },
  {
    type: 11,
    value: 'Date time (Date only).',
  },
  {
    type: 12,
    value: 'Date time (Date and time)',
  },
  {
    type: 13,
    value: 'Date time (Time only).',
  },
  {
    type: 14,
    value: 'Number',
  },
  {
    type: 15,
    value: 'Link',
  },
  {
    type: 16,
    value: 'Image',
  },
  {
    type: 17,
    value: 'Attachment',
  },
  {
    type: 18,
    value: 'Label',
  },
  {
    type: 19,
    value: 'Button',
  },
  {
    type: 20,
    value: 'Icon',
  },
  {
    type: 21,
    value: 'Table',
  },
  {
    type: 22,
    value: 'Column',
  },
  {
    type: 23,
    value: 'Component Group',
  },
  {
    type: 24,
    value: 'Custom',
  },
]

export const DEFAULT_DATA_OBJ_PROPERTY = {
  editting: true,
  name: "",
  unique: false,
  mandatory: false,
  maxLength: "",
  description: "",
  sourceObject: null,
  refObjectProperty: null,
  listPropertiesSelect: [],
  refObjectPropertyName: ""
}

export const REFERENCE_IMPACT = {
  FROM: 1,
  TO: 2
}

export const DISCUSSION_TYPE = [
  {
    id: 1,
    name: 'Follow-up Action',
  },
  {
    id: 2,
    name: 'Discussion',
  },
  // {
  //   id: 3,
  //   name: 'Other',
  // },
]
export const DURATION_SPRINT = [
  {
    id: 1,
    values: 'Custom'
  },
  {
    id: 2,
    values: '1 Week'
  },
  {
    id: 3,
    values: '2 Week'
  },
  {
    id: 4,
    values: '3 Week'
  },
  {
    id: 5,
    values: '4 Week'
  },
]
export const DATE_FORMAT = 'DD/MM/YYYY'
export const DATETIME_FORMAT = 'DD/MM/YYYY HH:mm'
export const PROJECT_PREFIX = '/PRJ/'
export const COMMON_PREFIX = '/common/'

const baseUrl = process.env.REACT_APP_API_BACKEND
const aiBaseUrl = process.env.REACT_APP_API_AI
const baseUrlImportFunction = process.env.REACT_APP_API_TEMPLATE_USECASE
const baseUrlImportUserRequirement = process.env.REACT_APP_API_TEMPLATE_USERREQUIREMENT

export const API_URLS = {
  AUTH: `${baseUrl}Auth/Me`,
  MEETING_MINUTES: `${baseUrl}MeetingMinutes`,
  USER_REQUIREMENTS: `${baseUrl}UserRequirements`,
  REFERENCE_DOCUMENT: `${baseUrl}ReferenceDocuments`,
  OBJECT_RELATIONSHIP_DIAGRAM: `${baseUrl}ordiagrams`,
  OBJECT: `${baseUrl}Objects`,
  HISTORY: `${baseUrl}artefacts`,
  GLOSSARY: `${baseUrl}glossaries`,
  ACTORS: `${baseUrl}Actors`,
  SCREENS: `${baseUrl}Screens`,
  MESSAGES: `${baseUrl}Messages`,
  EMAILS: `${baseUrl}Emailtemplates`,
  DATA_MIGRATIONS: `${baseUrl}DataMigrations`,
  WORKFLOWS: `${baseUrl}Workflows`,
  STATE_TRANSITIONS: `${baseUrl}Statetransitions`,
  USE_CASE_DIAGRAMS: `${baseUrl}Ucdiagrams`,
  USE_CASE: `${baseUrl}Functions`,
  NON_FUNCTIONS: `${baseUrl}Nonfunctions`,
  OTHER_REQS: `${baseUrl}Otherrequirements`,
  BUSINESS_RULES: `${baseUrl}Commonbusinessrules`,
  UPLOAD_FILE: '',
  UPDATE_VERSION: `${baseUrl}artefacts/version`,
  RESTORE_VERSION_HISTORY: `${baseUrl}artefacts`,
  REFERENCES_OBJECTS: `${baseUrl}references/objects`,
  REFERENCES_SCREENS: `${baseUrl}references/screens`,
  REFERENCES_ACTORS: `${baseUrl}references/actors`,
  REFERENCES_FUNCTIONS: `${baseUrl}references/functions`,
  REFERENCES_MESSAGES: `${baseUrl}references/messages`,
  REFERENCES_EMAIL_TEMPLATES: `${baseUrl}references/emailtemplates`,
  REFERENCES_USER_REQUIREMENTS: `${baseUrl}references/userrequirements`,
  REFERENCES_OTHER_REQUIREMENTS: `${baseUrl}references/otherrequirements`,
  REFERENCES_BUSINESS_RULES: `${baseUrl}references/commonbusinessrules`,
  REFERENCES_MEETING: `${baseUrl}references/meetingminutes`,
  REFERENCES_DOCUMENT: `${baseUrl}references/referencedocuments`,
  REFERENCES_MEMBERS: `${baseUrl}references/members`,
  REFERENCES_WORKFLOWS: `${baseUrl}references/workflows`,
  REFERENCES_STATE_TRANSITIONS: `${baseUrl}references/statetransitions`,
  REFERENCES_NON_FUNCTIONAL_REQUIREMENT: `${baseUrl}references/nonfunctions`,
  REFERENCES_DATA_MIGRATION: `${baseUrl}references/datamigrations`,
  REFERENCES_OBJECT_RELATIONSHIP_DIAGRAM: `${baseUrl}references/ordiagrams`,
  REFERENCES_USECASE_DIAGRAM: `${baseUrl}references/ucdiagrams`,
  REFERENCES_USER_STORY: `${baseUrl}references/userstories`,

  UPDATE_COMMENT: `${baseUrl}comment`,
  ADD_UPDATE_DELETE_REPLY: `${baseUrl}comment/reply`,
  CHANG_ASSIGN_TASK: `${baseUrl}artefacts/assign`,
  EXPORT_COMMENT: `${baseUrl}comment/export`,

  GENERATE_SRS: `${baseUrl}srs/Generate`,
  DOWNLOAD_SRS: `${baseUrl}projects/${extractProjectCode()}/srs/Download`,
  VALIDATE_SRS: `${baseUrl}srs/Validate`,
  MOCKUP_SCREEN: `${baseUrl}screens`,
  SELECT_COMMON_COMPONENT: `${baseUrl}components`,
  IMPORT_FILE_USECASE: `${baseUrl}Functions/import`,
  IMPORT_FILE_UR: `${baseUrl}userrequirements/import`,
  IMPORT_FILE_US: `${baseUrl}userstories/import`,
  MENTIONS: `${baseUrl}Mentions`,
  PERMISSION_MATRIX: `${baseUrl}permissionmatrix`,
  MY_ASSIGNED_TASK: `${baseUrl}mydashboard/AssignedTasks`,
  MY_PENDING_REVIEW: `${baseUrl}mydashboard/PendingReviewTasks`,
  MY_ASSIGNED_TASK_STATISTICS: `${baseUrl}mydashboard/AssignedTasksStatistics`,
  ASSIGN_TASK: `${baseUrl}mydashboard/AssignTask`,
  RECOMMEND_COMMON_COMPONENT: `${baseUrl}components/recommend`,
  QUALITY_REPORT_TOTAL_COMMENT: `${baseUrl}mydashboard/qualityReport`,
  QUALITY_REPORT_EXPORT: `${baseUrl}mydashboard/qualityReport/Export`,
  RECOMMEND_COMMON_REFERENCES_ARTEFACT: `${baseUrl}references/artefacts`,
  IMPORT_USECASE: `${baseUrlImportFunction}`,
  IMPORT_USERREQUIREMENT: `${baseUrlImportUserRequirement}`,
  IMPORT_FILE_VALIDATE_USECASE: `${baseUrl}Functions/import/validate`,
  IMPORT_FILE_VALIDATE_UR: `${baseUrl}userrequirements/import/validate`,
  IMPORT_FILE_VALIDATE_US: `${baseUrl}userstories/import/validate`,
  CONFIGURATION: `${baseUrl}projects/${extractProjectCode()}/configuration`,
  ATTACHMENTS: `${baseUrl}Attachments`,
  GET_SCOPE_COVERAGE: `${baseUrl}Dashboard/ScopeCoverage`,
  GET_SCOPE_CHANGE: `${baseUrl}Dashboard/ScopeChange?number=`,
  DOWNLOAD_IMPORT_TEMPLATE_UR: `${baseUrl}projects/${extractProjectCode()}/importtemplates/userrequirement`,
  DOWNLOAD_IMPORT_TEMPLATE_UC: `${baseUrl}projects/${extractProjectCode()}/importtemplates/function`,
  DOWNLOAD_IMPORT_TEMPLATE_US: `${baseUrl}projects/${extractProjectCode()}/importtemplates/userstory`,
  GET_LIST_GENERATE_SRS: `${baseUrl}srs`,
  GET_REFERENCES: `${baseUrl}references`,
  REFERENCES_ARTEFACT_MISSING: `${baseUrl}References/Artefacts/Missing`,

  // AI Assistant API URLs
  AI_HEALTH: `${aiBaseUrl}/health`,
  AI_AGENTS: `${aiBaseUrl}/agents`,
  AGENT_CONFIG: (agentCode: string) => `${aiBaseUrl}/agents/${agentCode}`,
  AI_CONVERSATIONS: `${aiBaseUrl}/conversations`,
  AI_CONVERSATION_MESSAGES: (conversationId: string) => `${aiBaseUrl}/conversations/${conversationId}/messages`,
  AI_DELETE_CONVERSATION: (conversationId: string) => `${aiBaseUrl}/conversations/${conversationId}`,
  EPIC_MANAGEMENT: `${baseUrl}epics`,
  SPRINTS_MANAGEMENT: `${baseUrl}Sprints`,
  REFERENCE_IMPACT_FROM: `${baseUrl}references/artefacts/referencefrom`,
  REFERENCE_IMPACT: `${baseUrl}references/artefacts/reference`,
  REFERENCE_TO: `${baseUrl}references/artefacts/referenceto`,
  USER_STORY_MANAGEMENT: `${baseUrl}userstories`,
  REFERENCES_EPIC: `${baseUrl}references/epics/`,
  REFERENCES_SPRINT: `${baseUrl}references/sprints/`,
  REFERENCES_PRODUCT: `${baseUrl}references/products/`,
  RECOMMENDED_COMMON_REQ: `${baseUrl}components/recommended`,


  REFERENCES_EPIC_USER_STORY: `${baseUrl}references/epicsUserStory`,
  REFERENCES_SPRINT_USER_STORY: `${baseUrl}references/sprintsUserStory`,
  REFERENCES_PRODUCT_USER_STORY: `${baseUrl}references/productsUserStory`,

  ADMIN_PROJECT: `${baseUrl}admin/projects`,
  COMMON_HISTORY: `${baseUrl}common/artefacts`,
  COMMON_RESTORE_VERSION_HISTORY: `${baseUrl}common/artefacts`,
  COMMON_COMPONENT: `${baseUrl}common/Components`,
  COMMON_COMMITTEE: `${baseUrl}common/Committees`,
  COMMON_USECASE: `${baseUrl}common/UseCases`,
  COMMON_SCREEN: `${baseUrl}common/Screens`,
  COMMON_OBJECT: `${baseUrl}common/Objects`,
  COMMON_MESSAGE: `${baseUrl}common/Messages`,
  COMMON_WORKFLOW: `${baseUrl}common/Workflows`,
  COMMON_NONFUNCTIONAL_REQUIREMENT: `${baseUrl}common/NonFunctionRequirements`,
  COMMON_BUSINESS_RULE: `${baseUrl}common/Commonbusinessrule`,
  COMMON_EMAIL: `${baseUrl}common/emailtemplates`,
  COMMON_REFERENCE_OBJECTS: `${baseUrl}common/References/Objects`,
  COMMON_REFERENCE_SCREENS: `${baseUrl}common/References/Screens`,
  COMMON_REFERENCE_REF_OBJECTS: `${baseUrl}common/References/Objects?status=${STATUS_COMMON.APPROVED}&status=${STATUS_COMMON.DRAFT}&status=${STATUS_COMMON.REJECTED}&status=${STATUS_COMMON.SUBMITTED}&status=${STATUS_COMMON.RECOMMEND}`,
  COMMON_REFERENCE_USECASE: `${baseUrl}common/References/UseCases?status=${STATUS_COMMON.APPROVED}&status=${STATUS_COMMON.DRAFT}&status=${STATUS_COMMON.REJECTED}&status=${STATUS_COMMON.SUBMITTED}`,
  COMMON_REFERENCE_ALL_USECASE: `${baseUrl}common/References/UseCases`,
  COMMON_REFERENCE_WORKFLOW: `${baseUrl}common/References/Workflows`,

  COMMON_REFERENCES_APPROVED_OBJECTS: `${baseUrl}common/References/Objects?status=${STATUS_COMMON.APPROVED}`,
  COMMON_REFERENCES_ARTEFACT: `${baseUrl}common/References/Artefacts`,
  COMMON_ATTACHMENTS: `${baseUrl}common/Attachments`,
  COMMON_MENTIONS: `${baseUrl}common/Mentions`,
  COMMON_REFERENCES_ARTEFACT_MISSING: `${baseUrl}common/References/Artefacts/Missing`,

  COMMON_REFERENCE_COMPONENTS: `${baseUrl}common/References/Components`,
  COMMON_REFERENCE_CBR: `${baseUrl}common/References/CommonBusinessRules`,
  COMMON_REFERENCE_EMAIL_TEMPLATES: `${baseUrl}common/References/EmailTemplates`,
  COMMON_REFERENCE_MESSAGES: `${baseUrl}common/References/Messages`,
  COMMON_REFERENCE_NON_FUNCTIONAL_REQUIREMENTS: `${baseUrl}common/References/NonFunctionRequirements`,
  COMMON_REFERENCE_TO: `${baseUrl}common/References/Artefacts/ReferenceTo`,

  SYNC_DATA_MART: `${baseUrl}admin/quartz/run`,
  COMPONENTS_JIRA: `${baseUrl}jira_components`,

  /**
   * AI module
   */
}


export const APP_ROUTES = {
  DASHBOARD: '/dashboard',
  OTHER_REQUIREMENT: '/other-requirements',
  OTHER_REQUIREMENT_DETAIL: '/other-requirement/',
  OBJECT_RELATIONSHIP: '/object-relationship-diagrams',
  OBJECT_RELATIONSHIP_DETAIL: '/object-relationship-diagram/',
  MEETING: '/meeting-minutes',
  MEETING_DETAIL: '/meeting-minute/',
  USER_REQUIREMENT: '/user-requirements',
  USER_REQUIREMENT_DETAIL: '/user-requirement/',
  REFERENCE_DOCUMENT: '/reference-documents',
  REFERENCE_DOCUMENT_DETAIL: '/reference-document/',
  OBJECT: '/objects',
  OBJECT_DETAIL: '/object/',
  ACTOR: '/actors',
  ACTOR_DETAIL: '/actor/',
  WORKFLOW: '/workflows',
  WORKFLOW_DETAIL: '/workflow/',
  STATE_TRANSITION: '/state-transitions',
  STATE_TRANSITION_DETAIL: '/state-transition/',
  USECASE: '/usecases',
  USECASE_CREATE: '/usecases/create',
  USECASE_DETAIL: '/usecase/',
  COMMON_BUSINESS_RULE: '/business-rules',
  COMMON_BUSINESS_RULE_DETAIL: '/business-rule/',
  SCREEN: '/screens',
  SCREEN_DETAIL: '/screen/',
  NONFUNTIONAL_REQ: '/non-functional-requirements',
  NONFUNTIONAL_REQ_DETAIL: '/non-functional-requirement/',
  OTHER_REQ: '/other-requirements',
  OTHER_REQ_DETAIL: '/other-requirement/',
  DATA_MIGRATION: '/data-migrations',
  DATA_MIGRATION_DETAIL: '/data-migration/',
  MESSAGE: '/messages',
  MESSAGE_DETAIL: '/message/',
  RECOMMENED: '/recommendedcommonreqs',
  RECOMMENED_DETAIL: '/recommendedcommonreq/',
  ADMIN_PROJECT: '/admin/projects',
  ADMIN_SUPERVISOR_AGENT: '/admin/supervisor-agent',
  ADMIN_WORKER_AGENT: '/admin/worker-agent',
  MAIL: '/email-templates',
  GLOSSARY: '/glossary',
  MAIL_DETAIL: '/email-template/',
  USECASE_DIAGRAM: '/use-case-diagrams',
  USECASE_DIAGRAM_DETAIL: '/use-case-diagram/',
  PROJECTS: '/projects',
  PROJECT_DETAIL: '/project/',
  PERMISSION_MATRIX: '/permission-matrices',
  REPORT: '/report',
  ACCESS_DENIED: '/403',
  PAGE_NOT_FOUND: '/404',
  MY_ASSIGNED_TASK: '/my-assigned-task',
  REVIEW_TASK: '/review-task',
  QUALITY_REPORT: '/quality-report',
  GENERATE_SRS: '/generate-srs',
  VALIDATION_RESULT: '/validation-result',
  SPRINT_MANAGEMENT: '/sprint-managements',
  SPRINT_MANAGEMENT_DETAIL: '/sprint-management/',

  COMMON_COMMITTEE: COMMON_PREFIX + 'committee',
  COMMON_COMPONENT: COMMON_PREFIX + 'components',
  COMMON_CBR: COMMON_PREFIX + 'common-businessrules',
  COMMON_CBR_DETAIL: COMMON_PREFIX + 'common-businessrule/',
  COMMON_NONFUNCTIONAL: COMMON_PREFIX + 'common-nonfunctionals',
  COMMON_NONFUNCTIONAL_DETAIL: COMMON_PREFIX + 'common-nonfunctional/',
  COMMON_MESSAGE: COMMON_PREFIX + 'common-messages',
  COMMON_MESSAGE_DETAIL: COMMON_PREFIX + 'common-message/',
  COMMON_EMAIL: COMMON_PREFIX + 'common-emailtemplates',
  COMMON_EMAIL_DETAIL: COMMON_PREFIX + 'common-emailtemplate/',
  COMMON_COMPONENT_DETAIL: COMMON_PREFIX + 'component/',
  COMMON_USECASE: COMMON_PREFIX + 'usecases',
  COMMON_USECASE_DETAIL: COMMON_PREFIX + 'usecase/',
  COMMON_OBJECT: COMMON_PREFIX + 'common-objects',
  COMMON_OBJECT_DETAIL: COMMON_PREFIX + 'common-object/',
  COMMON_SCREEN: COMMON_PREFIX + 'common-screens',
  COMMON_SCREEN_DETAIL: COMMON_PREFIX + 'common-screen/',
  COMMON_WORKFLOW: COMMON_PREFIX + 'common-workflows',
  COMMON_WORKFLOW_DETAIL: COMMON_PREFIX + 'common-workflow/',
  COMMON_BUNDLE: COMMON_PREFIX + 'bundle',
  EPIC_MANAGEMENT: '/epic-managements',
  EPIC_MANAGEMENT_DETAIL: '/epic-management/',
  USER_STORY_MANAGEMENT: '/user-story-managements',
  USER_STORY_MANAGEMENT_DETAIL: '/user-story-management/',
}

export const VALIDATE_ERROR = [
  {
    value: 1,
    text: 'Actor without linking to any Use Cases.'
  },
  {
    value: 2,
    text: 'There is no Actor in the system.'
  },
  {
    value: 3,
    text: 'There is no Object in the system.'
  },
  {
    value: 4,
    text: 'Object without linking to any Use Cases.'
  },
  {
    value: 5,
    text: 'There is no Use Case in the system.'
  },
  {
    value: 6,
    text: 'User Requirement without any artefact referenced to it.'
  },
  {
    value: 7,
    text: 'Email template without any artefact referenced to it.'
  },
  {
    value: 8,
    text: 'There is no Message in the system.'
  },
  {
    value: 9,
    text: 'Message without artefact referenced to it.'
  },
]
export const VALIDATE_WARNING = [
  {
    value: 1,
    text: 'There is no Common Business Rule in the system.'
  },
  {
    value: 2,
    text: 'Common Business Rule without any artefact referenced to it.'
  },
  {
    value: 3,
    text: 'There is no Data Migration in the system.'
  },
  {
    value: 4,
    text: 'There is no Email Template in the system.'
  },
  {
    value: 5,
    text: 'There is no Meeting Minutes in the system.'
  },
  {
    value: 6,
    text: 'There is no Non-Functional Requirement in the system.'
  },
  {
    value: 7,
    text: 'Object without any CRUD Use Cases.'
  },
  {
    value: 8,
    text: 'There is no Object Relationship Diagram in the system.'
  },
  {
    value: 9,
    text: 'There is no Other Requirements in the system.'
  },
  {
    value: 10,
    text: 'There is no Reference Document in the system.'
  },
  {
    value: 11,
    text: 'There is no Screen in the system.'
  },
  {
    value: 12,
    text: 'There is no State Transition in the system.'
  },
  {
    value: 13,
    text: 'There is no Login Use Case in the system.'
  },
  {
    value: 14,
    text: 'There is no Logout Use Case in the system.'
  },
  {
    value: 15,
    text: 'Use Case without linking to any Screens.'
  },
  {
    value: 16,
    text: 'There is no Use Case Diagram  in the system.'
  },
  {
    value: 17,
    text: 'There is no User Requirement in the system.'
  },
  {
    value: 18,
    text: 'There is no Workflow in the system.'
  },

]
export const REQ_ARTEFACT_TYPE_ID = {
  ACTOR: 1,
  COMMON_BUSINESS_RULE: 2,
  DATA_MIGRATION: 3,
  EMAIL_TEMPLATE: 4,
  MEETING_MINUTE: 5,
  MESSAGE: 6,
  NON_FUNCTIONAL_REQUIREMENT: 7,
  OBJECT: 8,
  OBJECT_RELATIONSHIP_DIAGRAM: 9,
  OTHER_REQUIREMENT: 10,
  REFERENCE_DOCUMENT: 11,
  SCREEN: 12,
  STATE_TRANSITION: 13,
  USECASE: 14,
  USECASE_DIAGRAM: 15,
  USER_REQUIREMENT: 16,
  WORKFLOW: 17,
  PERMISSION_MATRIX: 18,
  USER_STORY: 19,
  EPIC: 20,
  SPRINT: 21,
  MY_ASSIGNED_TASK: 22,
  MY_PENDING_REVIEW_TASK: 26,
  QUALITY_REPORT: 24,
  RECOMMENDED_COMMON_REQ: 25,
  GLOSSARY: 23,
  ADMIN_PROJECT: 40,
}
export const COM_ARTEFACT_TYPE_ID = {
  OBJECT: 1,
  SCREEN: 2,
  USECASE: 3,
  COMMON_BUSINESS_RULE: 4,
  NON_FUNCTIONAL_REQUIREMENT: 5,
  EMAIL_TEMPLATE: 6,
  MESSAGE: 7,
  COMPONENT: 8,
  WORKFLOW: 9,
  COMMITTEE: 40,
}
export const ARTEFACT_NAME = [
  {
    key: REQ_ARTEFACT_TYPE_ID.ACTOR,
    value: 'Actor',
    details: APP_ROUTES.ACTOR_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE,
    value: 'Common Business Rule',
    details: APP_ROUTES.COMMON_BUSINESS_RULE_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.DATA_MIGRATION,
    value: 'Data Migration',
    details: APP_ROUTES.DATA_MIGRATION_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT,
    value: 'Non-Functional Requirement',
    details: APP_ROUTES.NONFUNTIONAL_REQ_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.OBJECT,
    value: 'Object',
    details: APP_ROUTES.OBJECT_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.OBJECT_RELATIONSHIP_DIAGRAM,
    value: 'Object Relationship Diagram',
    details: APP_ROUTES.OBJECT_RELATIONSHIP_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.OTHER_REQUIREMENT,
    value: 'Other Requirement',
    details: APP_ROUTES.OTHER_REQUIREMENT_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX,
    value: 'Permission Matrix',
    details: APP_ROUTES.PERMISSION_MATRIX
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.SCREEN,
    value: 'Screen',
    details: APP_ROUTES.SCREEN_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION,
    value: 'State Transition',
    details: APP_ROUTES.STATE_TRANSITION_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.USECASE,
    value: 'Use Case',
    details: APP_ROUTES.USECASE_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.USECASE_DIAGRAM,
    value: 'Use Case Diagram',
    details: APP_ROUTES.USECASE_DIAGRAM_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.WORKFLOW,
    value: 'Workflow',
    details: APP_ROUTES.WORKFLOW_DETAIL,
  },
  // {
  //   key: 14,
  //   value: 'Epic Management',
  //   details: APP_ROUTES.EPIC_MANAGEMENT,
  // },
  // {
  //   key: 15,
  //   value: 'Sprint Management',
  //   details: APP_ROUTES.SPRINT_MANAGEMENT_DETAIL,
  // },
  {
    key: REQ_ARTEFACT_TYPE_ID.USER_STORY,
    value: 'User Story Management',
    details: APP_ROUTES.USER_STORY_MANAGEMENT_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT,
    value: 'User Requirement',
    details: APP_ROUTES.USER_REQUIREMENT_DETAIL,
  },
]

export const ARTEFACT_NAME_GENERATE = [
  {
    key: REQ_ARTEFACT_TYPE_ID.OBJECT_RELATIONSHIP_DIAGRAM,
    value: 'Object Relationship Diagram',
    details: APP_ROUTES.OBJECT_RELATIONSHIP_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.WORKFLOW,
    value: 'Workflow',
    details: APP_ROUTES.WORKFLOW_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION,
    value: 'State Transition',
    details: APP_ROUTES.STATE_TRANSITION_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.USECASE_DIAGRAM,
    value: 'Use Case Diagram',
    details: APP_ROUTES.USECASE_DIAGRAM_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX,
    value: 'Permission Matrix',
    details: APP_ROUTES.PERMISSION_MATRIX
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.USECASE,
    value: 'Use Case',
    details: APP_ROUTES.USECASE_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.SCREEN,
    value: 'Screen',
    details: APP_ROUTES.SCREEN_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT,
    value: 'Non-Functional Requirement',
    details: APP_ROUTES.NONFUNTIONAL_REQ_DETAIL,
  },

  {
    key: REQ_ARTEFACT_TYPE_ID.OTHER_REQUIREMENT,
    value: 'Other Requirement',
    details: APP_ROUTES.OTHER_REQUIREMENT_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.DATA_MIGRATION,
    value: 'Data Migration',
    details: APP_ROUTES.DATA_MIGRATION_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.OBJECT,
    value: 'Object',
    details: APP_ROUTES.OBJECT_DETAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.MESSAGE,
    value: 'Message',
    details: APP_ROUTES.MESSAGE,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE,
    value: 'Email Template',
    details: APP_ROUTES.MAIL,
  },
  {
    key: REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE,
    value: 'Common Business Rule',
    details: APP_ROUTES.COMMON_BUSINESS_RULE,
  },
]


export const VALIDATE_ARTEFACT_TYPE = [
  { value: REQ_ARTEFACT_TYPE_ID.ACTOR, text: 'Actor', url: APP_ROUTES.ACTOR_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE, text: 'Common Business Rule', url: APP_ROUTES.COMMON_BUSINESS_RULE_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.DATA_MIGRATION, text: 'Data Migration', url: APP_ROUTES.DATA_MIGRATION_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE, text: 'Email Template', url: APP_ROUTES.MAIL_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.MEETING_MINUTE, text: 'Meeting Minute', url: APP_ROUTES.MEETING_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.MESSAGE, text: 'Message', url: APP_ROUTES.MESSAGE_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT, text: 'Non-Functional Requirement', url: APP_ROUTES.NONFUNTIONAL_REQ_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.OBJECT, text: 'Object', url: APP_ROUTES.OBJECT_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.OBJECT_RELATIONSHIP_DIAGRAM, text: 'Object Relationship Diagram', url: APP_ROUTES.OBJECT_RELATIONSHIP_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.OTHER_REQUIREMENT, text: 'Other Requirement', url: APP_ROUTES.OTHER_REQUIREMENT_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.REFERENCE_DOCUMENT, text: 'Reference Document', url: APP_ROUTES.REFERENCE_DOCUMENT_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.SCREEN, text: 'Screen', url: APP_ROUTES.SCREEN_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION, text: 'State Transition', url: APP_ROUTES.STATE_TRANSITION_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.USECASE, text: 'Use Case', url: APP_ROUTES.USECASE_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.USECASE_DIAGRAM, text: 'Use Case Diagram', url: APP_ROUTES.USECASE_DIAGRAM_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT, text: 'User Requirement', url: APP_ROUTES.USER_REQUIREMENT_DETAIL },
  { value: REQ_ARTEFACT_TYPE_ID.WORKFLOW, text: 'Workflow', url: APP_ROUTES.WORKFLOW_DETAIL },
]

export const COM_VALIDATE_ARTEFACT_TYPE = [
  { value: COM_ARTEFACT_TYPE_ID.OBJECT, text: 'Object', url: APP_ROUTES.COMMON_OBJECT_DETAIL },
  { value: COM_ARTEFACT_TYPE_ID.SCREEN, text: 'Screen', url: APP_ROUTES.COMMON_SCREEN_DETAIL },
  { value: COM_ARTEFACT_TYPE_ID.USECASE, text: 'Use Case', url: APP_ROUTES.COMMON_USECASE_DETAIL },
  { value: COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE, text: 'Common Business Rule', url: APP_ROUTES.COMMON_CBR_DETAIL },
  { value: COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT, text: 'Non-Functional Requirement', url: APP_ROUTES.COMMON_NONFUNCTIONAL_DETAIL },
  { value: COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE, text: 'Email Template', url: APP_ROUTES.COMMON_EMAIL_DETAIL },
  { value: COM_ARTEFACT_TYPE_ID.MESSAGE, text: 'Message', url: APP_ROUTES.COMMON_MESSAGE_DETAIL },
  { value: COM_ARTEFACT_TYPE_ID.COMPONENT, text: 'Component', url: APP_ROUTES.COMMON_COMPONENT_DETAIL }
]
export const MESSAGE_FUNCTION = {
  CREATE_SUCCESS: 'common.message.create-success',
  UPDATE_SUCCESS: 'common.message.update-success',
  RESTORE_SUCCESS: 'common.message.restore-success',
  DELETE_SUCCESS: 'common.message.delete-success',
  SUBMIT_SUCCESS: 'common.message.submit-success',
  APPROVE_SUCCESS: 'common.message.approve-success',
  REJECT_SUCCESS: 'common.message.reject-success',
  CANCEL_SUCCESS: 'common.message.cancel-success',
  REMOVE_SUCCESS: 'common.message.remove-success',
  IMPORT_SUCCESS: 'common.message.import-success',
  EXPORT_SUCCESS: 'common.message.export-success',
  IMPORT_VALIDATE_SUCCESS: 'common.message.import-validate-success',
  ENDORSE_SUCCESS: 'common.message.endorse-success',
  ERROR: 'common.message.error-description',
  SUBMITTED_CR_SUCCESS: 'common.message.submitted-cr-success',
  REOPEN_SUCCESS: 'common.message.reopen-success',
}
export const MESSAGE_TYPE = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning'
}
export const IMAGE_MAX_SIZE = 1024 * 1024 * 5
export enum COMMENT_CATEGORY {
  BUG = 'Bug',
  QA = 'Q&A',
  SUGGESTION = 'Suggestion',
}

export enum COMMENT_STATUS {
  OPEN = 'Open',
  RESOLVED = 'Resolved',
  CLOSED = 'Closed',
  CANCELLED = 'Cancelled',
}

export const ARTEFACT_COMMENT = {
  ACTOR: "actor",
  COMMON_BUSINESS_RULE: "common-business-rule",
  DATA_MIGRATION_REQUIREMENT: "data-migration-requirement",
  NON_FUNCTION_REQUIREMENT: "non-functional-requirement",
  OBJECT: "object",
  OBJECT_RELATIONSHIP_DIAGRAM: "object-relationship-diagram",
  OTHER_REQUIREMENT: "other-requirement",
  PERMISSION_MATRIX: "permission-matrix",
  SCREEN: "screen",
  STATE_TRASITION: "state-transition",
  USE_CASE: "use-case",
  USE_CASE_DIAGRAM: "use-case-diagram",
  MEETING_MINUTES: "meeting-minutes",
  USER_REQUIREMENTS: "user-requirements",
  REFERENCE_DOCUMENTS: "reference-documents",
  MESSAGE: "message",
  EMAIL_TEMPLATE: "email-templates",
  COMMON_OBJECT: "common-object",
  COMMON_USE_CASE: "common-use-case",
  WORKFLOW: "workflow",
  USER_STORY: "user-story",

  COMMON_MESSAGE: "common-message",
  COMMON_EMAIL_TEMPLATE: "common-email-templates",
  COMMON_SCREEN: "common-screen",
  COMMON_NON_FUNCTION_REQUIREMENT: "common-non-functional-requirement",
  COMMON_COMMON_BUSINESS_RULE: "common-common-business-rule",
  COMMON_COMPONENT: "common-component",
}
export const LIST_NFR_CATEGORY = [
  {
    id: 0,
    name: `${intl.formatMessage({
      id: 'nfr.category.performance-requirements',
    })}`,
  },
  {
    id: 1,
    name: `${intl.formatMessage({
      id: 'nfr.category.safety-requirements',
    })}`,
  },
  {
    id: 2,
    name: `${intl.formatMessage({
      id: 'nfr.category.security-requirements',
    })}`,
  },
  {
    id: 3,
    name: `${intl.formatMessage({
      id: 'nfr.category.software-quality-attributes',
    })}`,
  },
]

export const getArtefactTypeURLDetail = (artefactType) => {
  let url = ''

  switch (artefactType) {
    case REQ_ARTEFACT_TYPE_ID.ACTOR:
      url = APP_ROUTES.ACTOR_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE:
      url = APP_ROUTES.COMMON_BUSINESS_RULE_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.DATA_MIGRATION:
      url = APP_ROUTES.DATA_MIGRATION_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE:
      url = APP_ROUTES.MAIL_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.EPIC:
      url = APP_ROUTES.EPIC_MANAGEMENT_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.MEETING_MINUTE:
      url = APP_ROUTES.MEETING_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.MESSAGE:
      url = APP_ROUTES.MESSAGE_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT:
      url = APP_ROUTES.NONFUNTIONAL_REQ_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.OBJECT:
      url = APP_ROUTES.OBJECT_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.OBJECT_RELATIONSHIP_DIAGRAM:
      url = APP_ROUTES.OBJECT_RELATIONSHIP_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.OTHER_REQUIREMENT:
      url = APP_ROUTES.OTHER_REQUIREMENT_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX:
      url = `${APP_ROUTES.PERMISSION_MATRIX}`
      break;
    case REQ_ARTEFACT_TYPE_ID.SCREEN:
      url = APP_ROUTES.SCREEN_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.SPRINT:
      url = APP_ROUTES.SPRINT_MANAGEMENT_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION:
      url = APP_ROUTES.STATE_TRANSITION_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.USECASE:
      url = APP_ROUTES.USECASE_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.USECASE_DIAGRAM:
      url = APP_ROUTES.USECASE_DIAGRAM_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT:
      url = APP_ROUTES.USER_REQUIREMENT_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.USER_STORY:
      url = APP_ROUTES.USER_STORY_MANAGEMENT_DETAIL
      break;
    case REQ_ARTEFACT_TYPE_ID.WORKFLOW:
      url = APP_ROUTES.WORKFLOW_DETAIL
      break;
  }
  return url;
}




export const getPriority = (piority) => {
  let text = ''

  switch (piority) {
    case PIORITY.LOW:
      text = intl.formatMessage({ id: 'user-requirement.piority-low' })
      break;
    case PIORITY.MEDIUM:
      text = intl.formatMessage({ id: 'user-requirement.piority-medium' })
      break;
    case PIORITY.HIGHT:
      text = intl.formatMessage({ id: 'user-requirement.piority-high' })
      break;
    case PIORITY.URGENT:
      text = intl.formatMessage({ id: 'user-requirement.piority-urgent' })
      break;
  }
  return text;
}

export const WINDOW_CONFIRM_MESS = {
  KEY_VI: 'vi-VN',
  MESS_VI: 'Các thay đổi bạn đã thực hiện có thể không được lưu.',
  MESS_DEFAULT: 'Changes you made may not be saved.',
}

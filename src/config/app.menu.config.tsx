import {
  ApartmentOutlined,
  AppstoreAddOutlined,
  AppstoreOutlined,
  <PERSON><PERSON>Outlined,
  <PERSON>lusterOutlined,
  CommentOutlined,
  ContainerOutlined, ControlOutlined, CreditCardOutlined, DeploymentUnitOutlined, DownloadOutlined, FileOutlined, FlagOutlined, FolderOutlined, <PERSON>Outlined, GatewayOutlined, ImportOutlined, IssuesCloseOutlined, LaptopOutlined, MacCommandOutlined, <PERSON>Outlined, MessageOutlined, PullRequestOutlined, ReadOutlined, TeamOutlined, ThunderboltOutlined, RobotOutlined, ToolOutlined
} from '@ant-design/icons'
import { APP_COMMON_ROLES, APP_ROLES, APP_ROUTES } from '../constants'
import CustomSvgIcons from '../helper/component/custom-icons'

interface MenuConfigType {
  label: string,
  isGroup?: boolean,
  path?: string,
  icon?: React.ReactNode | JSX.Element | string,
  chidlren?: MenuConfigType[],
  className?: string,
  isCustom?: boolean,
  checkRole?: any[],
  projectMethodology?: string | null,
  isComon?: boolean,
  isAdmin?: boolean
}
const AppMenuConfig: MenuConfigType[] = [
  {
    label: 'app.menu.input_management',
    icon: <ControlOutlined style={{ fontSize: '20px' }} />,
    isGroup: true,
    chidlren: [
      { label: 'app.menu.meeting_minutes', path: APP_ROUTES.MEETING, icon: <ContainerOutlined style={{ fontSize: '20px' }} /> },
      { label: 'app.menu.user_requirements', path: APP_ROUTES.USER_REQUIREMENT, icon: <ReadOutlined style={{ fontSize: '20px' }} /> },
      { label: 'app.menu.reference_documents', path: APP_ROUTES.REFERENCE_DOCUMENT, icon: <FileOutlined style={{ fontSize: '20px' }} /> }
    ]
  },
  {
    label: 'app.menu.high_level_requirements',
    icon: <BarsOutlined style={{ fontSize: '20px' }} />,
    isGroup: true,
    chidlren: [
      { label: 'app.menu.object_relationship_diagram', path: APP_ROUTES.OBJECT_RELATIONSHIP, icon: <PullRequestOutlined style={{ fontSize: '20px' }} /> },
      { label: 'app.menu.actor_list', path: APP_ROUTES.ACTOR, icon: <CustomSvgIcons className="custom-svg-icon" name="ActorSiderCustomIcon" /> },
      { label: 'app.menu.workflow', path: APP_ROUTES.WORKFLOW, icon: <CustomSvgIcons className="custom-svg-icon" name="WorkflowSiderCustomIcon" /> },
      { label: 'app.menu.state_transition', path: APP_ROUTES.STATE_TRANSITION, icon: <CustomSvgIcons className="custom-svg-icon" name="StateSiderCustomIcon" /> },
      { label: 'app.menu.use_case_diagram', path: APP_ROUTES.USECASE_DIAGRAM, icon: <ForkOutlined style={{ fontSize: '20px' }} /> },
      { label: 'app.menu.permission_matrix', path: APP_ROUTES.PERMISSION_MATRIX, icon: <CustomSvgIcons className="custom-svg-icon" name="PermissionMatrixCustomSiderIcon" /> }
    ]
  },
  {
    label: 'app.menu.functional_requirements',
    icon: <DeploymentUnitOutlined style={{ fontSize: '20px' }} />,
    isGroup: true,
    chidlren: [
      { label: 'app.menu.object_list', path: APP_ROUTES.OBJECT, icon: <CustomSvgIcons className="custom-svg-icon" name="ObjectSiderCustomIcon" /> },
      { label: 'app.menu.use_case_specifications', path: APP_ROUTES.USECASE, icon: <CustomSvgIcons className="custom-svg-icon" name="UsecaseSiderCustomIcon" /> },
      { label: 'app.menu.mockups_screen', path: APP_ROUTES.SCREEN, icon: <CustomSvgIcons className="custom-svg-icon" name="ScreenSiderCustomIcon" /> },
      { label: 'app.menu.common_business_rule', path: APP_ROUTES.COMMON_BUSINESS_RULE, icon: <GatewayOutlined style={{ fontSize: '20px' }} /> }      
    ]
  },
  {
    label: 'app.menu.agile.project_screen',
    icon: <LaptopOutlined style={{ fontSize: '20px' }} />,
    isGroup: true,    
    projectMethodology: 'agile',
    chidlren: [
      { label: 'app.menu.epic', path: APP_ROUTES.EPIC_MANAGEMENT, icon: <IssuesCloseOutlined style={{ fontSize: '20px' }} />, className: 'rq-ms-0' },
      { label: 'app.menu.agile.project_screen.sprint', path: APP_ROUTES.SPRINT_MANAGEMENT, icon: <ThunderboltOutlined style={{ fontSize: '20px' }} /> },
      { label: 'app.menu.agile.user-story', path: APP_ROUTES.USER_STORY_MANAGEMENT, icon: <ReadOutlined style={{ fontSize: '20px' }} /> }
    ]
  },
  { label: 'app.menu.non_functional_requirements', path: APP_ROUTES.NONFUNTIONAL_REQ, icon: <CustomSvgIcons className="custom-svg-icon" name="NFRCustomIcon" /> },
  { label: 'app.menu.other_requirements', path: APP_ROUTES.OTHER_REQUIREMENT, icon: <MacCommandOutlined style={{ fontSize: '20px' }} /> },
  { label: 'app.menu.data_migration', path: APP_ROUTES.DATA_MIGRATION, icon: <CustomSvgIcons className="custom-svg-icon" name="DataMigrationCustomIcon" /> },
  {
    label: 'app.menu.appendices',
    icon: <FolderOutlined style={{ fontSize: '20px' }} />,
    isGroup: true,
    chidlren: [
      { label: 'app.menu.message_list', path: APP_ROUTES.MESSAGE, icon: <MessageOutlined style={{ fontSize: '20px' }} /> },
      { label: 'app.menu.email_templates', path: APP_ROUTES.MAIL, icon: <MailOutlined style={{ fontSize: '20px' }} /> },
      { label: 'app.menu.term', path: APP_ROUTES.GLOSSARY, icon: <CreditCardOutlined style={{ fontSize: '20px' }} /> },
    ]
  },
  // { label: 'app.menu.report', path: APP_ROUTES.REPORT, icon: <PieChartOutlined style={{ fontSize: '20px', marginRight: 10 }} />, className: 'rq-ms-0' },
  {
    label: 'app.menu.utilities',
    icon: <AppstoreAddOutlined style={{ fontSize: '20px' }} />,
    isGroup: true,
    chidlren: [
      { label: 'app.menu.utilities.validate_srs', path: "#validate_srs", icon: <IssuesCloseOutlined style={{ fontSize: '20px' }} />, isCustom: true, checkRole: [APP_ROLES.BA_LEAD, APP_ROLES.PM, APP_ROLES.BA] },
      { label: 'app.menu.utilities.generate_srs', path: "#generate_srs", icon: <DownloadOutlined style={{ fontSize: '20px' }} />, isCustom: true, checkRole: [APP_ROLES.BA_LEAD, APP_ROLES.PM, APP_ROLES.BA] },
      { label: 'app.menu.utilities.select_from_common_requirement', path: "#select_from_common_requirement", icon: <ImportOutlined style={{ fontSize: '20px' }} />, isCustom: true, checkRole: [APP_ROLES.BA, APP_ROLES.PM, APP_ROLES.BA_LEAD] },
      { label: 'app.menu.utilities.recommend_common_requirement', path: APP_ROUTES.RECOMMENED, icon: <FlagOutlined style={{ fontSize: '20px' }} /> , checkRole: [APP_ROLES.BA_LEAD, APP_ROLES.BA]},
      { label: 'app.menu.utilities.export_comments', icon: <CommentOutlined style={{ fontSize: '20px' }} /> , checkRole: [APP_ROLES.BA_LEAD, APP_ROLES.PM], isCustom:true}
    ],
    checkRole: [APP_ROLES.BA_LEAD, APP_ROLES.PM, APP_ROLES.BA]
  },
]

const CommonMenuConfig: MenuConfigType[] = [
  {
    label: 'app.menu.common.common_artefacts',
    icon: <AppstoreOutlined style={{ fontSize: '20px' }} />,
    isGroup: true,
    isComon: true,
    checkRole: [APP_COMMON_ROLES.BA_MEMBER, APP_COMMON_ROLES.REVIEWER, APP_COMMON_ROLES.SYSTEM_ADMIN],
    chidlren: [
      { isComon: true, label: 'app.menu.common.workflow', path: APP_ROUTES.COMMON_WORKFLOW, icon: <CustomSvgIcons className="custom-svg-icon" name="WorkflowSiderCustomIcon" /> },
      { isComon: true, label: 'app.menu.common.object', path: APP_ROUTES.COMMON_OBJECT, icon: <CustomSvgIcons className="custom-svg-icon" name="ObjectSiderCustomIcon" /> },
      { isComon: true, label: 'app.menu.common.use_case', path: APP_ROUTES.COMMON_USECASE, icon: <CustomSvgIcons className="custom-svg-icon" name="UsecaseSiderCustomIcon" /> },
      { isComon: true, label: 'app.menu.common.mockup_screen', path: APP_ROUTES.COMMON_SCREEN, icon: <CustomSvgIcons className="custom-svg-icon" name="ScreenSiderCustomIcon" /> },
      { isComon: true, label: 'app.menu.common.cbr', path: APP_ROUTES.COMMON_CBR, icon: <GatewayOutlined style={{ fontSize: '20px' }} /> },
      { isComon: true, label: 'app.menu.common.nonfunctional', path: APP_ROUTES.COMMON_NONFUNCTIONAL, icon: <CustomSvgIcons className="custom-svg-icon" name="NFRCustomIcon" /> },
      { isComon: true, label: 'app.menu.common.message', path: APP_ROUTES.COMMON_MESSAGE, icon: <MessageOutlined style={{ fontSize: '20px' }} /> },
      { isComon: true, label: 'app.menu.common.emailtemplate', path: APP_ROUTES.COMMON_EMAIL, icon: <MailOutlined style={{ fontSize: '20px' }} /> },
    ],
  },
  // { label: 'app.menu.common.bundle', path: APP_ROUTES.COMMON_BUNDLE, icon: <MacCommandOutlined style={{ fontSize: '20px' }} /> },
  { isComon: true, label: 'app.menu.common.component', path: APP_ROUTES.COMMON_COMPONENT, icon: <ApartmentOutlined style={{ fontSize: '20px' }} />, checkRole: [APP_COMMON_ROLES.BA_MEMBER, APP_COMMON_ROLES.REVIEWER, APP_COMMON_ROLES.SYSTEM_ADMIN] },
  { isComon: true, label: 'app.menu.common.common_req_tool_committee', path: APP_ROUTES.COMMON_COMMITTEE, icon: <TeamOutlined style={{ fontSize: '20px' }} />, checkRole: [APP_COMMON_ROLES.SYSTEM_ADMIN] }
]

const AdminMenuConfig: MenuConfigType[] = [
  {
    label: 'app.menu.admin.admin_artefacts',
    path: APP_ROUTES.ADMIN_PROJECT,
    icon: <AppstoreOutlined style={{ fontSize: '20px' }} />,
    isAdmin: true,
    checkRole: [APP_COMMON_ROLES.SYSTEM_ADMIN],
  },
  {
    label: 'app.menu.admin.agent_settings',
    icon: <RobotOutlined style={{ fontSize: '20px' }}/>,
    isComon: true,
    isGroup: true,

    chidlren:[
      {
        isComon: true,
        label: 'app.menu.admin.supervisor_agent',
        icon: <ClusterOutlined style={{ fontSize: '20px' }}/>,
        path: APP_ROUTES.ADMIN_SUPERVISOR_AGENT,
      },
      {
        isComon: true,
        label: 'app.menu.admin.worker_agent',
        icon: <ClusterOutlined style={{ fontSize: '20px' }}/>,
        path: APP_ROUTES.ADMIN_WORKER_AGENT,
      },
    ]
  },
]

export default { AppMenuConfig, CommonMenuConfig , AdminMenuConfig}


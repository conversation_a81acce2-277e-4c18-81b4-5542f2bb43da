import AppState from '@/store/types';
import { Card, Col, Empty, Row, Select, Space } from 'antd';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import intl from '../../../config/locale.config';
import { getScopeChange, getScopeCoverage } from './action';
import DashboardChangeTable from './components/change-table';
import DashboardCoverageTable from './components/coverage-table';
import DashboardPieChart from './components/pie-chart';
import DashboardStackColumnChart from './components/stack-column-chart';
import { DashboardState } from './type';
import { extractProjectCode } from '../../../helper/share';

const { Option } = Select;

const DashboardPage = () => {
  const dispath = useDispatch();
  const [filterType, setFilterType] = useState(1);
  const state = useSelector<AppState | null>(
    (s) => s?.Dashboard
  ) as DashboardState
  const [scopeCoverageData, setScopeCoverageData] = useState<any>(null);
  const [scopeChange, setScopeChange] = useState<any>(null);

  useEffect(() => {    
    document.title = extractProjectCode() + "-" + intl.formatMessage({ id: 'dashboard.page-title' });
    dispath(getScopeCoverage());
    dispath(getScopeChange(filterType));
  }, [])

  useEffect(() => {
    if (state) {
      setScopeCoverageData(state.scopeCoverage);
      setScopeChange(state.scopeChange );
    }
  }, [state])

  const handleFilterChange = (e) => {
    if (e) {
      setFilterType(e);
      dispath(getScopeChange(e));
    }
  }

  return (
    <div className='dashboard-page'>
      <div className='dashboard-page-title'>{intl.formatMessage({ id: 'dashboard.page-title' })}</div>
      <Space direction="vertical">
        <Row gutter={16} style={{ width: '100%' }}>
          <Col span={12}>
            <Card title={intl.formatMessage({ id: 'dashboard.scope_coverage.title' })} loading={state?.isScopeCoverageLoading}>
              {
                scopeCoverageData ? <>
                  <DashboardPieChart covered={scopeCoverageData.coveredUSR || 0} uncovered={scopeCoverageData.unCoveredUSR || 0} />
                  <DashboardCoverageTable data={scopeCoverageData} />
                </> : <Empty description={intl.formatMessage({ id: 'dashboard.no_data' })} />
              }
            </Card>
          </Col>
          <Col span={12}>
            <Card title={intl.formatMessage({ id: 'dashboard.scope_change.title' })} loading={state?.isScopeChangeLoading}>
              <div className='dashboard-filter'>
                <span>{intl.formatMessage({ id: 'dashboard.filter.period' })}</span>
                <Select value={filterType} onChange={handleFilterChange}>
                  <Option value={1}>{intl.formatMessage({ id: 'dashboard.filter.this_week' })}</Option>
                  <Option value={2}>{intl.formatMessage({ id: 'dashboard.filter.last_week' })}</Option>
                  <Option value={3}>{intl.formatMessage({ id: 'dashboard.filter.this_month' })}</Option>
                  <Option value={4}>{intl.formatMessage({ id: 'dashboard.filter.last_month' })}</Option>
                  <Option value={5}>{intl.formatMessage({ id: 'dashboard.filter.this_quarter' })}</Option>
                  <Option value={6}>{intl.formatMessage({ id: 'dashboard.filter.last_quarter' })}</Option>
                  <Option value={7}>{intl.formatMessage({ id: 'dashboard.filter.previous_six_months' })}</Option>
                  <Option value={8}>{intl.formatMessage({ id: 'dashboard.filter.this_year' })}</Option>
                  <Option value={9}>{intl.formatMessage({ id: 'dashboard.filter.last_year' })}</Option>
                  <Option value={10}>{intl.formatMessage({ id: 'dashboard.filter.from_the_beginning' })}</Option>
                </Select>
              </div>
              {
                scopeChange ?
                  <>
                    <DashboardStackColumnChart data={scopeChange.chart} />
                    <DashboardChangeTable data={scopeChange.data} />
                  </> : <Empty description={intl.formatMessage({ id: 'dashboard.no_data' })} />
              }
            </Card>
          </Col>
        </Row>
      </Space>
    </div>
  )
}

export default DashboardPage

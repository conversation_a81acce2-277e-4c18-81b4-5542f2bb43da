import { Action } from '@reduxjs/toolkit'
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { ShowAppMessage } from '../../helper/share'
import {
  getDetailFailed, getDetailRequest,
  getDetailSuccess, getListFailed, getListRequest,
  getListSuccess,
  updateInstructionsRequest, updateInstructionsSuccess, updateInstructionsFailed
} from './action'
import { Agent, AgentCode } from '../../modules/_shared/ai'
import AIService from '../../services/ai.service'

function* handleGetList(action: Action) {
  if (getListRequest.match(action)) {
    try {
      const data: Agent[] = yield call(AIService.getListAgents);

      yield put(getListSuccess(data.filter((agent: Agent) => agent.code !== AgentCode.Master)));
    } catch (err) {
      console.error('API Error Details:', err); // Debug log
      yield put(getListFailed(null));
      
      // Show a more detailed error message
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      ShowAppMessage(null, 'Error', `Failed to fetch worker agents: ${errorMessage}`);
    }
  }
}

function* handleGetDetail(action: Action) {
  if (getDetailRequest.match(action)) {
    try {
      // Extract agent ID and project ID from payload
      const data = yield call(AIService.getAgentDetails, action.payload);

      yield put(getDetailSuccess(data));
    } catch (err: any) {
      console.error('Failed to fetch agent details:', err); // Debug log
      yield put(getDetailFailed(null));
      ShowAppMessage(err, null, 'Failed to fetch agent details');
    }
  }
}

function* handleUpdateInstructions(action: Action) {
  if (updateInstructionsRequest.match(action)) {
    try {
      const data = yield call(AIService.updateAgentInstruction, action.payload.agentCode, action.payload);
      yield put(updateInstructionsSuccess(data));
      ShowAppMessage(null, 'Success', 'Agent instructions updated successfully');
    } catch (err: any) {
      yield put(updateInstructionsFailed(null));
      ShowAppMessage(err, null, 'Failed to update agent instructions');
    }
  }
}

function* watchGetList() {
  yield takeLatest(getListRequest.type, handleGetList)
}

function* watchGetDetail() {
  yield takeLatest(getDetailRequest.type, handleGetDetail)
}


function* watchUpdateInstructions() {
  yield takeLatest(updateInstructionsRequest.type, handleUpdateInstructions)
}

export default function* adminWorkerAgentSaga() {
  yield all([
    fork(watchGetList),
    fork(watchGetDetail),
    fork(watchUpdateInstructions),
  ])
}

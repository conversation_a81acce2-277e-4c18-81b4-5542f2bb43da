import { Space, Typography, Table, Card } from 'antd'
import moment from 'moment'
import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { DATE_FORMAT, SCREEN_MODE, SEARCH_TYPE } from '../../constants'
import { getColumnSearchProps, renderStatusBadge } from '../../helper/share'
import LavPageHeader from '../../helper/component/lav-breadcumb'
import { getListRequest, resetState } from './action'
import AdminWorkerAgentForm from './form/index'
import './style.css'
import { Agent, AgentCode } from '../../modules/_shared/ai'

const { Text } = Typography

const AdminWorkerAgent = () => {
  const dispatch = useDispatch()
  const adminWorkerAgentState = useSelector(
    (state: any) => state.AdminWorkerAgent
  )
  const { isLoadingList = false, listData = [] } = adminWorkerAgentState || {}

  const [screenMode, setScreenMode] = useState<any>(SCREEN_MODE.VIEW)
  const [agentCode, setAgentCode] = useState('')
  const [selectedAgentId, setSelectedAgentId] = useState('')

  // Auto-load data when component mounts
  useEffect(() => {
    document.title = 'Worker Agents Management'
    dispatch(getListRequest({ project_id: '6864b7e4ecc61ecbe4a146f6' }))
  }, [dispatch])

  // Load data when switching back to VIEW mode
  useEffect(() => {
    if (screenMode === SCREEN_MODE.VIEW) {
      dispatch(getListRequest({ project_id: '6864b7e4ecc61ecbe4a146f6' }))
    }
  }, [screenMode, dispatch])

  // Reset state when component unmounts
  useEffect(() => {
    return () => {
      dispatch(resetState({}))
    }
  }, [dispatch])

  const columns = [
    {
      title: 'Agent Code',
      dataIndex: 'code',
      key: 'code',
      ...getColumnSearchProps('code', SEARCH_TYPE.TEXT),
      render: (text: string, record: any) => {
        return (
          <Text
            className="admin-worker-agent__agent-code"
            onClick={() => {
              setAgentCode(record.code)
              setSelectedAgentId(record.id) // Store the actual agent ID (_id from API)
              setScreenMode(SCREEN_MODE.EDIT)
            }}
          >
            {text}
          </Text>
        )
      },
    },
    {
      title: 'Agent Name',
      dataIndex: 'name',
      key: 'name',
      ...getColumnSearchProps('name', SEARCH_TYPE.TEXT),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ...getColumnSearchProps('description', SEARCH_TYPE.TEXT),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: '100px',
      sorter: true,
      render: (status: number) => {
        // Handle null status as requested
        if (status === null || status === undefined) {
          return <span>-</span>
        }
        return renderStatusBadge(status)
      },
    },
    {
      title: 'Created Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: any) => {
        return text ? moment(text).format(DATE_FORMAT) : ''
      },
    },
    {
      title: 'Last Updated',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (text: any) => {
        return text ? moment(text).format(DATE_FORMAT) : ''
      },
    },
  ]

  const filteredListData =
    listData?.filter((agent: Agent) => agent.code !== AgentCode.Master) ?? []

  return (
    <Space direction="vertical" size="middle" className="full-width p-20px">
      {screenMode === SCREEN_MODE.VIEW ? (
        <>
          <LavPageHeader
            showBreadcumb={false}
            title="Worker Agents Management"
          />
          <Card>
            <Table
              dataSource={filteredListData}
              columns={columns}
              rowKey={(record) =>
                record.id || record.code || Math.random().toString()
              }
              loading={isLoadingList}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} of ${total} items`,
              }}
              scroll={{ x: true }}
              locale={{
                emptyText: isLoadingList ? 'Loading...' : 'No data available',
              }}
            />
          </Card>
        </>
      ) : null}

      {screenMode === SCREEN_MODE.EDIT ? (
        <AdminWorkerAgentForm
          workerAgentId={selectedAgentId} // Pass the actual agent ID (_id)
          screenMode="EDIT"
          onDismiss={() => setScreenMode(SCREEN_MODE.VIEW)}
          onFinish={() => {
            setScreenMode(SCREEN_MODE.VIEW)
            dispatch(getListRequest({ project_id: '6864b7e4ecc61ecbe4a146f6' })) // Refresh data after edit
          }}
          code={agentCode} // Keep the code for display purposes
        />
      ) : null}
    </Space>
  )
}

export default AdminWorkerAgent

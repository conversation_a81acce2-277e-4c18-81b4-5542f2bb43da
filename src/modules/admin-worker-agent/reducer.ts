import { createReducer } from '@reduxjs/toolkit'
import {
  getDetailFailed,
  getDetailRequest,
  getDetailSuccess,
  getListFailed,
  getListRequest,
  getListSuccess,
  resetState,
  updateInstructionsRequest,
  updateInstructionsSuccess,
  updateInstructionsFailed,
} from './action'
import { defaultState, AdminWorkerAgentState } from './type'

const initState: AdminWorkerAgentState = defaultState

const reducer = createReducer(initState, (builder) => {
  return builder
    .addCase(resetState, (state, action?) => {
      Object.assign(state, {
        ...defaultState,
        selectedData: state.selectedData,
        listData: state.listData,
      })
    })

    .addCase(getListRequest, (state, action?) => {
      state.isLoadingList = true
    })
    .addCase(getListSuccess, (state, action) => {
      state.isLoadingList = false
      state.listData = action.payload
    })
    .addCase(getListFailed, (state, action) => {
      state.isLoadingList = false
      state.listData = null
    })

    .addCase(getDetailRequest, (state, action?) => {
      state.isLoading = true
    })
    .addCase(getDetailSuccess, (state, action) => {
      state.isLoading = false
      state.detail = action.payload
    })
    .addCase(getDetailFailed, (state, action) => {
      state.isLoading = false
      state.detail = null
    })
    .addCase(updateInstructionsRequest, (state, action?) => {
      state.isLoading = true
      state.updateInstructionsSuccess = false
    })
    .addCase(updateInstructionsSuccess, (state, action) => {
      state.isLoading = false
      state.updateInstructionsSuccess = true
    })
    .addCase(updateInstructionsFailed, (state, action) => {
      state.isLoading = false
      state.updateInstructionsSuccess = false
    })
})

export type { AdminWorkerAgentState } from './type'
export default reducer

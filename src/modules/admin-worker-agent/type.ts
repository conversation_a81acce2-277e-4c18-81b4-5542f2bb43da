import { Agent } from '../../modules/_shared/ai'

export interface AdminWorkerAgentState {
  isLoading: boolean,
  createSuccess?: boolean,
  updateSuccess?: boolean,
  deleteSuccess?: boolean,
  updateInstructionsSuccess?: boolean,
  listData?: any,
  isLoadingList?: boolean,
  detail?: Agent | null,
  selectedData?: Agent | null,
}

export const defaultState: AdminWorkerAgentState = {
  isLoading: false,
  createSuccess: false,
  updateSuccess: false,
  deleteSuccess: false,
  updateInstructionsSuccess: false,
  listData: null,
  isLoadingList: false,
  detail: null,
  selectedData: null,
}

export enum ActionEnum {
  RESET_STATE = 'admin-worker-agent/RESET_STATE',
  
  GET_LIST_REQUEST = 'admin-worker-agent/GET_LIST_REQUEST',
  GET_LIST_SUCCESS = 'admin-worker-agent/GET_LIST_SUCCESS',
  GET_LIST_FAILED = 'admin-worker-agent/GET_LIST_FAILED',
  
  GET_DETAIL_REQUEST = 'admin-worker-agent/GET_DETAIL_REQUEST',
  GET_DETAIL_SUCCESS = 'admin-worker-agent/GET_DETAIL_SUCCESS',
  GET_DETAIL_FAILED = 'admin-worker-agent/GET_DETAIL_FAILED',
  
  CREATE_REQUEST = 'admin-worker-agent/CREATE_REQUEST',
  CREATE_SUCCESS = 'admin-worker-agent/CREATE_SUCCESS',
  CREATE_FAILED = 'admin-worker-agent/CREATE_FAILED',
  
  UPDATE_REQUEST = 'admin-worker-agent/UPDATE_REQUEST',
  UPDATE_SUCCESS = 'admin-worker-agent/UPDATE_SUCCESS',
  UPDATE_FAILED = 'admin-worker-agent/UPDATE_FAILED',
  
  DELETE_REQUEST = 'admin-worker-agent/DELETE_REQUEST',
  DELETE_SUCCESS = 'admin-worker-agent/DELETE_SUCCESS',
  DELETE_FAILED = 'admin-worker-agent/DELETE_FAILED',
  
  UPDATE_INSTRUCTIONS_REQUEST = 'admin-worker-agent/UPDATE_INSTRUCTIONS_REQUEST',
  UPDATE_INSTRUCTIONS_SUCCESS = 'admin-worker-agent/UPDATE_INSTRUCTIONS_SUCCESS',
  UPDATE_INSTRUCTIONS_FAILED = 'admin-worker-agent/UPDATE_INSTRUCTIONS_FAILED',
}

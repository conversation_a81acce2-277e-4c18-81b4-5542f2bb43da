import React, { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { But<PERSON>, Tabs, Card, message } from 'antd'
import { RobotOutlined, SettingOutlined, CloseOutlined, ExpandOutlined, EyeOutlined, SaveOutlined } from '@ant-design/icons'
import { AIAssistantState } from '../types'
import { toggleAIChatPanel, toggleAISettingsPanel, setActiveTab, closeAllPanels, enterCanvasOnlyMode, exitCanvasOnlyMode, setCurrentEditingMessage, updateMessageContentRequest } from '../actions'
import { AIChatBox } from './chatbox'
import AISettings from './ai-settings'
import { RightMenu } from './right-menu'
import { CanvasEditor } from '../../../../helper/component/tiptap'
import AppState from '../../../../store/types'
import './ai-assistant.less'
import intl from '../../../../config/locale.config'

const { TabPane } = Tabs

type AIAssistantProps = {
  collapsed: boolean
}

export const AIAssistant: React.FC<AIAssistantProps> = ({ collapsed }) => {
  const dispatch = useDispatch()
  const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState
  const [canvasContent, setCanvasContent] = useState(aiState.canvasContent || '')
  const [isSaving, setIsSaving] = useState(false)

  // Sync canvas content with state when it changes
  useEffect(() => {
    setCanvasContent(aiState.canvasContent || '')
  }, [aiState.canvasContent])

  const handleToggleAIChat = () => {
    if (aiState.isCanvasOnlyMode) {
      // Exit canvas-only mode and open AI chat
      dispatch(exitCanvasOnlyMode())
      dispatch(toggleAIChatPanel())
    } else {
      dispatch(toggleAIChatPanel())
    }
  }

  const handleToggleSettings = () => {
    dispatch(toggleAISettingsPanel())
  }

  const handleTabChange = (activeKey: string) => {
    dispatch(setActiveTab(activeKey as 'ai-chat' | 'settings'))
  }

  const handleCloseAll = () => {
    // Clear current editing message when closing all panels
    dispatch(setCurrentEditingMessage(null))
    dispatch(closeAllPanels())
  }

  const handleEnterCanvasOnlyMode = () => {
    // Get the content of the currently editing message
    if (aiState.currentEditingMessage && aiState.currentConversation?.messages) {
      const editingMessage = aiState.currentConversation.messages.find(
        msg => msg.id === aiState.currentEditingMessage
      )
      
      if (editingMessage) {
        const messageContent = editingMessage.content || ''
        setCanvasContent(messageContent)
        dispatch(enterCanvasOnlyMode(messageContent))
      }
    }
  }

  const handleSaveCanvasContent = async () => {
    if (!aiState.currentEditingMessage || !aiState.currentConversation) {
      message.error('No message selected for editing')
      return
    }

    setIsSaving(true)
    try {
      // The canvasContent is already in markdown format from the canvas editor
      // We don't need to convert it again - just save it directly
      dispatch(updateMessageContentRequest({
        conversationId: aiState.currentConversation.id,
        messageId: aiState.currentEditingMessage,
        content: canvasContent
      }))
      
      message.success('Message updated successfully')
      // Exit canvas mode after saving
      dispatch(exitCanvasOnlyMode())
      dispatch(setCurrentEditingMessage(null))
    } catch (error) {
      message.error('Failed to update message')
    } finally {
      setIsSaving(false)
    }
  }

  const handleCanvasContentChange = (newContent: string) => {
    setCanvasContent(newContent)
  }

  return (
    <>
      {/* Right Side Menu - Always visible */}
      <RightMenu
        onToggleAIChat={handleToggleAIChat}
        onToggleSettings={handleToggleSettings}
        isAIChatOpen={aiState.isAiPanelOpen && aiState.activeTab === 'ai-chat'}
        isSettingsOpen={aiState.isAiPanelOpen && aiState.activeTab === 'settings'}
      />
      {/* <CanvasEditor onClose={() => { }} onSave={async () => { }} collapsed={collapsed} /> */}
      {/* Tabbed Interface - Shows when any panel is open */}
      {aiState.isAiPanelOpen && (
        <div className="ai-assistant-tabbed-container">
          <div className="ai-assistant-tabs-header">
            <Tabs
              activeKey={aiState.activeTab || undefined}
              onChange={handleTabChange}
              type="card"
              size="small"
              tabBarExtraContent={
                <div className="tab-extra-actions">
                  {aiState.currentEditingMessage && (
                    <Button title='Canvas Only View' type="text" onClick={handleEnterCanvasOnlyMode} icon={<EyeOutlined />} />
                  )}
                  <Button title='Toggle Full screen' type="text" onClick={() => { }} icon={<ExpandOutlined />} />
                  <Button title='Close Chat Panel' type="text" onClick={handleCloseAll} icon={<CloseOutlined />} />
                </div>
              }
            >
              <TabPane
                tab={
                  <span>
                    <RobotOutlined />
                    {intl.formatMessage({ id: 'ai.agent.name' }) }
                  </span>
                }
                key="ai-chat"
              />
              <TabPane
                tab={
                  <span>
                    <SettingOutlined />
                    Settings
                  </span>
                }
                key="settings"
              />
            </Tabs>
          </div>

          <div className="ai-assistant-tab-content">
            {aiState.activeTab === 'ai-chat' && (
              <AIChatBox
                isVisible={true}
                onClose={handleToggleAIChat}
                hideHeader={true}
              />
            )}
            {aiState.activeTab === 'settings' && (
              <AISettings />
            )}
          </div>
        </div>
      )}

      {/* Canvas Only Mode - Shows when in canvas-only mode */}
      {aiState.isCanvasOnlyMode && (
        <div className="canvas-only-container">
          <Card
            title="Canvas Editor"
            size="small"
            extra={
              <div style={{ display: 'flex', gap: '8px' }}>
                <Button
                  type="text"
                  icon={<SaveOutlined />}
                  onClick={handleSaveCanvasContent}
                  loading={isSaving}
                  disabled={isSaving}
                />
                  
                <Button
                  type="text"
                  icon={<CloseOutlined />}
                  title="Close Canvas"
                  onClick={() => {
                    dispatch(setCurrentEditingMessage(null))
                    dispatch(closeAllPanels())
                  }}
                />
              </div>
            }
            className="canvas-only-card"
          >
            <CanvasEditor
              content={canvasContent}
              placeholder="Edit your content here..."
              onChange={handleCanvasContentChange}
              editable={true}
              style={{
                minHeight: '60vh',
                maxHeight: '80vh',
                overflow: 'auto'
              }}
            />
          </Card>
        </div>
      )}
    </>
  )
}

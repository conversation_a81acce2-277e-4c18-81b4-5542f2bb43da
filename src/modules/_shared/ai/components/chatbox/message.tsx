import React from 'react'
import { Message } from '../../types'
import { Avatar, Button, Space, Tooltip, Typography } from 'antd'
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  CopyOutlined,
  LoadingOutlined,
  RobotOutlined,
} from '@ant-design/icons'
import intl from '../../../../../config/locale.config'
import copy from 'copy-to-clipboard'
import CustomSvgIcons from '../../../../../helper/component/custom-icons'
import Markdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { MessageStep } from './message-step'

interface MessageContentProps {
  message: Message
  openCanvas: () => void
}

const UserMessage: React.FC<Message> = (message) => {
  return (
    <div style={{ display: 'flex', justifyContent: 'end', width: '100%' }}>
      <div className="user-message" style={{ width: '90%' }}>
        <Markdown remarkPlugins={[remarkGfm]}>{message.content}</Markdown>
      </div>
    </div>
  )
}

const BotMessage: React.FC<
  Message & Pick<MessageContentProps, 'openCanvas'>
> = (message) => {
  return (
    <div className="bot-message" key={message.id}>
      {message.steps?.map(MessageStep)}
      <div>
        <Markdown remarkPlugins={[remarkGfm]}>{message.content}</Markdown>
        {message.isLoading && <LoadingOutlined />}
      </div>
      {Boolean(message.content) && (
        <div className="d-flex justify-content-between align-items-center w-100">
          <div className="message-tools">
            <Tooltip title="Copy" placement="bottom">
              <Button
                type="text"
                icon={<CopyOutlined />}
                onClick={() => copy(message.content)}
              />
            </Tooltip>
            <Tooltip title="Edit in Canvas" placement="bottom">
              <Button
                type="text"
                icon={<CustomSvgIcons name="CanvasButtonIcon" fontSize={12} />}
                onClick={message.openCanvas}
                disabled={message.isLoading}
              />
            </Tooltip>
          </div>
          {Boolean(message.promptTokens && message.completionTokens) && (
            <Space>
              <Tooltip title="Prompt Tokens">
                <ArrowUpOutlined /> {message.promptTokens}
              </Tooltip>
              <Tooltip title="Completion Tokens">
                <ArrowDownOutlined /> {message.completionTokens}
              </Tooltip>
            </Space>
          )}
        </div>
      )}
    </div>
  )
}

export const MessageContent: React.FC<MessageContentProps> = ({
  message,
  openCanvas,
}) => {
  return (
    <div key={message.id} className="message-container">
      {message.isBot && (
        <Space style={{ marginBottom: '.5rem' }}>
          <Avatar
            size="small"
            icon={<RobotOutlined />}
            style={{ backgroundColor: '#2979FF' }}
          />
          <Typography.Text strong className="agent-name">
            {intl.formatMessage({ id: 'ai.agent.name' })}
          </Typography.Text>
        </Space>
      )}
      <div>
        {!message.isBot && <UserMessage {...message} />}
        {message.isBot && <BotMessage {...message} openCanvas={openCanvas} />}
      </div>
    </div>
  )
}

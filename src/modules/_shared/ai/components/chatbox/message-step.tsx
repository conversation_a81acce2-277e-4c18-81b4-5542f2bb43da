import React from 'react'
import { IMessageStep } from '../../types'
import { Space, Typography } from 'antd'
import CustomSvgIcons from '../../../../../helper/component/custom-icons'
import remarkGfm from 'remark-gfm'
import Markdown from 'react-markdown'
import { CheckCircleFilled, LoadingOutlined } from '@ant-design/icons'

type StepConfig = {
  icon: string | React.ReactElement
  title: (step: IMessageStep) => string | React.ReactElement
  component: React.FC<IMessageStep>
}

const capitalizeFirstLetter = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1)
}
const normalizeMemberId = (memberId: string) => {
  if (!memberId) return ''
  return memberId
    .split('-')
    .map((part) => capitalizeFirstLetter(part))
    .join(' ')
}

const STEP_MAP: Record<string, StepConfig> = {
  transfer_task_to_member: {
    icon: <CustomSvgIcons name="DelegationIcon" />,
    title: (step) =>
      `Delegate task to ${normalizeMemberId(step.args.member_id)}`,
    component: (step) => (
      <>
        <p>
          <b>Member:</b> {step.args.member_id}
        </p>
        <p>
          <b>Task Description:</b> {step.args.task_description}
        </p>
        <p>
          <b>Expected output:</b> {step.args.expected_output}
        </p>
        {Boolean(step.result) && (
          <div>
            <b>Result:</b>{' '}
            <Markdown remarkPlugins={[remarkGfm]}>{step.result}</Markdown>
          </div>
        )}
      </>
    ),
  },
  think: {
    icon: <CustomSvgIcons name="ThinkingIcon" />,
    title: () => 'Thinking',
    component: (step) => (
      <>
        <p>
          <b>Title:</b> {step.args.title}
        </p>
        <p>
          <b>Thought:</b> {step.args.thought}
        </p>
        {Boolean(step.args.action) && (
          <p>
            <b>Next action:</b> {step.args.action}
          </p>
        )}
        {Boolean(step.args.confidence) && (
          <div className="text-secondary font-italic">
            Confidence: {step.args.confidence}
          </div>
        )}
      </>
    ),
  },
  analyze: {
    icon: <CustomSvgIcons name="AnalyzeIcon" />,
    title: () => 'Analyze',
    component: (step) => (
      <>
        <p>
          <b>Title:</b> {step.args.title}
        </p>
        <p>
          <b>Analysis:</b> {step.args.analysis}
        </p>
        {Boolean(step.args.result) && (
          <p>
            <b>Result:</b> {step.args.result}
          </p>
        )}
        {Boolean(step.args.next_action) && (
          <p>
            <b>Next action:</b> {step.args.next_action}
          </p>
        )}
        {Boolean(step.args.confidence) && (
          <div className="text-secondary font-italic">
            Confidence: {step.args.confidence}
          </div>
        )}
      </>
    ),
  },
}

export const MessageStep: React.FC<IMessageStep> = (step, index) => {
  const stepConfig = STEP_MAP[step?.name]
  if (!stepConfig) return null
  return (
    <div className="message-step" key={step.id}>
      <Typography.Title level={5} style={{ marginBottom: 0 }}>
        <div
          className="d-flex"
          style={{
            cursor: 'pointer',
          }}
          data-toggle="collapse"
          aria-expanded="false"
          aria-controls={step.id}
          data-target={`#${step.id}`}
        >
          <Space className="flex-grow-1">
            {stepConfig.icon}
            {stepConfig.title(step)}
          </Space>
          {step.done ? <CheckCircleFilled /> : <LoadingOutlined spin />}
        </div>
      </Typography.Title>
      <div className="collapse" id={step.id}>
        {stepConfig.component(step)}
      </div>
    </div>
  )
}

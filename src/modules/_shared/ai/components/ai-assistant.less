@import '../../../../commons.less';

.ai-assistant-tabbed-container {
  position: fixed;
  top: @navbar-height;
  right: 0;
  bottom: 0;
  width: 600px; // Increased width to match ai-assistant-container
  z-index: 1001;
  background: white;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;

  // Animation
  animation: slideInFromRight 0.3s ease-out;

  @keyframes slideInFromRight {
    from {
      transform: translateX(100%);
    }

    to {
      transform: translateX(0);
    }
  }

  @media (max-width: 1200px) {
    width: 450px; // Responsive width for medium screens
  }

  @media (max-width: 768px) {
    width: 100%;
    left: 0;
  }

  .ai-assistant-tabs-header {
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    padding: 0;

    .ant-tabs {
      margin: 0;

      .ant-tabs-nav {
        margin: 0;
        padding: 8px 16px 0 16px;

        .ant-tabs-nav-wrap {
          .ant-tabs-nav-list {
            .ant-tabs-tab {
              background: transparent;
              border: 1px solid transparent;
              border-radius: 6px 6px 0 0;
              margin-right: 4px;
              padding: 8px 16px;

              &:hover {
                background: rgba(0, 0, 0, 0.04);
              }

              &.ant-tabs-tab-active {
                background: white;
                border-color: #e8e8e8;
                border-bottom-color: white;

                .ant-tabs-tab-btn {
                  color: @primary-color;
                  font-weight: 500;
                }
              }

              .ant-tabs-tab-btn {
                display: flex;
                align-items: center;
                gap: 6px;

                .anticon {
                  font-size: 14px;
                }
              }
            }
          }
        }
      }

      .ant-tabs-extra-content {
        .tab-extra-actions {
          display: flex;
          align-items: center;
          padding: 8px 0;

          .close-all-btn {
            padding: 4px 8px;
            cursor: pointer;
            border-radius: 4px;
            color: #666;
            transition: all 0.2s;

            &:hover {
              background: rgba(0, 0, 0, 0.06);
              color: #333;
            }
          }
        }
      }
    }
  }

  .ai-assistant-tab-content {
    flex: 1;
    overflow: hidden;
    position: relative;

    // Override the individual component positioning since they're now in tabs
    .ai-assistant-container,
    .settings-panel-container {
      position: static;
      width: 100%;
      height: 100%;
      box-shadow: none;
      border: none;
      transform: none;
      animation: none;

      .ai-assistant-card,
      .settings-panel-card {
        height: 100%;
        border-radius: 0;

        // When header is hidden, adjust the content layout
        .ant-card-body {
          height: 100%;
          display: flex;
          flex-direction: column;
        }
      }

      // Adjust content areas when headers are hidden
      .token-usage {
        flex: none;
        padding: 12px 16px;
        border-bottom: 1px solid #e8e8e8;
      }

      .current-task {
        flex: none;
        padding: 12px 16px;
        border-bottom: 1px solid #e8e8e8;
      }

      .ai-messages {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
      }

      .ai-input-area {
        flex: none;
        padding: 12px 16px;
        border-top: 1px solid #e8e8e8;
      }

      // Settings panel layout
      .settings-content {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
      }

      .settings-footer {
        flex: none;
        padding: 16px 20px;
        border-top: 1px solid #e8e8e8;
        background: #fafafa;
      }
    }
  }
}

// Update page wrapper to accommodate the tabbed interface
.page-wrapper {
  &.ai-panels-open {
    padding-right: 450px;

    @media (max-width: 768px) {
      padding-right: 0;
    }
  }
}

// Update right menu positioning when tabbed interface is open
.right-side-menu {
  .ai-panels-open & {
    right: 470px;

    @media (max-width: 768px) {
      right: 10px;
    }
  }
}

// Responsive adjustments
@media (max-width: 1400px) {
  .ai-assistant-tabbed-container {
    width: 500px;
  }

  .page-wrapper.ai-panels-open {
    padding-right: 500px;
  }

  .right-side-menu .ai-panels-open & {
    right: 520px;
  }
}

@media (max-width: 1200px) {
  .ai-assistant-tabbed-container {
    width: 450px;
  }

  .page-wrapper.ai-panels-open {
    padding-right: 450px;
  }

  .right-side-menu .ai-panels-open & {
    right: 470px;
  }
}

@media (max-width: 992px) {
  .ai-assistant-tabbed-container {
    width: 400px;
  }

  .page-wrapper.ai-panels-open {
    padding-right: 400px;
  }

  .right-side-menu .ai-panels-open & {
    right: 420px;
  }
}

// Smooth transitions for layout changes
.page-wrapper {
  transition: padding-right 0.3s ease-out;
}

.right-side-menu {
  transition: right 0.3s ease-out;
}

// Tab content animations
.ai-assistant-tab-content {

  .ai-assistant-container,
  .settings-panel-container {
    animation: fadeIn 0.2s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Canvas Only Mode Styles
.canvas-only-container {
  position: fixed;
  top: @navbar-height;
  left: 20px;
  right: 80px; // Leave space for right menu
  bottom: 20px;
  z-index: 999;
  padding: 20px;
  background: rgba(0, 0, 0, 0.02);
  
  .canvas-only-card {
    height: 100%;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid #e8e8e8;
    animation: slideInFromRight 0.3s ease-out;
    
    .ant-card-head {
      background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
      border-bottom: 1px solid #e8e8e8;
      border-radius: 12px 12px 0 0;
      
      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
      
      .ant-card-extra {
        .ant-btn {
          border: none;
          box-shadow: none;
          
          &:hover {
            background: #f5f5f5;
          }
        }
      }
    }
    
    .ant-card-body {
      padding: 24px;
      height: calc(100% - 64px);
      overflow: hidden;
    }
  }
  
  @media (max-width: 768px) {
    left: 10px;
    right: 70px;
    padding: 10px;
  }
}
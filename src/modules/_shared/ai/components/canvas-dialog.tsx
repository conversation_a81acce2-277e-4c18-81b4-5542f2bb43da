import './canvas-dialog.less'
import { <PERSON><PERSON>, <PERSON><PERSON>, Toolt<PERSON> } from "antd"
import { CloseOutlined } from "@ant-design/icons"
import React, { forwardRef, useImperativeHandle, useState } from 'react'
import { CanvasEditor } from '../../../../helper/component/tiptap'

type CanvasDialogProps = {
  onClose?: () => void
  ref: any
}

export const CanvasDialog: React.FC<CanvasDialogProps> = forwardRef(({ onClose }, ref) => {
  const [isOpen, setIsOpen] = useState(false)
  const [content, setContent] = useState("")

  const handleClose = () => {
    onClose?.()
    setIsOpen(false)
  }

  const handleOpen = (initialContent: string) => {
    setContent(initialContent)
    setIsOpen(true)
  }

  const handleContentChange = (newContent: string) => {
    setContent(newContent)
  }

  useImperativeHandle(ref, () => ({
    open: handleOpen,
  }))

  return <Modal
    open={isO<PERSON>}
    width="50%"
    closable={false}
    footer={null}
    style={{ top: 45 }}
  >
    <div className="canvas-dialog-content">
      <div className="canvas-dialog-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <h2 style={{ margin: 0 }}>Canvas Editor</h2>
        <div style={{ display: 'flex', gap: '8px' }}>
          <Tooltip title="Close Editor">
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={handleClose}
              style={{ color: '#666' }}
            />
          </Tooltip>
        </div>
      </div>
      <div className="canvas-dialog-body">
        <CanvasEditor
          content={content}
          placeholder="Type '/' for commands, or start typing..."
          onChange={handleContentChange}
          editable={true}
          style={{
            minHeight: '400px',
            padding: '16px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px'
          }}
        />
      </div>
    </div>
  </Modal>
})
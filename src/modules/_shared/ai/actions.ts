import { createAction } from '@reduxjs/toolkit'
import {
  AIActionTypes, Conversation, ConversationCreateRequest, Message, MessageEvent, MessageEventError,
  SendMessageRequest, UpdateMessageContentRequest,
} from './types'

// UI Actions
export const setCurrentConversation = createAction<string>(AIActionTypes.SET_CURRENT_CONVERSATION)
export const setCurrentConversationMessages = createAction<Message[]>(AIActionTypes.SET_CURRENT_CONVERSATION_MESSAGES)
export const fetchLastConversationOrCreate = createAction(AIActionTypes.FETCH_LAST_CONVERSATION_OR_CREATE)

// Conversation Actions
export const fetchConversationsRequest = createAction(AIActionTypes.FETCH_CONVERSATIONS_REQUEST)
export const fetchConversationsSuccess = createAction<Conversation[]>(AIActionTypes.FETCH_CONVERSATIONS_SUCCESS)
export const fetchConversationsFailure = createAction<string>(AIActionTypes.FETCH_CONVERSATIONS_FAILURE)

export const createConversationRequest = createAction<ConversationCreateRequest>(AIActionTypes.CREATE_CONVERSATION_REQUEST)
export const createConversationSuccess = createAction<Conversation>(AIActionTypes.CREATE_CONVERSATION_SUCCESS)
export const createConversationFailure = createAction<string>(AIActionTypes.CREATE_CONVERSATION_FAILURE)

export const deleteConversationRequest = createAction<string>(AIActionTypes.DELETE_CONVERSATION_REQUEST)
export const deleteConversationSuccess = createAction<string>(AIActionTypes.DELETE_CONVERSATION_SUCCESS)
export const deleteConversationFailure = createAction<string>(AIActionTypes.DELETE_CONVERSATION_FAILURE)

// Message Actions
export const sendMessageRequest = createAction<SendMessageRequest>(AIActionTypes.SEND_MESSAGE_REQUEST)
export const sendMessageSuccess = createAction<Message>(AIActionTypes.SEND_MESSAGE_SUCCESS)
export const sendMessageFailure = createAction<string>(AIActionTypes.SEND_MESSAGE_FAILURE)

export const updateMessageContentRequest = createAction<UpdateMessageContentRequest>(AIActionTypes.UPDATE_MESSAGE_CONTENT_REQUEST)
export const updateMessageContentSuccess = createAction<Message>(AIActionTypes.UPDATE_MESSAGE_CONTENT_SUCCESS)
export const updateMessageContentFailure = createAction<string>(AIActionTypes.UPDATE_MESSAGE_CONTENT_FAILURE)

export const receiveAIResponse = createAction<Message>(AIActionTypes.RECEIVE_AI_RESPONSE)

// Streaming Actions
export const startStreamingResponse = createAction<string>(AIActionTypes.START_STREAMING_RESPONSE) // messageId
export const receiveStreamingChunk = createAction<MessageEvent>(AIActionTypes.RECEIVE_STREAMING_CHUNK)
export const endStreamingResponse = createAction<MessageEvent>(AIActionTypes.END_STREAMING_RESPONSE)
export const streamingError = createAction<MessageEventError>(AIActionTypes.STREAMING_ERROR)

// Typing Actions
export const setAITyping = createAction<boolean>(AIActionTypes.SET_AI_TYPING)

// Suggestions Actions
export const setSuggestions = createAction<string[]>(AIActionTypes.SET_SUGGESTIONS)
export const clearSuggestions = createAction(AIActionTypes.CLEAR_SUGGESTIONS)

// Error Actions
export const clearAIError = createAction(AIActionTypes.CLEAR_AI_ERROR)

// Panel Management Actions
export const toggleAIChatPanel = createAction(AIActionTypes.TOGGLE_AI_CHAT_PANEL)
export const toggleAISettingsPanel = createAction(AIActionTypes.TOGGLE_SETTINGS_PANEL)
export const setActiveTab = createAction<'ai-chat' | 'settings' | null>(AIActionTypes.SET_ACTIVE_TAB)
export const closeAllPanels = createAction(AIActionTypes.CLOSE_ALL_PANELS)
export const enterCanvasOnlyMode = createAction<string>(AIActionTypes.ENTER_CANVAS_ONLY_MODE)
export const exitCanvasOnlyMode = createAction(AIActionTypes.EXIT_CANVAS_ONLY_MODE)
export const setCurrentEditingMessage = createAction<string | null>(AIActionTypes.SET_CURRENT_EDITING_MESSAGE)

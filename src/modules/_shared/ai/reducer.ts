import { createReducer } from '@reduxjs/toolkit'
import _merge from 'lodash/merge'
import { AIAssistantState, Message, StreamMessageEvent } from './types'
import {
  setCurrentConversation,
  fetchConversationsRequest,
  fetchConversationsSuccess,
  fetchConversationsFailure,
  createConversationRequest,
  createConversationSuccess,
  createConversationFailure,
  deleteConversationRequest,
  deleteConversationSuccess,
  deleteConversationFailure,
  sendMessageRequest,
  sendMessageSuccess,
  sendMessageFailure,
  updateMessageContentRequest,
  updateMessageContentSuccess,
  updateMessageContentFailure,
  receiveAIResponse,
  startStreamingResponse,
  receiveStreamingChunk,
  endStreamingResponse,
  streamingError,
  setAITyping,
  setSuggestions,
  clearSuggestions,
  clearAIError,
  toggleAIChatPanel,
  toggleAISettingsPanel,
  setActiveTab,
  closeAllPanels,
  enterCanvasOnlyMode,
  exitCanvasOnlyMode,
  setCurrentEditingMessage,
  setCurrentConversationMessages,
} from './actions'

const initialState: AIAssistantState = {
  isOpen: false,
  isLoading: false,
  currentConversation: undefined,
  conversations: [],
  error: undefined,
  isTyping: false,
  suggestions: [],
  // UI Panel States
  isAiPanelOpen: false,
  activeTab: null,
  // Canvas Editor States
  isCanvasOnlyMode: false,
  canvasContent: '',
  currentEditingMessage: null,
}

const handleTogglePanel =
  (tab: AIAssistantState['activeTab']) => (state: AIAssistantState) => {
    state.isAiPanelOpen = !state.isAiPanelOpen
    state.activeTab = tab
  }

export const aiAssistantReducer = createReducer(initialState, (builder) => {
  builder
    // UI Actions
    .addCase(setCurrentConversation, (state, action) => {
      const conversation = state.conversations.find(
        (c) => c.id === action.payload
      )
      if (conversation) {
        conversation.messages = conversation.messages ?? []
        state.currentConversation = conversation
      }
    })

    // Conversation Actions
    .addCase(fetchConversationsRequest, (state) => {
      state.isLoading = true
      state.error = undefined
    })
    .addCase(fetchConversationsSuccess, (state, action) => {
      state.isLoading = false
      state.conversations = action.payload
    })
    .addCase(fetchConversationsFailure, (state, action) => {
      state.isLoading = false
      state.error = action.payload
    })

    .addCase(createConversationRequest, (state) => {
      state.isLoading = true
      state.error = undefined
    })
    .addCase(createConversationSuccess, (state, action) => {
      state.isLoading = false
      state.conversations.unshift(action.payload)
      state.currentConversation = action.payload
      state.currentConversation.messages =
        state.currentConversation.messages ?? []
    })
    .addCase(createConversationFailure, (state, action) => {
      state.isLoading = false
      state.error = action.payload
    })

    .addCase(deleteConversationRequest, (state) => {
      state.isLoading = true
      state.error = undefined
    })
    .addCase(deleteConversationSuccess, (state, action) => {
      state.conversations = state.conversations.filter(
        (c) => c.id !== action.payload
      )
    })
    .addCase(deleteConversationFailure, (state, action) => {
      state.isLoading = false
      state.error = action.payload
    })

    // Message Actions
    .addCase(sendMessageRequest, (state) => {
      state.isLoading = true
      state.error = undefined
    })
    .addCase(sendMessageSuccess, (state, action) => {
      state.isLoading = false
      if (state.currentConversation) {
        state.currentConversation.messages.push(action.payload)
        state.currentConversation.updatedAt = action.payload.updatedAt
        const conversationIndex = state.conversations.findIndex(
          (c) => c.id === state.currentConversation?.id
        )
        if (conversationIndex !== -1) {
          state.conversations[conversationIndex] = state.currentConversation
        }
      }
    })
    .addCase(sendMessageFailure, (state, action) => {
      state.isLoading = false
      state.error = action.payload
    })
    .addCase(updateMessageContentRequest, (state) => {
      state.isLoading = true
      state.error = undefined
    })
    .addCase(updateMessageContentSuccess, (state, action) => {
      state.isLoading = false
      if (state.currentConversation) {
        const messageIndex = state.currentConversation.messages.findIndex(
          (m) => m.id === action.payload.id
        )
        if (messageIndex !== -1) {
          state.currentConversation.messages[messageIndex] = action.payload
          state.currentConversation.updatedAt = action.payload.updatedAt
          const conversationIndex = state.conversations.findIndex(
            (c) => c.id === state.currentConversation?.id
          )
          if (conversationIndex !== -1) {
            state.conversations[conversationIndex] = state.currentConversation
          }
        }
      }
      state.currentEditingMessage = null
    })
    .addCase(updateMessageContentFailure, (state, action) => {
      state.isLoading = false
      state.error = action.payload
    })
    .addCase(receiveAIResponse, (state, action) => {
      state.isTyping = false
      if (state.currentConversation) {
        state.currentConversation.messages.push(action.payload)
        const conversationIndex = state.conversations.findIndex(
          (c) => c.id === state.currentConversation?.id
        )
        if (conversationIndex !== -1) {
          state.conversations[conversationIndex] = state.currentConversation
        }
      }
    })
    .addCase(startStreamingResponse, (state, action) => {
      if (state.currentConversation) {
        // TODO: Do something
      }
    })
    .addCase(receiveStreamingChunk, (state, action) => {
      if (state.currentConversation) {
        const { event, id, ...partialMessage } = action.payload

        const messageIndex = state.currentConversation.messages.findIndex(
          (m) => m.id === id
        )

        const message =
          messageIndex !== -1
            ? state.currentConversation.messages[messageIndex]
            : undefined
        switch (action.payload.event) {
          case StreamMessageEvent.MessageCreated:
            state.currentConversation.messages.push({
              id,
              ...partialMessage,
              isLoading: true,
              isBot: true,
            } as Message)

            break
          case StreamMessageEvent.MessageDelta:
            if (!message) break
            state.isTyping = false
            if (partialMessage.content) {
              message.content += partialMessage.content
            }
            if (partialMessage.reasoningContent) {
              message.reasoningContent += partialMessage.reasoningContent
            }
            if (partialMessage.step) {
              message.steps = message.steps ?? []
              // Update existing message
              const existingStepIndex = message.steps!.findIndex(
                (s) => s.id === partialMessage.step!.id
              )
              if (existingStepIndex !== -1) {
                _merge(message.steps![existingStepIndex], partialMessage.step)
              } else {
                message.steps!.push(partialMessage.step!)
              }
            }

            break
          case StreamMessageEvent.MessageComplete:
            if (!message) break
            _merge(message, { ...partialMessage, isLoading: false })
            state.currentConversation.messages[messageIndex] =
              message as Message
            break
          case StreamMessageEvent.ConversationNameGenerated:
            if (partialMessage.conversationId === state.currentConversation.id && partialMessage["conversationName"]) {
              state.currentConversation.title = partialMessage["conversationName"]
              state.conversations.forEach((c) => {
                if (c.id === state.currentConversation!.id) {
                  c.title = state.currentConversation!.title
                }
              })
            }
            break
          case StreamMessageEvent.MessageError:
            break
        }
      }
    })
    .addCase(setCurrentConversationMessages, (state, action) => {
      if (state.currentConversation) {
        state.currentConversation.messages = action.payload
      }
    })
    .addCase(endStreamingResponse, (state, action) => {
      state.isTyping = false
      if (state.currentConversation) {
        const messageIndex = state.currentConversation.messages.findIndex(
          (m) => m.id === action.payload.id
        )
        if (messageIndex !== -1) {
          state.currentConversation.messages[messageIndex].isLoading = false
          state.currentConversation.updatedAt = new Date()
        }
      }
    })
    .addCase(streamingError, (state, action) => {
      state.isTyping = false
      state.error = action.payload.error
      if (state.currentConversation) {
        const messageIndex = state.currentConversation.messages.findIndex(
          (m) => m.id === action.payload.event
        )
        if (messageIndex !== -1) {
          state.currentConversation.messages[messageIndex].isLoading = false
          state.currentConversation.messages[messageIndex].content =
            'Error: Failed to receive response'
        }
      }
    })
    // Typing Actions
    .addCase(setAITyping, (state, action) => {
      state.isTyping = action.payload
    })

    // Suggestions Actions
    .addCase(setSuggestions, (state, action) => {
      state.suggestions = action.payload
    })
    .addCase(clearSuggestions, (state) => {
      state.suggestions = []
    })

    // Error Actions
    .addCase(clearAIError, (state) => {
      state.error = undefined
    })
    .addCase(toggleAIChatPanel, handleTogglePanel('ai-chat'))
    .addCase(toggleAISettingsPanel, handleTogglePanel('settings'))
    .addCase(setActiveTab, (state, action) => {
      state.activeTab = action.payload
    })
    .addCase(closeAllPanels, (state) => {
      state.isAiPanelOpen = false
      state.activeTab = null
      state.isCanvasOnlyMode = false
      state.canvasContent = ''
      state.currentEditingMessage = null
    })
    .addCase(enterCanvasOnlyMode, (state, action) => {
      state.isAiPanelOpen = false
      state.activeTab = null
      state.isCanvasOnlyMode = true
      state.canvasContent = action.payload
    })
    .addCase(exitCanvasOnlyMode, (state) => {
      state.isCanvasOnlyMode = false
      state.canvasContent = ''
      state.currentEditingMessage = null
    })
    .addCase(setCurrentEditingMessage, (state, action) => {
      state.currentEditingMessage = action.payload
    })
})

export { initialState as AIState }

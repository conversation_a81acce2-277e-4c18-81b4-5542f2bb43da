import { Action } from '@reduxjs/toolkit'
import {
  all,
  call,
  fork,
  put,
  take,
  takeLatest,
} from 'redux-saga/effects'
import { eventChannel, EventChannel } from 'redux-saga'
import { extractProjectCode, ShowAppMessage } from '../../../helper/share'
import { MESSAGE_TYPE } from '../../../constants'
import aiService from '../../../services/ai.service'
import {
  createConversationFailure,
  createConversationRequest,
  createConversationSuccess,
  deleteConversationFailure,
  deleteConversationRequest,
  deleteConversationSuccess,
  endStreamingResponse,
  fetchConversationsFailure,
  fetchConversationsRequest,
  fetchConversationsSuccess,
  fetchLastConversationOrCreate,
  receiveAIResponse,
  receiveStreamingChunk,
  sendMessageFailure,
  sendMessageRequest,
  sendMessageSuccess,
  setAITyping,
  setCurrentConversation,
  setCurrentConversationMessages,
  startStreamingResponse,
  streamingError,
  updateMessageContentRequest,
  updateMessageContentSuccess,
  updateMessageContentFailure,
} from './actions'
import {
  AgentCode,
  Conversation,
  Message,
  MessageEvent,
  SendMessageRequest,
  UpdateMessageContentRequest,
  StreamMessageEvent,
} from './types'

// Default agent type and project configuration
const getDefaultAgentType = () => AgentCode.Master
const getProjectId = (): string => extractProjectCode() ?? ""
enum EventType {
  Chunk,
  Complete,
  Error,
}

// Create event channel for streaming from POST /v1/conversations/:id/messages
function createStreamingChannel(
  apiRequest: SendMessageRequest
): EventChannel<any> {
  return eventChannel((emitter) => {
    // Start the streaming request
    aiService.sendStreamingMessage(
      apiRequest,
      // onChunk callback
      (event: MessageEvent) => {
        emitter({
          type: EventType.Chunk,
          payload: event,
        })
      },
      // onComplete callback
      () => {
        emitter({
          type: EventType.Complete,
        })
      },
      // onError callback
      (error: Error) => {
        emitter({
          type: EventType.Error,
          payload: error,
        })
      }
    ).catch(() => {
      // Handle fetch event source errors silently
    })

    // Return unsubscribe function
    return () => {}
  })
}

// Streaming response handler saga
function* handleStreamingResponse(
  apiRequest: SendMessageRequest,
  streamId: string
) {
  try {
    const streamingChannel = yield call(createStreamingChannel, apiRequest)

    while (true) {
      const action = yield take(streamingChannel)

      if (action.type === EventType.Chunk) {
        const event = action.payload as MessageEvent
        yield put(receiveStreamingChunk(event))
      } else if (action.type === EventType.Complete) {
        yield put(
          endStreamingResponse({
            id: action?.payload?.id ?? streamId,
            event: StreamMessageEvent.MessageComplete,
          })
        )
        break
      } else if (action.type === EventType.Error) {
        yield put(
          streamingError({
            id: action?.payload?.id ?? streamId,
            event: StreamMessageEvent.MessageError,
            error: action.payload.message,
          })
        )
        break
      }
    }

    streamingChannel.close()
  } catch (error: any) {
    yield put(
      streamingError({
        id: streamId,
        event: StreamMessageEvent.MessageError,
        error: error.message,
      })
    )
  }
}

function* handleFetchConversations(action: Action) {
  if (fetchConversationsRequest.match(action)) {
    try {
      const conversations: Conversation[] = yield call(
        aiService.listConversations,
        extractProjectCode(),
        50,
        0
      )
      yield put(
        fetchConversationsSuccess(
          conversations
        )
      )
    } catch (error: any) {
      // Fallback to mock data on error
      yield put(fetchConversationsFailure(error.message))
      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to fetch conversations')
    }
  }
}

function* handleCreateConversation(action: Action) {
  if (createConversationRequest.match(action)) {
    try {
      const request = action.payload
      const data = yield call(aiService.createConversation, request)

      yield put(createConversationSuccess(data))
    } catch (error: any) {
      yield put(createConversationFailure(error.message))
      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to create conversation')
    }
  }
}

function* handleSendMessage(action: Action) {
  if (sendMessageRequest.match(action)) {
    const request = action.payload as SendMessageRequest

    try {
      const userMessage: Message = {
        id: Date.now().toString(),
        content: request.content,
        isBot: false,
        steps: [],
        references: [],
        isLoading: false,
        promptTokens: 0,
        completionTokens: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      yield put(sendMessageSuccess(userMessage))

      // Show AI typing indicator
      yield put(setAITyping(true))

      if (request.sse) {
        // Handle real streaming response with SSE
        const streamingId = `streaming_${Date.now()}`
        yield put(startStreamingResponse(streamingId))

        try {
          // Start the streaming saga
          yield fork(handleStreamingResponse, request, streamingId)
        } catch (streamError: any) {
          yield put(
            streamingError({
              id: streamingId,
              error: streamError.message,
              event: StreamMessageEvent.MessageError,
            })
          )
        }
      } else {
        // Handle regular response
        const data = yield call(aiService.sendMessage, request)


        yield put(receiveAIResponse(data))
      }
    } catch (error: any) {
      yield put(sendMessageFailure(error.message))
    }
  }
}

function* handleDeleteConversation(action: Action) {
  if (deleteConversationRequest.match(action)) {
    try {
      const conversationId = action.payload as string
      yield call(aiService.deleteConversation, conversationId)

      yield put(deleteConversationSuccess(conversationId))
    } catch (error: any) {
      yield put(deleteConversationFailure(error.message))
    }
  }
}

function* handleUpdateMessageContent(action: Action) {
  if (updateMessageContentRequest.match(action)) {
    try {
      const request = action.payload as UpdateMessageContentRequest
      const updatedMessage: Message = yield call(
        aiService.updateMessageContent,
        request.conversationId,
        request.messageId,
        request.content
      )
      yield put(updateMessageContentSuccess(updatedMessage))
      ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'Message updated successfully')
    } catch (error: any) {
      yield put(updateMessageContentFailure(error.message))
      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to update message')
    }
  }
}

function* handleFetchLastConversationOrCreate(action: Action) {
  if (fetchLastConversationOrCreate.match(action)) {
    try {
      const conversations: Conversation[] = yield call(
        aiService.listConversations,
        extractProjectCode(),
        50,
        0
      )

      if (conversations.length > 0) {
        yield put(fetchConversationsSuccess(conversations))
        yield put(setCurrentConversation(conversations[0].id))
      } else {
        const request = {
          agentType: getDefaultAgentType(),
          projectId: getProjectId(),
        }
        yield put(createConversationRequest(request))
      }
    } catch (error: any) {
      yield put(fetchConversationsFailure(error.message))
    }
  }
}

function* handleSetCurrentConversation(action: Action) {
  if (setCurrentConversation.match(action)) {
    const messages: Message[] = yield call(
      aiService.listMessages,
      action.payload,
      50,
      0
    )
    yield put(setCurrentConversationMessages(messages))
  }
}

function* watchAIAssistantRequests() {
  yield takeLatest(
    fetchLastConversationOrCreate.type,
    handleFetchLastConversationOrCreate
  )
  yield takeLatest(fetchConversationsRequest.type, handleFetchConversations)
  yield takeLatest(createConversationRequest.type, handleCreateConversation)
  yield takeLatest(sendMessageRequest.type, handleSendMessage)
  yield takeLatest(updateMessageContentRequest.type, handleUpdateMessageContent)
  yield takeLatest(deleteConversationRequest.type, handleDeleteConversation)
  yield takeLatest(setCurrentConversation.type, handleSetCurrentConversation)
}

export default function* aiAssistantSaga() {
  yield all([fork(watchAIAssistantRequests)])
}

import React, { <PERSON> } from 'react'
import Icon from '@ant-design/icons'

import './styles.less'

// table icons declaration
import { ReactComponent as EditCustomIcon } from '../../../assets/icons/edit.svg'
import { ReactComponent as DeleteCustomIcon } from '../../../assets/icons/delete.svg'
import { ReactComponent as CheckmarkCustomIcon } from '../../../assets/icons/check_mark.svg'

// sider icons declaration
import { ReactComponent as ObjectSiderCustomIcon } from '../../../assets/icons/object_sider.svg'
import { ReactComponent as ActorSiderCustomIcon } from '../../../assets/icons/actor_sider.svg'
import { ReactComponent as UsecaseSiderCustomIcon } from '../../../assets/icons/usecase_sider.svg'
import { ReactComponent as PermissionMatrixCustomSiderIcon } from '../../../assets/icons/permission_sider.svg'
import { ReactComponent as ScreenSiderCustomIcon } from '../../../assets/icons/screen_sider.svg'
import { ReactComponent as WorkflowSiderCustomIcon } from '../../../assets/icons/workflow_sider.svg'
import { ReactComponent as StateSiderCustomIcon } from '../../../assets/icons/state_sider.svg'
import { ReactComponent as NFRCustomIcon } from '../../../assets/icons/nfr_sider.svg'
import { ReactComponent as DataMigrationCustomIcon } from '../../../assets/icons/data_migration_sider.svg'
import { ReactComponent as MeetingCustomIcon } from '../../../assets/icons/meeting.svg'
import { ReactComponent as CanvasButtonIcon } from '../../../assets/icons/canvas_button.svg'
import { ReactComponent as DelegationIcon } from '../../../assets/icons/delegation.svg'
import { ReactComponent as ThinkingIcon } from '../../../assets/icons/thinking.svg'
import { ReactComponent as AnalyzeIcon } from '../../../assets/icons/analyze.svg'


const iconsList = {
  ObjectSiderCustomIcon,
  ActorSiderCustomIcon,
  UsecaseSiderCustomIcon,
  PermissionMatrixCustomSiderIcon,
  ScreenSiderCustomIcon,
  WorkflowSiderCustomIcon,
  StateSiderCustomIcon,
  NFRCustomIcon,
  DataMigrationCustomIcon,
  EditCustomIcon,
  DeleteCustomIcon,
  CheckmarkCustomIcon,
  MeetingCustomIcon,
  CanvasButtonIcon,
  DelegationIcon,
  ThinkingIcon,
  AnalyzeIcon
}

const CustomSvgIcons = (props) => {
  return <Icon component={iconsList[props.name]} {...props} />
}

export default CustomSvgIcons

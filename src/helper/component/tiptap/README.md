# TipTap Components

This folder contains reusable TipTap editor components with Notion-like functionality, including a slash command menu system.

## Components

### CanvasEditor
A basic TipTap editor with Notion-like features and slash commands.

```tsx
import { CanvasEditor } from '../../../helper/component/tiptap';

<CanvasEditor
  content="Initial content"
  placeholder="Type '/' for commands..."
  onChange={(newContent) => console.log(newContent)}
  editable={true}
/>
```

### FullScreenEditor
A wrapper around CanvasEditor with full-screen styling and layout optimized for the agent-chatbot form.

```tsx
import { FullScreenEditor } from '../../../helper/component/tiptap';

<FullScreenEditor
  content="Initial content"
  placeholder="Type '/' for commands..."
  onChange={(newContent) => console.log(newContent)}
  style={{ height: '100%', padding: '40px 24px' }}
/>
```

### SlashMenu
The slash command menu component that can be used independently.

```tsx
import { SlashMenu } from '../../../helper/component/tiptap';

<SlashMenu
  visible={true}
  position={{ top: 100, left: 50 }}
  commands={slashCommands}
  onCommandSelect={(cmd) => cmd.action(editor)}
  onClose={() => setVisible(false)}
/>
```

## Hooks

### useSlashMenu
A hook that manages slash menu state and interactions.

```tsx
import { useSlashMenu } from '../../../helper/component/tiptap';

const {
  menuState,
  showMenu,
  hideMenu,
  executeCommand,
  handleKeyDown,
  handleTextInput,
  handleClickOutside
} = useSlashMenu({ editor });
```

## Utilities

### Slash Commands
- `createSlashCommands()` - Returns all available slash commands
- `filterSlashCommands(commands, query)` - Filters commands by search query

### Text Conversion
- `convertToHtml(text)` - Converts plain text/markdown to HTML
- `convertToPlainText(html)` - Converts HTML to plain text

## Extension Groups

The extensions are organized into groups for easy import:

```tsx
import { 
  getCoreExtensions,
  getFormattingExtensions,
  getBlockExtensions,
  getListExtensions,
  getTableExtensions,
  getAllNotionExtensions
} from '../../../helper/component/tiptap';
```

## Individual Extensions

All TipTap extensions are available as individual imports:

```tsx
import {
  HeadingExtension,
  CodeBlockExtension,
  BlockquoteExtension,
  BulletListExtension,
  OrderedListExtension,
  // ... and more
} from '../../../helper/component/tiptap';
```

## Slash Commands

When you type `/` in the editor, a menu appears with these commands:

- **Text blocks**: Paragraph, Heading 1-3
- **Lists**: Bullet list, Numbered list
- **Formatting**: Code block, Quote
- **Elements**: Horizontal rule, Table

## Features

- **Notion-like interface**: Clean, minimal design similar to Notion
- **Slash commands**: Type `/` to insert blocks
- **Rich text formatting**: Bold, italic, code, strikethrough
- **Lists**: Bullet and numbered lists
- **Code blocks**: Syntax highlighting support
- **Tables**: Full table support with headers
- **Keyboard shortcuts**: Standard editing shortcuts
- **Auto-focus**: Automatic focus management
- **Responsive**: Works on different screen sizes

## Usage in Agent Chatbot

The agent-chatbot form uses the `FullScreenEditor` component which includes:

1. Full-screen layout with header bar
2. Save/Cancel buttons with keyboard shortcuts (Ctrl+S, Esc)
3. Integrated slash command menu
4. Proper content conversion between HTML and plain text
5. Loading states and error handling

The slash menu functionality is completely contained within the tiptap components, making it reusable across the application.

## Customization

### Adding New Extensions

1. Create a new extension file in the tiptap folder
2. Export it from `index.ts`
3. Add it to the appropriate extension group

### Adding New Slash Commands

1. Edit `slash-commands.tsx`
2. Add your command to the `createSlashCommands()` function
3. Include an icon, label, description, and action function

### Styling

The editor styles are defined in the `FullScreenEditor` component and can be customized by:

1. Modifying the CSS in `full-screen-editor.tsx`
2. Passing custom styles via the `style` prop
3. Adding custom CSS classes via the `className` prop

import React, { useEffect } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import {
  HeadingExtension,
  CodeBlockExtension,
  BlockquoteExtension,
  BulletListExtension,
  OrderedListExtension,
  ListItemExtension,
  StrikeExtension,
  CodeExtension,
  HorizontalRuleExtension,
  TableExtension,
  TableRowExtension,
  TableCellExtension,
  TableHeaderExtension,
  PlaceholderExtension,
  DragHandleExtension,
  MarkdownTableParserExtension,
  convertToHtml,
  convertToPlainText
} from './index';
import './drag-handle.css';
import './table-styles.css';
import { SlashMenu } from './slash-menu';
import { useSlashMenu } from './use-slash-menu';
import { TableControls } from './table-controls';
import { useTableControls } from './use-table-controls';

interface CanvasEditorProps {
  content: string;
  placeholder?: string;
  onChange?: (content: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  editable?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const CanvasEditor: React.FC<CanvasEditorProps> = ({
  content,
  placeholder = 'Type "/" for commands, or start typing...',
  onChange,
  onFocus,
  onBlur,
  editable = true,
  className,
  style
}) => {
  const editor = useEditor({
    extensions: [
      StarterKit,
      HeadingExtension,
      CodeBlockExtension,
      BlockquoteExtension,
      BulletListExtension,
      OrderedListExtension,
      ListItemExtension,
      StrikeExtension,
      CodeExtension,
      HorizontalRuleExtension,
      TableExtension,
      TableRowExtension,
      TableHeaderExtension,
      TableCellExtension,
      PlaceholderExtension.configure({ placeholder }),
      DragHandleExtension,
      MarkdownTableParserExtension,
    ],
    content: convertToHtml(content),
    editable,
    onUpdate: ({ editor }) => {
      if (onChange) {
        const html = editor.getHTML();
        const plainText = convertToPlainText(html);
        onChange(plainText);
      }
    },
    onFocus,
    onBlur,
    editorProps: {
      attributes: {
        class: `canvas-editor-content ${className || ''}`,
      },
    },
  });

  const {
    menuState,
    showMenu,
    hideMenu,
    executeCommand,
    handleKeyDown,
    handleTextInput,
    handleClickOutside
  } = useSlashMenu({ editor });

  const {
    controlsState,
    showControls,
    hideControls,
    addRow,
    addColumn,
    deleteRow,
    deleteColumn
  } = useTableControls(editor);

  // Update editor content when prop changes
  useEffect(() => {
    if (editor && content !== undefined) {
      const currentContent = convertToPlainText(editor.getHTML());
      if (currentContent !== content) {
        const htmlContent = convertToHtml(content);
        
        // Clear the editor first, then set the new content
        // This ensures proper parsing of complex structures like tables
        editor.commands.clearContent();
        editor.commands.setContent(htmlContent);
      }
    }
  }, [editor, content]);

  // Set up editor event handlers
  useEffect(() => {
    if (!editor) return;

    const originalHandleKeyDown = editor.options.editorProps?.handleKeyDown;

    editor.setOptions({
      editorProps: {
        ...editor.options.editorProps,
        handleKeyDown: (view, event) => {
          // Handle slash command
          if (event.key === '/') {
            const { state } = view;
            const { selection } = state;
            const { $from } = selection;
            
            // Get position for menu
            const coords = view.coordsAtPos($from.pos);
            showMenu({
              top: coords.top + 20,
              left: coords.left
            });
            return false; // Let the '/' character be inserted
          }

          // Handle slash menu navigation
          if (handleKeyDown(event)) {
            return true;
          }

          // Call original handler if exists
          if (originalHandleKeyDown) {
            return originalHandleKeyDown(view, event);
          }

          return false;
        },
        handleTextInput: (view, from, to, text) => {
          // Handle slash menu search
          if (handleTextInput(text)) {
            return false;
          }

          // Call original handler if exists - remove this call to avoid signature issues
          return false;
        },
        handleClick: (view, pos, event) => {
          const target = event.target as HTMLElement;
          
          // Check if clicked element is a table cell
          const cellElement = target.closest('td, th') as HTMLElement;
          if (cellElement) {
            const table = cellElement.closest('table');
            if (table) {
              // Find the row and column indices
              const tableRows = Array.from(table.querySelectorAll('tr'));
              const cellRow = cellElement.parentElement as HTMLTableRowElement;
              const row = tableRows.findIndex(tr => tr === cellRow);
              const col = Array.from(cellRow.children).findIndex(cell => cell === cellElement);
              
              // Show table controls
              if (row >= 0 && col >= 0) {
                showControls(row, col, cellElement);
                
                // Add table-focused class to editor for styling
                const editorContent = view.dom.closest('.canvas-editor-content');
                if (editorContent) {
                  editorContent.classList.add('table-focused');
                }
              }
              return true;
            }
          } else {
            // Hide controls if clicked outside table
            hideControls();
            
            // Remove table-focused class
            const editorContent = view.dom.closest('.canvas-editor-content');
            if (editorContent) {
              editorContent.classList.remove('table-focused');
            }
          }
          
          return false;
        }
      }
    });
  }, [editor, showMenu, handleKeyDown, handleTextInput, showControls, hideControls]);

  // Set up document event listeners
  useEffect(() => {
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [handleClickOutside]);

  // Adjust table column widths based on content length
  useEffect(() => {
    if (!editor) return;

    const adjustTableColumnWidths = () => {
      const tables = editor.view.dom.querySelectorAll('table');
      tables.forEach((table) => {
        const cells = table.querySelectorAll('td, th');
        cells.forEach((cell) => {
          const content = cell.textContent || '';
          const lines = content.split('\n').length;
          const hasLongLines = content.split('\n').some(line => line.length > 50);
          
          // If content would exceed 5 lines at normal width, make column wider
          if (lines > 3 || hasLongLines) {
            cell.classList.add('has-long-content');
            if (content.length > 200) {
              cell.setAttribute('data-content-length', 'long');
            }
          } else {
            cell.classList.remove('has-long-content');
            cell.removeAttribute('data-content-length');
          }
        });
      });
    };

    // Adjust on content updates
    const handleUpdate = () => {
      setTimeout(adjustTableColumnWidths, 100);
    };

    editor.on('update', handleUpdate);
    adjustTableColumnWidths(); // Initial adjustment

    return () => {
      editor.off('update', handleUpdate);
    };
  }, [editor]);

  if (!editor) {
    return <div>Loading editor...</div>;
  }

  return (
    <div style={{ position: 'relative', ...style }}>
      <div style={{ 
        overflowX: 'auto', 
        overflowY: 'auto',
        width: '100%',
        minHeight: '200px'
      }}>
        <EditorContent editor={editor} />
      </div>
      <SlashMenu
        visible={menuState.visible}
        position={menuState.position}
        commands={menuState.filteredCommands}
        onCommandSelect={executeCommand}
        onClose={hideMenu}
      />
      <TableControls
        visible={controlsState.visible}
        position={controlsState.position}
        onAddRow={addRow}
        onAddColumn={addColumn}
        onDeleteRow={deleteRow}
        onDeleteColumn={deleteColumn}
        onClose={hideControls}
      />
    </div>
  );
};

export default CanvasEditor;

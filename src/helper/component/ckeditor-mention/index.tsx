import AppCommonService from '../../../services/app.service'
import { CKEditor } from '@ckeditor/ckeditor5-react'
import { Editor } from 'ckeditor5-custom-build/build/ckeditor'
import React, { useEffect, useState } from 'react'
import { extractProjectCode } from '../../..//helper/share'
import { APP_ROUTES, REQ_ARTEFACT_TYPE_ID, PROJECT_PREFIX, COM_ARTEFACT_TYPE_ID } from '../../../constants'
import { Regex } from '../../../helper/share/type'
import { UploadAdapter } from '../ckeditor/UploadAdapter'

function MentionCustomization(editor) {
  // The upcast converter will convert <a class="mention" href="" data-user-id="">
  // elements to the model 'mention' attribute.
  editor.conversion.for('upcast').elementToAttribute({
    view: {
      name: 'a',
      key: 'data-mention',
      classes: 'mention',
      attributes: {
        href: true,
        'data-user-id': true,
      },
    },
    model: {
      key: 'mention',
      value: (viewItem) => {
        // The mention feature expects that the mention attribute value
        // in the model is a plain object with a set of additional attributes.
        // In order to create a proper object, use the toMentionAttribute helper method:
        const mentionAttribute = editor.plugins
          .get('Mention')
          .toMentionAttribute(viewItem, {
            // Add any other properties that you need.
            link: viewItem.getAttribute('href'),
            userId: viewItem.getAttribute('data-user-id'),
            dataReferences: viewItem.getAttribute('data-references'),
            description: viewItem.getAttribute('title'),
          })
        return mentionAttribute
      },
    },
    converterPriority: 'high',
  })

  // Downcast the model 'mention' text attribute to a view <a> element.
  editor.conversion.for('downcast').attributeToElement({
    model: 'mention',
    view: (modelAttributeValue, { writer }) => {
      // Do not convert empty attributes (lack of value means no mention).
      if (!modelAttributeValue) {
        return
      }
      let id = ''
      let link = ''
      const isCommon = editor?.config?._config?.is_common;
      if (modelAttributeValue) {
        if (isCommon) {
          switch (modelAttributeValue.artefactType) {
            case COM_ARTEFACT_TYPE_ID.OBJECT:
              id = `${Regex.req_suffix_cm_object}${modelAttributeValue.name}${Regex.req_prefix_cm_object}`
              link = `${APP_ROUTES.COMMON_OBJECT_DETAIL}${modelAttributeValue.name}`
              break
            case COM_ARTEFACT_TYPE_ID.SCREEN:
              id = `${Regex.req_suffix_cm_screen}${modelAttributeValue.name}${Regex.req_prefix_cm_screen}`
              link = `${APP_ROUTES.COMMON_SCREEN_DETAIL}${modelAttributeValue.name}`
              break
            case COM_ARTEFACT_TYPE_ID.USECASE:
              id = `${Regex.req_suffix_cm_usecase}${modelAttributeValue.name}${Regex.req_prefix_cm_usecase}`
              link = `${APP_ROUTES.COMMON_USECASE_DETAIL}${modelAttributeValue.name}`
              break
            case COM_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE:
              id = `${Regex.req_suffix_cm_cbr}${modelAttributeValue.name}${Regex.req_prefix_cm_cbr}`
              link = `${APP_ROUTES.COMMON_CBR_DETAIL}${modelAttributeValue.name}`
              break
            case COM_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT:
              id = `${Regex.req_suffix_cm_nfr}${modelAttributeValue.name}${Regex.req_prefix_cm_nfr}`
              link = `${APP_ROUTES.COMMON_NONFUNCTIONAL_DETAIL}${modelAttributeValue.name}`
              break
            case COM_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE:
              id = `${Regex.req_suffix_cm_email_template}${modelAttributeValue.name}${Regex.req_prefix_cm_email_template}`
              link = `${APP_ROUTES.COMMON_EMAIL_DETAIL}${modelAttributeValue.name}`
              break
            case COM_ARTEFACT_TYPE_ID.MESSAGE:
              id = `${Regex.req_suffix_cm_message}${modelAttributeValue.name}${Regex.req_prefix_cm_message}`
              link = `${APP_ROUTES.COMMON_MESSAGE_DETAIL}${modelAttributeValue.name}`
              break
              case COM_ARTEFACT_TYPE_ID.WORKFLOW:
              id = `${Regex.req_suffix_cm_workflow}${modelAttributeValue.name}${Regex.req_prefix_cm_workflow}`
              link = `${APP_ROUTES.COMMON_WORKFLOW_DETAIL}${modelAttributeValue.name}`
              break
            default:
              id = modelAttributeValue.userId
              link = modelAttributeValue.link
              break
          }
        } else {
          switch (modelAttributeValue.artefactType) {
            case REQ_ARTEFACT_TYPE_ID.ACTOR:
              id = `${Regex.req_suffix_actor}${modelAttributeValue.name}${Regex.req_prefix_actor}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.ACTOR_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.COMMON_BUSINESS_RULE:
              id = `${Regex.req_suffix_business_rule}${modelAttributeValue.name}${Regex.req_prefix_business_rule}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.COMMON_BUSINESS_RULE_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.DATA_MIGRATION:
              id = `${Regex.req_suffix_data_migration}${modelAttributeValue.name}${Regex.req_prefix_data_migration}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.DATA_MIGRATION_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.EMAIL_TEMPLATE:
              id = `${Regex.req_suffix_email}${modelAttributeValue.name}${Regex.req_prefix_email}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.MAIL_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.MEETING_MINUTE:
              id = `${Regex.req_suffix_meeting_minutes}${modelAttributeValue.name}${Regex.req_prefix_meeting_minutes}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.MEETING_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.MESSAGE:
              id = `${Regex.req_suffix_mess}${modelAttributeValue.name}${Regex.req_prefix_mess}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.MESSAGE_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.NON_FUNCTIONAL_REQUIREMENT:
              id = `${Regex.req_suffix_non_functional_requirement}${modelAttributeValue.name}${Regex.req_prefix_non_functional_requirement}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.NONFUNTIONAL_REQ_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.OBJECT:
              id = `${Regex.req_suffix_obj}${modelAttributeValue.name}${Regex.req_prefix_obj}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.OBJECT_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.OBJECT_RELATIONSHIP_DIAGRAM:
              id = `${Regex.req_suffix_object_relationship_diagram}${modelAttributeValue.name}${Regex.req_prefix_object_relationship_diagram}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.OBJECT_RELATIONSHIP_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.OTHER_REQUIREMENT:
              id = `${Regex.req_suffix_other_requirement}${modelAttributeValue.name}${Regex.req_prefix_other_requirement}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.OTHER_REQUIREMENT_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.REFERENCE_DOCUMENT:
              id = `${Regex.req_suffix_reference_document}${modelAttributeValue.name}${Regex.req_prefix_reference_document}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.REFERENCE_DOCUMENT_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.SCREEN:
              id = `${Regex.req_suffix_screen}${modelAttributeValue.name}${Regex.req_prefix_screen}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.SCREEN_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.STATE_TRANSITION:
              id = `${Regex.req_suffix_state_transition}${modelAttributeValue.name}${Regex.req_prefix_state_transition}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.STATE_TRANSITION_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.USECASE:
              id = `${Regex.req_suffix_use_case}${modelAttributeValue.name}${Regex.req_prefix_use_case}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USECASE_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.USECASE_DIAGRAM:
              id = `${Regex.req_suffix_usecase_diagram}${modelAttributeValue.name}${Regex.req_prefix_usecase_diagram}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USECASE_DIAGRAM_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.USER_REQUIREMENT:
              id = `${Regex.req_suffix_user_requirement}${modelAttributeValue.name}${Regex.req_prefix_user_requirement}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USER_REQUIREMENT_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.WORKFLOW:
              id = `${Regex.req_suffix_work_flow}${modelAttributeValue.name}${Regex.req_prefix_work_flow}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.WORKFLOW_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.PERMISSION_MATRIX:
              id = `${Regex.req_suffix_permission_matrix}${modelAttributeValue.name}${Regex.req_prefix_permission_matrix}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.PERMISSION_MATRIX}`
              break
            case REQ_ARTEFACT_TYPE_ID.USER_STORY:
              id = `${Regex.req_suffix_user_story}${modelAttributeValue.name}${Regex.req_prefix_user_story}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.USER_STORY_MANAGEMENT_DETAIL}${modelAttributeValue.name}`
              break
            case REQ_ARTEFACT_TYPE_ID.EPIC:
              id = `${Regex.req_suffix_epic}${modelAttributeValue.name}${Regex.req_prefix_epic}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.EPIC_MANAGEMENT_DETAIL}${modelAttributeValue.name}`
              break

            case REQ_ARTEFACT_TYPE_ID.SPRINT:
              id = `${Regex.req_suffix_sprint}${modelAttributeValue.name}${Regex.req_prefix_sprint}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.SPRINT_MANAGEMENT_DETAIL}${modelAttributeValue.name}`
              break

            case REQ_ARTEFACT_TYPE_ID.GLOSSARY:
              id = `${Regex.req_suffix_glossary}${modelAttributeValue.name}${Regex.req_prefix_glossary}`
              link = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.GLOSSARY}`
              break
            default:
              id = modelAttributeValue.userId
              link = modelAttributeValue.link
              break
          }
        }
      }
      const a = writer.createAttributeElement(
        'a',
        {
          'class': 'mention',
          'data-mention': modelAttributeValue.id,
          'data-user-id': id,
          'title': modelAttributeValue?.description ? modelAttributeValue?.description : modelAttributeValue.id,
          'data-references': modelAttributeValue.dataReferences ? modelAttributeValue.dataReferences : `#_${modelAttributeValue.artefactType}_${modelAttributeValue.name}_#`,
          'href': modelAttributeValue.link ? modelAttributeValue.link : '#' + link,
        },
        {
          priority: 20,
          id: modelAttributeValue.uid,
        }
      )

      return a
    },
    converterPriority: 'high',
  })
}

interface CkeditorMentionProps {
  data: any
  saveDataPre?: any
  validate?: any
  isCommon?: boolean
  placeholder?: string
}
const CkeditorMention = React.forwardRef(({ data, saveDataPre, validate, isCommon, placeholder = '' }: CkeditorMentionProps, ref: any) => {
  const [ckeditorData, setCkeditorData] = useState('')
  const [mentionItems, setMentionItems] = useState<any>(null)
  useEffect(() => {
    AppCommonService.getMentions(isCommon).then(res => {
      setMentionItems(res.data)
    }).catch(err => {
      setMentionItems([])
    })
    return () => {
      setMentionItems([])
    }
  }, [])

  useEffect(() => {
    setCkeditorData(data || '')
  }, [data])

  const getFeedItems = (queryText) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const finalList: any = []
        mentionItems.map((item) => {
          const searchString = queryText.toLowerCase()
          if (item.id.toLowerCase().replace(/\s/g, '').includes(searchString)) {
            finalList.push(item)
          }
        })
        if (finalList.length > 0) {
          finalList.sort((a, b) => {
            if (a.id < b.id) {
              return -1
            } else if (a.id > b.id) {
              return 1
            }
            return 0
          })
          const getMax20 = finalList.slice(0, 20)
          resolve(getMax20)
        } else {
          resolve([])
        }
      }, 100)
    })
  }

  const customItemRenderer = (item) => {
    const itemElement = document.createElement('span')
    itemElement.classList.add('custom-item')
    itemElement.id = `mention-list-item-id-${item.artefactType}`
    itemElement.title = `${item.artefactType === REQ_ARTEFACT_TYPE_ID.GLOSSARY ? item.description : item.id} `
    itemElement.textContent = `${item.id} `
    return itemElement
  }

  const editorConfiguration = {
    toolbar: {
      viewportTopOffset: 80,
      items: [
        'blockQuote',
        'bold',
        'italic',
        'strikethrough',
        'underline',
        'fontFamily',
        'alignment',
        'insertTable',
        'imageUpload',
        'numberedList',
        'bulletedList',
      ],

      shouldNotGroupWhenFull: true,
    },
    extraPlugins: [MentionCustomization],
    mention: {
      dropdownLimit: 99999999,
      feeds: [
        {
          marker: '@',
          feed: getFeedItems,
          itemRenderer: customItemRenderer,
        },
      ],
    },
    table: {
      contentToolbar: [
        'tableColumn',
        'tableRow',
        'mergeTableCells',
        'tableProperties',
        'tableCellProperties',
      ],
      defaultHeadings: { rows: 1 },
    },
    image: {
      styles: ['alignLeft', 'alignCenter', 'alignRight'],
      toolbar: [
        'imageStyle:alignLeft',
        'imageStyle:alignCenter',
        'imageStyle:alignRight',
        '|',
        '|',
        'imageTextAlternative',
      ],
    },
    is_common: isCommon
  }

  return (
    mentionItems ?
      <>
        <CKEditor
          ref={ref}
          editor={Editor}
          config={editorConfiguration}
          data={ckeditorData}
          placeholder={placeholder}
          onReady={(editor: any) => {
            if (editor) {
              editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
                return new UploadAdapter(loader);
              };
            }
          }}
          onChange={(event: any, editor: any) => {
            setCkeditorData(editor.getData())
            if (saveDataPre) {
              saveDataPre(editor.getData())
            }
          }}
          onBlur={(event: any, editor: any) => {
            if (validate) {
              validate(editor.getData())
            }
          }}
          onFocus={(event: any, editor: any) => { }}
        />
      </>
      : <></>
  )
})

export default CkeditorMention

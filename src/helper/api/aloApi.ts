import { AccountInfo, PublicClientApplication } from '@azure/msal-browser';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { loginRequest, msalConfig } from '../../authConfig';
import { APP_ROUTES } from '../../constants';
import { extractProjectCode } from '../share';
import { parseJwt } from "../share";

const msalInstance = new PublicClientApplication(msalConfig);

const instance = (headers?: Record<string, string>, isDownload = false) => {
  const returnValue = axios.create()
  const baseUrl = process.env.REACT_APP_API_BACKEND || '';
  const projectCode = extractProjectCode();

  returnValue.interceptors.request.use(
    (config) => {
      if (!headers) {
        let accessToken: any = localStorage.getItem('accessToken')
        config.headers = {
          'ProjectCode': projectCode ? projectCode : '',
          'Content-Type': 'application/json;charset=UTF-8',
          'Access-Control-Allow-Origin': '*',
          Authentication: `Bearer ${accessToken}`,
          Authorization: `Bearer ${accessToken}`
        }
      }
      if (isDownload) {
        config.responseType = 'arraybuffer'
      }
      if (projectCode && config.url?.indexOf('importtemplates') == -1) {
        config.url = config.url?.replace(baseUrl, `${baseUrl}projects/${projectCode}/`);
      }
      return config
    },
    (error) => {
      console.log(error)
      return Promise.reject(error)
    }
  )
  returnValue.interceptors.response.use(
    (response) => {
      return response
    },
    async (error) => {
      console.log('Authentication', error)
      if (error.response.status === 401) {
        try {
          const allAccounts = msalInstance.getAllAccounts();
          const defaultAccount = allAccounts.length > 0 ? allAccounts[0] : null;
          const account = msalInstance.getActiveAccount() || defaultAccount as AccountInfo;
          const request = {
            ...loginRequest,
            account: account,
          }
          const response = await msalInstance.acquireTokenSilent(request);
          const accessToken = response.accessToken;
          localStorage.setItem('accessToken', accessToken);
          const { config: oldRequest } = error;
          return axios.request({ ...oldRequest, headers: {
            ...oldRequest.headers,
            Authentication: `Bearer ${accessToken}`,
            Authorization: `Bearer ${accessToken}`
          }});
        } catch (tokenError) {
          return Promise.reject(tokenError)
        }

        // window.location.reload(); 
        // return Promise.resolve();
      }
      // if (error.response.status === 403) {
      //   // history.replace(APP_ROUTES.ACCESS_DENIED)
      //   window.location.href = '/#' + APP_ROUTES.ACCESS_DENIED;
      //   return Promise.reject(error);
      // }
      if (error.response.status === 404) {
        // history.replace(APP_ROUTES.ACCESS_DENIED)
        window.location.href = '/#/' + APP_ROUTES.PAGE_NOT_FOUND;
        return Promise.reject(error);
      }
      return Promise.reject(error);
    }
  )

  return returnValue
}

export const startTokenRefreshInterval = () => {
  const intervalId = setInterval(async () => {
    const accessToken = localStorage.getItem('accessToken');
    if (accessToken) {
      const allAccounts = msalInstance.getAllAccounts();
      const defaultAccount = allAccounts.length > 0 ? allAccounts[0] : null;
      const account = msalInstance.getActiveAccount() || defaultAccount as AccountInfo;
      const request = {
        ...loginRequest,
        account: account,
      };
      const response = await msalInstance.acquireTokenSilent(request);
      localStorage.setItem('accessToken', response.accessToken);
    }
  }, 30 * 60 * 1000);

  return intervalId;
};

export const apiCall = async (
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
  url: string,
  data?: Record<string, unknown> | string,
  headers?: Record<string, string>,
  isDownload?: boolean
): Promise<AxiosResponse<any>> => {
  const config: AxiosRequestConfig = { method, url }
  if (method === 'GET') {
    config.params = data
  } else {
    config.data = data
  }
  return instance(headers, isDownload)(config).then((response) => {
    return response
  })
}

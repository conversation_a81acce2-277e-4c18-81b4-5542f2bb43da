import { apiCall } from '../helper/api/aloApi'
import { API_URLS } from '../constants'
import { extractProjectCode } from '../helper/share'
import {
  SendMessageRequest,
  UpdateMessageContentRequest,
  Agent,
  Message,
  Conversation,
  ConversationCreateRequest,
  MessageEvent,
  AgentPromptUpdate,
} from '../modules/_shared/ai/types'
import { fetchEventSource } from '@microsoft/fetch-event-source'

export interface AIApiResponse<T = any> {
  data: T
  metadata: {
    total: number
    offset: number
    limit: number
  }
  error: string
  message?: string
}

class _AIService {
  /**
   * Get available agents
   */
  async getListAgents(): Promise<Agent[]> {
    try {
      const response = await apiCall('GET', API_URLS.AI_AGENTS).then(
        ({ data }) => data
      )
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to fetch agents: ${error.message}`)
    }
  }

  async getAgentDetails(agentCode: string): Promise<Agent> {
    try {
      const response = await apiCall(
        'GET',
        `${API_URLS.AI_AGENTS}/${agentCode}`
      ).then(({ data }) => data)
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to fetch agents: ${error.message}`)
    }
  }

  async updateAgentInstruction(
    agentCode,
    request: AgentPromptUpdate
  ): Promise<Agent> {
    try {
      const response = await apiCall(
        'PATCH',
        API_URLS.AGENT_CONFIG(agentCode),
        request
      ).then(({ data }) => data)
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to update agent: ${error.message}`)
    }
  }

  /**
   * List all conversations
   */
  async listConversations(
    projectId = extractProjectCode(),
    limit: number = 50,
    offset: number = 0
  ): Promise<AIApiResponse<Conversation[]>> {
    try {
      const params = { projectId, limit, offset }
      const response = await apiCall(
        'GET',
        API_URLS.AI_CONVERSATIONS,
        params
      ).then(({ data }) => data)
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to fetch conversations: ${error.message}`)
    }
  }

  /**
   * Create a new conversation
   */
  async createConversation(
    request: ConversationCreateRequest
  ): Promise<AIApiResponse<Conversation>> {
    try {
      const requestBody = {
        project_id: request.projectId,
      }

      const response = await apiCall(
        'POST',
        API_URLS.AI_CONVERSATIONS,
        requestBody
      ).then(({ data }) => data)

      return response.data
    } catch (error: any) {
      throw new Error(`Failed to create conversation: ${error.message}`)
    }
  }

  /**
   * Delete a conversation
   */
  async deleteConversation(conversationId: string): Promise<AIApiResponse> {
    try {
      const response = await apiCall(
        'DELETE',
        API_URLS.AI_DELETE_CONVERSATION(conversationId)
      ).then(({ data }) => data)
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to delete conversation: ${error.message}`)
    }
  }

  /**
   * List messages in a conversation
   */
  async listMessages(
    conversationId: string,
    limit: number = 100,
    offset: number = 0
  ): Promise<AIApiResponse<Message[]>> {
    try {
      const params = { limit, offset }
      const response = await apiCall(
        'GET',
        API_URLS.AI_CONVERSATION_MESSAGES(conversationId),
        params
      ).then(({ data }) => data)
      return response.data
    } catch (error: any) {
      throw new Error(`Failed to fetch messages: ${error.message}`)
    }
  }

  /**
   * Send a message to the AI assistant
   */
  async sendMessage({
    conversationId,
    ...request
  }: SendMessageRequest): Promise<AIApiResponse<any>> {
    try {
      const response = await apiCall(
        'POST',
        API_URLS.AI_CONVERSATION_MESSAGES(conversationId),
        request
      ).then(({ data }) => data)

      return response.data
    } catch (error: any) {
      throw new Error(`Failed to send message: ${error.message}`)
    }
  }

  /**
   * Update message content
   */
  async updateMessageContent(
    conversationId: string,
    messageId: string,
    content: string
  ): Promise<Message> {
    try {
      const response = await apiCall(
        'PATCH',
        `${API_URLS.AI_CONVERSATION_MESSAGES(
          conversationId
        )}/${messageId}/content`,
        { content }
      ).then(({ data }) => data)

      return response.data
    } catch (error: any) {
      throw new Error(`Failed to update message content: ${error.message}`)
    }
  }

  /**
   * Send a streaming message to the AI assistant using Server-Sent Events
   * The POST /v1/conversations/:id/messages endpoint returns an event stream directly
   */
  async sendStreamingMessage(
    request: SendMessageRequest,
    onChunk: (event: MessageEvent) => void,
    onComplete: () => void,
    onError: (error: Error) => void
  ): Promise<void> {
    try {
      // Create EventSource directly to the messages endpoint
      const url = API_URLS.AI_CONVERSATION_MESSAGES(request.conversationId)

      // We need to make a streaming request to the same endpoint
      await this.createStreamingRequest(
        url,
        request,
        onChunk,
        onComplete,
        onError
      )
    } catch (error: any) {
      onError(new Error(`Streaming failed: ${error.message}`))
    }
  }

  /**
   * Create a streaming request using fetch with ReadableStream
   */
  private async createStreamingRequest(
    url: string,
    messageRequest: SendMessageRequest,
    onChunk: (event: MessageEvent) => void,
    onComplete: () => void,
    onError: (error: Error) => void
  ): Promise<AbortController> {
    const controller = new AbortController()
    try {
      // Get auth token and project code for headers
      const accessToken = localStorage.getItem('accessToken')
      const projectCode = extractProjectCode()

      await fetchEventSource(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ProjectCode: projectCode || '',
          Authentication: `Bearer ${accessToken}`,
          Authorization: `Bearer ${accessToken}`,
          Accept: 'text/event-stream',
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify(messageRequest),
        signal: controller.signal,
        onmessage: (event) => {
          console.log(event)
          onChunk(JSON.parse(event.data) as MessageEvent)
        },
        onclose: onComplete,
        onerror: (e) => {
          throw e
        },
      })
    } catch (error: any) {
      console.error(error)
      onError(error)
    }
    return controller
  }
}

const AIService = new _AIService()
export default AIService

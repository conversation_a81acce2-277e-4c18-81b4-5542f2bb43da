import { API_URLS } from "../constants";
import { apiCall } from "../helper/api/aloApi";

const AppCommonService = {
    async getData(url) {
        const res = await apiCall("GET", url)
        return res.data
    },
    async getData2List(url1, url2) {
        const [res1, res2] = await Promise.all([
            apiCall("GET", url1),
            apiCall("GET", url2)
        ]);
        return [res1.data, res2.data]
    },
    async getData3List(url1, url2, url3) {
        const [res1, res2, res3] = await Promise.all([
            apiCall("GET", url1),
            apiCall("GET", url2),
            apiCall("GET", url3)
        ]);
        return [res1.data, res2.data, res3.data]
    },
    async validateSRS(assignee) {
        const result = await apiCall('GET', API_URLS.VALIDATE_SRS + assignee);
        return result.data;
    },
    async getMentions(isCommon) {
        const res = await apiCall("GET", isCommon ? API_URLS.COMMON_MENTIONS : API_URLS.MENTIONS)
        return res
    },
    async getMembers() {
        const members = await apiCall("GET", `${API_URLS.REFERENCES_MEMBERS}?Role=BA`)
        return members
    },
    async getMembersBA() {
        const members = await apiCall("GET", `${API_URLS.REFERENCES_MEMBERS}?Role=BA&Role=BA lead`)
        return members
    },
    async getMembersBACustomers() {
        const members = await apiCall("GET", `${API_URLS.REFERENCES_MEMBERS}?Role=BA&Role=Customer`)
        return members
    },
    async getCustomers() {
        const members = await apiCall("GET", `${API_URLS.REFERENCES_MEMBERS}?Role=Customer`)
        return members
    },
    async getBaLeadPm() {
        const members = await apiCall("GET", `${API_URLS.REFERENCES_MEMBERS}?Role=BA lead&Role=PM`)
        return members
    },
    async assignTask(data) {
        const res = await apiCall("PUT", `${API_URLS.ASSIGN_TASK}`, data)
        return res
    },
    async getCommonComponents() {
        const res = await apiCall("GET", API_URLS.SELECT_COMMON_COMPONENT)
        return res
    },
    async getComponentArtefacts(id) {
        const res = await apiCall("GET", API_URLS.SELECT_COMMON_COMPONENT + '/' + id)
        return res
    },
    async selectCommonComponent(componentId) {
        const res = await apiCall("POST", `${API_URLS.SELECT_COMMON_COMPONENT}/${componentId}/select`)
        return res
    },
    async viewDetailArtefact(artefactType, componentId, itemId) {
        const res = await apiCall("GET", `${API_URLS.SELECT_COMMON_COMPONENT}/${componentId}/${artefactType}/${itemId}`)
        return res
    },
    async viewDetailCommonArtefact(url, itemId) {
        const res = await apiCall("GET", `${url}/${itemId}`)
        return res
    },
    async previewImage(id, isCommon = false) {
        const url = `${isCommon ? API_URLS.COMMON_ATTACHMENTS : API_URLS.ATTACHMENTS}/${id}/Preview`;
        const res = await apiCall("GET", url)
        return res
    },
    async downloadFile(id, isCommon = false) {
        const url = `${isCommon ? API_URLS.COMMON_ATTACHMENTS : API_URLS.ATTACHMENTS}/${id}/Download`;
        const res = await apiCall("GET", url, undefined, undefined, true)
        return res
    },
    async downloadTemplate(url) {
        const res = await apiCall('GET', url, undefined, undefined, true);
        return res
    },
    async exportQualityReport() {
        const targetUrl = API_URLS.QUALITY_REPORT_EXPORT;
        const result = await apiCall('GET', targetUrl, undefined, undefined, true);
        return result;
    },
    async getReferences(id) {
        const res = await apiCall("GET", API_URLS.GET_REFERENCES + '/' + id)
        return res
    },
    async getListLeftMenu(url) {
        const res = await apiCall("GET", url)
        return res
    },
    async getImpact(url) {
        const res = await apiCall("GET", url)
        return res
    },
    async getImpactCountCR(artefactType, artefactId) {
        const res = await apiCall("GET", `${API_URLS.REFERENCE_IMPACT}/count?artefactType=${artefactType}&artefactId=${artefactId}`)
        return res
    },
    async getReferenceEpic() {
        const epics = await apiCall("GET", `${API_URLS.REFERENCES_EPIC}`)
        return epics
    },
    async getReferenceSprint() {
        const sprints = await apiCall("GET", `${API_URLS.REFERENCES_SPRINT}`)
        return sprints
    },
    async getReferenceProduct() {
        const products = await apiCall("GET", `${API_URLS.REFERENCES_PRODUCT}`)
        return products
    },
    async uploadFile(url, data) {
        const products = await apiCall("POST", url, data)
        return products
    },
    async getReferenceTo(url) {
        const data = await apiCall("GET", url)
        return data
    },
    async saveConfig(url, dataConfig) {
        const data = await apiCall("PUT", url, dataConfig)
        return data
    },

    async updateComment(payload) {
        const data = await apiCall("PUT", `${API_URLS.UPDATE_COMMENT}/${payload.id}`, payload)
        return data
    },
    async resolveComment(payload) {
        const data = await apiCall("PUT", `${API_URLS.UPDATE_COMMENT}/${payload.commentId}/resolve`, payload)
        return data
    },
    async closeComment(payload) {
        const data = await apiCall("PUT", `${API_URLS.UPDATE_COMMENT}/${payload.commentId}/close`, payload)
        return data
    },

    async reopenComment(payload) {
        const data = await apiCall("PUT", `${API_URLS.UPDATE_COMMENT}/${payload.commentId}/reopen`, payload)
        return data
    },
    async cancelComment(payload) {
        const data = await apiCall("PUT", `${API_URLS.UPDATE_COMMENT}/${payload.commentId}/cancel`, payload)
        return data
    },
    async deleteComment(payload) {
        const data = await apiCall("DELETE", `${API_URLS.UPDATE_COMMENT}/${payload}`, payload)
        return data
    },
    async replyComment(payload) {
        const data = await apiCall("PUT", `${API_URLS.UPDATE_COMMENT}/${payload.commentId}/reply`, payload)
        return data
    },

    async updateReplyComment(payload) {
        const data = await apiCall("PUT", `${API_URLS.UPDATE_COMMENT}/reply/${payload?.commentId}`, payload)
        return data
    },

    async deleteReplyComment(payload) {
        const data = await apiCall("DELETE", `${API_URLS.ADD_UPDATE_DELETE_REPLY}/${payload}`)
        return data
    },
    async updateAssignTaskInfor(payload) {
        const data = await apiCall("PUT", `${API_URLS.CHANG_ASSIGN_TASK}?artefactType=${payload?.artefactType}&artefactId=${payload?.artefactId}`, payload.data)
        return data
    },

    async getObjectProperties(payload, isCommon = false) {
        const products = await apiCall("GET", `${isCommon ? API_URLS.COMMON_REFERENCE_OBJECTS : API_URLS.REFERENCES_OBJECTS}/${payload}/${isCommon ? 'properties' : 'objectproperties'}`)
        return products
    },
    async updateVersion(payload, artefactType, artefactId) {
        const products = await apiCall("POST", `${API_URLS.UPDATE_VERSION}?artefactType=${artefactType}&artefactId=${artefactId}`, payload)
        return products
    },
    
    async restoreVersionHistory(baseUrl, payload, artefactType, artefactId) {
        const versionHistory = await apiCall("PUT", `${baseUrl}/${artefactType}/${artefactId}/history`, payload)
        return versionHistory
    },

    async createProject(payload) {
        const data = await apiCall("POST", API_URLS.ADMIN_PROJECT, payload)
        return data
    },

    async getDetailProject(payload) {
        const data = await apiCall("GET", API_URLS.ADMIN_PROJECT + '/' + payload, )
        return data
    },

    async updateProject(code, payload) {
        const data = await apiCall("PUT", API_URLS.ADMIN_PROJECT + '/' + code, payload)
        return data
    },
    async syncDataMart(jobName: string) {
        const data = await apiCall("GET", API_URLS.SYNC_DATA_MART + '?jobName=' + jobName)
        return data
    },
    async getComponents() {
        const products = await apiCall("GET", `${API_URLS.COMPONENTS_JIRA}`)
        return products
    },
}

export default AppCommonService

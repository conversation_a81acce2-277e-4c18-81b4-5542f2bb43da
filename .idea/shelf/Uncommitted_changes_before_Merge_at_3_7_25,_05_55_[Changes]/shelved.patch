Index: src/modules/_shared/ai/README.md
===================================================================
diff --git a/src/modules/_shared/ai/README.md b/src/modules/_shared/ai/README.md
deleted file mode 100644
--- a/src/modules/_shared/ai/README.md	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ /dev/null	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
@@ -1,214 +0,0 @@
-# AI Chat Assistant Integration
-
-This module provides a complete integration with the AI Chat Assistant backend API based on the OpenAPI specification.
-
-## Features
-
-- ✅ **Real API Integration**: Connects to the actual AI backend API
-- ✅ **Multiple Agent Types**: Support for leader_agent, ur_agent, and hlr_agent
-- ✅ **Multiple AI Models**: Support for gpt-4.1-mini, gpt-4.1, and gpt-o4
-- ✅ **Streaming Support**: Real-time streaming responses from the AI
-- ✅ **Settings Panel**: User-configurable agent types and models
-- ✅ **Error Handling**: Graceful fallback to mock responses when API is unavailable
-- ✅ **Type Safety**: Full TypeScript support with OpenAPI-based types
-- ✅ **Comprehensive Testing**: Unit tests, integration tests, and manual test runner
-
-## Architecture
-
-### Service Layer (`ai-assistant.service.ts`)
-- Handles all API communication
-- Transforms API responses to internal format
-- Provides streaming support
-- Includes error handling and fallbacks
-
-### Redux Integration
-- **Actions**: All AI-related actions including streaming support
-- **Reducer**: State management for conversations, messages, and UI state
-- **Saga**: Handles async operations and API calls
-
-### UI Components
-- **AIChatBox**: Main chat interface
-- **AISettings**: Configuration panel for agent types and models
-- **AIAssistant**: Tabbed interface combining chat and settings
-
-## API Endpoints
-
-Based on the OpenAPI specification:
-
-- `GET /v1/health` - Health check
-- `GET /v1/agents` - List available agents
-- `GET /v1/conversations` - List conversations
-- `POST /v1/conversations/{conversation_id}/messages` - Send message
-- `DELETE /v1/conversations/{conversation_id}` - Delete conversation
-
-## Configuration
-
-### Environment Variables
-
-Add to your `.env` file:
-```
-REACT_APP_API_AI_ASSISTANT=https://ai-assistant-api.ops-ai.dev
-```
-
-### Agent Types
-
-- **leader_agent**: Strategic analysis and high-level decision making
-- **ur_agent**: Specialized in user requirements analysis  
-- **hlr_agent**: Focus on high-level requirement specification
-
-### AI Models
-
-- **gpt-4.1-mini**: Fast and efficient for simple tasks
-- **gpt-4.1**: Balanced performance and capability
-- **gpt-o4**: Most advanced model for complex analysis
-
-## Usage
-
-### Basic Chat Integration
-
-```tsx
-import { AIChatBox } from './modules/_shared/ai/components/chatbox'
-
-function MyComponent() {
-  return (
-    <AIChatBox 
-      isVisible={true} 
-      onClose={() => {}} 
-    />
-  )
-}
-```
-
-### Settings Panel
-
-```tsx
-import AISettings from './modules/_shared/ai/components/ai-settings'
-
-function SettingsPage() {
-  return (
-    <AISettings 
-      onSettingsChange={(settings) => console.log(settings)} 
-    />
-  )
-}
-```
-
-### Redux Integration
-
-```tsx
-import { useDispatch } from 'react-redux'
-import { sendMessageRequest } from './modules/_shared/ai/actions'
-
-function sendMessage() {
-  dispatch(sendMessageRequest({
-    content: 'Hello AI',
-    conversationId: 'conv-123',
-    agentType: 'leader_agent',
-    projectId: 'my-project'
-  }))
-}
-```
-
-## Testing
-
-### Running Tests
-
-```bash
-# Run all AI tests
-npm test -- --testPathPattern=ai
-
-# Run specific test files
-npm test ai-assistant.service.test.ts
-npm test reducer.test.ts
-npm test integration.test.ts
-```
-
-### Manual Testing
-
-Use the built-in test runner:
-
-```typescript
-import { runAIIntegrationTests } from './modules/_shared/ai/test-runner'
-
-// Run all integration tests
-await runAIIntegrationTests()
-```
-
-Or in browser console:
-```javascript
-window.runAITests()
-```
-
-## Error Handling
-
-The integration includes multiple layers of error handling:
-
-1. **API Level**: Service layer catches and transforms API errors
-2. **Saga Level**: Fallback to mock responses when API is unavailable
-3. **UI Level**: User-friendly error messages and loading states
-
-## Streaming Support
-
-The integration supports real-time streaming responses:
-
-```typescript
-// Enable streaming in settings
-const settings = {
-  agentType: 'leader_agent',
-  model: 'gpt-4.1',
-  stream: true
-}
-
-// Messages will be streamed in real-time to the UI
-```
-
-## Development
-
-### Adding New Agent Types
-
-1. Update the `AgentType` enum in `types.ts`
-2. Add the new agent to the `AGENT_TYPES` array in `ai-settings.tsx`
-3. Update tests to include the new agent type
-
-### Adding New Models
-
-1. Update the `AvaiModel` enum in `types.ts`
-2. Add the new model to the `AI_MODELS` array in `ai-settings.tsx`
-3. Update tests to include the new model
-
-### Extending API Functionality
-
-1. Add new methods to `ai-assistant.service.ts`
-2. Create corresponding actions in `actions.ts`
-3. Update the reducer to handle new actions
-4. Add saga handlers for async operations
-5. Write tests for new functionality
-
-## Troubleshooting
-
-### API Connection Issues
-
-1. Check environment variables are set correctly
-2. Verify API endpoint is accessible
-3. Check network connectivity
-4. Review browser console for CORS issues
-
-### Authentication Issues
-
-1. Ensure access tokens are valid
-2. Check project code is set correctly
-3. Verify API permissions
-
-### Performance Issues
-
-1. Consider disabling streaming for slower connections
-2. Reduce message history length
-3. Optimize component re-renders
-
-## Contributing
-
-1. Follow the existing code structure
-2. Add tests for new functionality
-3. Update documentation
-4. Ensure TypeScript types are correct
-5. Test with both real API and fallback modes
Index: src/layout/normal-layout/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { CommentState } from '@/modules/_shared/comment/type'\nimport { AIAssistantState } from '@/modules/_shared/ai/types'\nimport { DoubleLeftOutlined, DoubleRightOutlined } from '@ant-design/icons'\nimport { Button, Layout, Spin, notification } from 'antd'\nimport { saveAs } from 'file-saver'\nimport moment from 'moment'\nimport { FC, useEffect, useState } from 'react'\nimport { GlobalHotKeys } from 'react-hotkeys'\nimport { useSelector } from 'react-redux'\nimport { useHistory } from 'react-router-dom'\nimport APP_MENU from '../../config/app.menu.config'\nimport intl from '../../config/locale.config'\nimport {\n  API_URLS,\n  APP_ROLES,\n  APP_ROUTES,\n  MESSAGE_TYPE,\n  PROJECT_PREFIX,\n} from '../../constants'\nimport {\n  ShowAppMessage,\n  currentUserName,\n  extractProjectCode,\n  hasRole,\n} from '../../helper/share'\nimport SharedComment from '../../modules/_shared/comment'\nimport GenerateSrs from '../../modules/generate-srs'\nimport { GenerateSrsState } from '../../modules/generate-srs/type'\nimport ValidateScopeButton from '../../modules/validate-srs/button'\nimport TableService from '../../services/lav-table-service'\nimport AppState from '../../store/types'\nimport AppHeader from '../components/header'\nimport AppMenuComponent from '../components/menu'\nimport SelectCommonComponentModal from '../components/select-common-component'\nimport AppVersion from '../components/version'\nimport { AIAssistant } from '../../modules/_shared/ai';\nimport '../style.less'\n\nconst { Content, Sider } = Layout\n\ntype Props = {\n  isCommon?: boolean | false\n  children?: JSX.Element | JSX.Element[]\n  isAdmin?: boolean | false\n}\n\nconst NormalLayout: FC<Props> = (props: Props) => {\n  const [collapsed, setCollapsed] = useState<boolean>(false)\n  const [menus, setMenus] = useState<any[]>([])\n  const history = useHistory()\n  const [showValidateSRSModal, setShowValidateSRSModal] = useState(false)\n  const [showSelectCommonComponentModal, setShowSelectCommonComponentModal] =\n    useState(false)\n  const [showGenerateModal, setShowGenerateModal] = useState(false)\n  const [accessToken, setAccessToken] = useState(null)\n\n  const state = useSelector<AppState | null>(\n    (s) => s?.generateSrs\n  ) as GenerateSrsState\n\n  const commentState = useSelector<AppState | null>(\n    (s) => s?.Comment\n  ) as CommentState\n\n  const aiAssistantState = useSelector<AppState | null>(\n    (s) => s?.aiAssistant\n  ) as AIAssistantState\n\n  useEffect(() => {\n    setMenus(\n      props.isCommon\n        ? APP_MENU.CommonMenuConfig\n        : props.isAdmin\n          ? APP_MENU.AdminMenuConfig\n          : APP_MENU.AppMenuConfig\n    )\n  }, [props])\n\n  const toggleCollapsed = () => {\n    setCollapsed(!collapsed)\n  }\n\n  const handleCustomMenuClick = (e: any, menu: any) => {\n    e.preventDefault()\n    switch (menu.label) {\n      case 'app.menu.utilities.validate_srs': {\n        if (hasRole(APP_ROLES.BA_LEAD) || hasRole(APP_ROLES.PM)) {\n          setShowValidateSRSModal(true)\n        } else {\n          const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.VALIDATION_RESULT\n            }/${currentUserName()}/${new Date().getTime()}`\n          history.push(href)\n        }\n        break\n      }\n      case 'app.menu.utilities.generate_srs': {\n        const href = `${PROJECT_PREFIX}${extractProjectCode()}${APP_ROUTES.GENERATE_SRS\n          }`\n        history.push(href)\n        break\n\n        // (hasRole(APP_ROLES.BA_LEAD) || hasRole(APP_ROLES.PM) || hasRole(APP_ROLES.BA)) ? setShowGenerateModal(true) : setShowGenerateModal(false);\n        // break;\n      }\n      case 'app.menu.utilities.select_from_common_requirement': {\n        setShowSelectCommonComponentModal(true)\n        break\n      }\n      case 'app.menu.utilities.recommend_common_requirement': {\n        break\n      }\n      case 'app.menu.utilities.export_comments': {\n        if (hasRole(APP_ROLES.BA_LEAD) || hasRole(APP_ROLES.PM)) {\n          const reqNoti = notification\n          TableService.export(`${API_URLS.EXPORT_COMMENT}`)\n            .then((res: any) => {\n              var blob = new Blob([res.data])\n              let fName = `${intl.formatMessage({\n                id: `${extractProjectCode()}_Comments`,\n              })}_${moment().format('DDMMYYYYHHMMSS')}.xlsx`\n              saveAs(blob, fName)\n            })\n            .catch((e) => {\n              try {\n                let responseMess: any = e.response.data\n                const uint8Array: any = new Uint8Array(responseMess)\n                const messId = String.fromCharCode.apply(null, uint8Array)\n                reqNoti['error']({\n                  description: intl.formatMessage({ id: messId }),\n                  message: intl.formatMessage({ id: 'common.message.error' }),\n                  placement: 'bottomRight',\n                })\n              } catch (err) {\n                ShowAppMessage(MESSAGE_TYPE.ERROR)\n              }\n            })\n          e.stopPropagation()\n        }\n        break\n      }\n    }\n  }\n\n  const keyMap = { COLLAPSE: '`' }\n  const handlers = {\n    COLLAPSE: (e) => {\n      setCollapsed(!collapsed)\n    },\n  }\n\n  return (\n    <GlobalHotKeys\n      allowChanges={true}\n      className={collapsed ? 'collapsed' : ''}\n      handlers={handlers}\n      keyMap={keyMap}\n    >\n      <Layout>\n        <Spin spinning={state.isLoading}>\n          <AppHeader\n            isAdmin={props.isAdmin || false}\n            showDashboard\n            isCommon={props.isCommon || false}\n            accessTokenReceived={setAccessToken}\n          />\n          <Layout\n            className={`page-wrapper ${collapsed ? `collapsed` : 'expaned'} ${aiAssistantState.isAiPanelOpen ? 'ai-panels-open' : ''}`}\n          >\n            <Sider\n              className={\n                collapsed\n                  ? `custom-sider-container-collapsed`\n                  : 'custom-sider-container'\n              }\n              trigger={null}\n              collapsible\n              collapsed={collapsed}\n            >\n              <div className=\"sider-menu-wrapper\">\n                <div className=\"sidebar-content\">\n                  {props.isCommon ? (\n                    <div\n                      className={\n                        collapsed\n                          ? 'project-and-collapse-collapsed'\n                          : 'project-and-collapse'\n                      }\n                    >\n                      {collapsed ? null : (\n                        <span className=\"project-text\">\n                          {intl.formatMessage({\n                            id: 'app.menu.common_requirement',\n                          })}\n                        </span>\n                      )}\n                    </div>\n                  ) : !props.isAdmin ? (\n                    <>\n                      <div\n                        className={\n                          collapsed\n                            ? 'project-and-collapse-collapsed'\n                            : 'project-and-collapse'\n                        }\n                      >\n                        {collapsed ? null : (\n                          <span className=\"project-text\">\n                            {intl.formatMessage({ id: 'app.menu.project' })}\n                          </span>\n                        )}\n                      </div>\n                      <div className=\"project-name\">\n                        <div className=\"project-logo\">\n                          <img\n                            src=\"https://insight.fsoft.com.vn/jira9/secure/projectavatar?avatarId=10324\"\n                            alt=\"\"\n                          />\n                        </div>\n                        <span style={collapsed ? { visibility: 'hidden' } : {}}>\n                          {extractProjectCode()}\n                        </span>\n                      </div>\n                    </>\n                  ) : (\n                    <></>\n                  )}\n                  <AppMenuComponent\n                    collapsed={collapsed}\n                    menus={menus}\n                    isAdmin={props.isAdmin}\n                    isCommon={props.isCommon}\n                    onCustomMenuClick={handleCustomMenuClick}\n                  />\n                </div>\n                <div\n                  className={`siderbar-fixed-bottom ${collapsed\n                    ? 'project-and-collapse-collapsed'\n                    : 'project-and-collapse'\n                    }`}\n                >\n                  <AppVersion />\n                  <Button\n                    type=\"text\"\n                    onClick={toggleCollapsed}\n                    icon={\n                      collapsed ? (\n                        <DoubleRightOutlined />\n                      ) : (\n                        <DoubleLeftOutlined />\n                      )\n                    }\n                  />\n                </div>\n              </div>\n            </Sider>\n            <Content\n              className=\"main-content-view\"\n              style={{ position: 'relative' }}\n            >\n              {accessToken ? (\n                <>\n                  {props.children}\n                  {showValidateSRSModal ? (\n                    <ValidateScopeButton\n                      onDismiss={() => setShowValidateSRSModal(false)}\n                    />\n                  ) : (\n                    <></>\n                  )}\n                  {showSelectCommonComponentModal ? (\n                    <SelectCommonComponentModal\n                      onDismiss={() => setShowSelectCommonComponentModal(false)}\n                    />\n                  ) : (\n                    <></>\n                  )}\n                  {/* {showGenerateModal ? <GenerateSrs onDismiss={() => setShowGenerateModal(false)} /> : <></>} */}\n                  {showGenerateModal ? (\n                    <GenerateSrs\n                      onDismiss={() => setShowGenerateModal(false)}\n                    />\n                  ) : (\n                    <></>\n                  )}\n                </>\n              ) : (\n                <></>\n              )}\n            </Content>\n          </Layout>\n        </Spin>\n        <SharedComment\n          artefact={commentState.artefact}\n          field={commentState.field}\n          title={commentState.title}\n          index={commentState.index}\n          isVisible={commentState.isVisible}\n          comments={commentState.comments}\n          projectId={commentState.projectId}\n          itemId={commentState.itemId}\n        />\n        {/* AI Assistant Components */}\n        <AIAssistant />\n      </Layout>\n    </GlobalHotKeys>\n  )\n}\nexport default NormalLayout\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/layout/normal-layout/index.tsx b/src/layout/normal-layout/index.tsx
--- a/src/layout/normal-layout/index.tsx	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/src/layout/normal-layout/index.tsx	(date 1751496906057)
@@ -300,7 +300,7 @@
           itemId={commentState.itemId}
         />
         {/* AI Assistant Components */}
-        <AIAssistant />
+        <AIAssistant collapsed={collapsed} />
       </Layout>
     </GlobalHotKeys>
   )
Index: src/locales/en-US.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import request from './en-US/request'\nimport viewobject from './en-US/viewObject'\nimport actor from './en-US/actor'\nimport common from './en-US/commonlocale'\nimport msg from './en-US/message'\nimport viewobjectSpecification from './en-US/viewObjectSpecification'\nimport createObject from './en-US/createObject'\nimport updateObject from './en-US/updateObject'\nimport viewScreenList from './en-US/viewScreenList'\nimport viewUseCaseDetail from './en-US/viewUseCaseDetail'\nimport viewScreenDetails from './en-US/viewScreenDetails'\nimport viewFunction from './en-US/view-function'\nimport createFunction from './en-US/create-function'\nimport createScreen from './en-US/createScreen'\nimport workFlow from './en-US/viewWorkFlow'\nimport viewStateTransition from './en-US/view-state-transition'\nimport createStateTransition from './en-US/create-state-transition'\nimport mess from './en-US/mess'\nimport mail from './en-US/email'\nimport viewDataMigration from './en-US/view-data-migration'\nimport viewNonFunctional from './en-US/view-non-functional'\nimport createNonFunctional from './en-US/create-non-functional'\nimport userRequirement from './en-US/user-requirement'\nimport viewObjectRelationship from './en-US/view-object-relationship'\nimport meeting from './en-US/meeting'\nimport viewBusinessRule from './en-US/view-business-rule'\nimport viewUsecaseDiagram from './en-US/view-usecase-diagram'\nimport reference_document from './en-US/reference_document'\nimport viewOtherRequirement from './en-US/view-other-requirement'\nimport generateSrs from './en-US/generate-srs'\nimport dashboard from './en-US/dashboard'\nimport projectManagement from './en-US/project-management'\nimport common_committee from './en-US/common_committee'\nimport common_usercase from './en-US/common_usercase'\nimport commonComponent from './en-US/common-component'\nimport commonObject from './en-US/common-object'\nimport commonScreen from './en-US/common-screen'\nimport menu from './en-US/menu'\nimport myAssignedTask from './en-US/my_assigned_task'\nimport validate_srs from './en-US/validate-srs'\nimport select_common_component from './en-US/select-common-component'\nimport myPendingReviewTask from './en-US/my-pending-review-task'\nimport recommend_common_component from './en-US/recommend-common-component'\nimport related_links from './en-US/related-links'\nimport qualityReport from './en-US/quality-report'\nimport common_nonFunctionalRequirement from './en-US/common_non-functional-requirement'\nimport commonmessage from './en-US/common-message'\nimport effortEstimation from './en-US/effort_estimation'\nimport epicManagement from './en-US/epic-management'\nimport sprint_management from './en-US/sprint_management'\nimport user_story from './en-US/user-story'\nimport recommended from './en-US/recommenedcommonrequirement'\nimport glossary from './en-US/glossary'\nimport viewVersionHistory from './en-US/viewVersionHistory'\nexport default {\n  ...viewVersionHistory,\n  ...glossary,\n  ...recommended,\n  ...commonmessage,\n  ...projectManagement,\n  ...request,\n  ...viewobject,\n  ...actor,\n  ...msg,\n  ...common,\n  ...viewobjectSpecification,\n  ...createObject,\n  ...updateObject,\n  ...viewScreenList,\n  ...viewUseCaseDetail,\n  ...viewFunction,\n  ...createFunction,\n  ...createScreen,\n  ...viewScreenDetails,\n  ...workFlow,\n  ...viewStateTransition,\n  ...createStateTransition,\n  ...mess,\n  ...mail,\n  ...viewDataMigration,\n  ...viewNonFunctional,\n  ...createNonFunctional,\n  ...userRequirement,\n  ...viewObjectRelationship,\n  ...meeting,\n  ...viewBusinessRule,\n  ...viewUsecaseDiagram,\n  ...reference_document,\n  ...viewOtherRequirement,\n  ...generateSrs,\n  ...dashboard,\n  ...commonObject,\n  ...common_committee,\n  ...commonComponent,\n  ...commonScreen,\n  ...common_usercase,\n  ...common_nonFunctionalRequirement,\n  ...menu,\n  ...myAssignedTask,\n  ...myPendingReviewTask,\n  ...validate_srs,\n  ...select_common_component,\n  ...recommend_common_component,\n  ...related_links,\n  ...qualityReport,\n  ...effortEstimation,\n  ...epicManagement,\n  ...sprint_management,\n  ...user_story\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/locales/en-US.ts b/src/locales/en-US.ts
--- a/src/locales/en-US.ts	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/src/locales/en-US.ts	(date 1751496906057)
@@ -52,6 +52,7 @@
 import recommended from './en-US/recommenedcommonrequirement'
 import glossary from './en-US/glossary'
 import viewVersionHistory from './en-US/viewVersionHistory'
+import ai from './en-US/ai'
 export default {
   ...viewVersionHistory,
   ...glossary,
@@ -106,5 +107,6 @@
   ...effortEstimation,
   ...epicManagement,
   ...sprint_management,
-  ...user_story
+  ...user_story,
+  ...ai
 }
Index: src/services/ai-assistant.service.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { apiCall } from '../helper/api/aloApi'\nimport { API_URLS } from '../constants'\nimport { extractProjectCode } from '../helper/share'\nimport {\n  MessageRequest,\n  AgentType,\n  AvaiModel,\n  AIConversation,\n  AIMessage,\n  SendMessageRequest,\n  CreateConversationRequest,\n  TeamRunResponseContentEvent\n} from '../modules/_shared/ai/types'\n// Note: EventStreamService not needed since we're using fetch directly\n\nexport interface AIApiResponse<T = any> {\n  data: T\n  status: number\n  message?: string\n}\n\nexport interface ConversationResponse {\n  status_code: number\n  message: string\n  conversation_id: string\n  title: string\n  created_at: string\n}\n\nexport interface MessageResponse {\n  id: string\n  content: string\n  role: 'user' | 'assistant' | 'system'\n  timestamp: string\n  tokens?: number\n}\n\nexport interface ListConversationsResponse {\n  conversations: ConversationResponse[]\n  total: number\n  limit: number\n  offset: number\n}\n\nexport interface ListMessagesResponse {\n  messages: MessageResponse[]\n  total: number\n  limit: number\n  offset: number\n}\n\nexport interface SendMessageResponse {\n  message: MessageResponse\n  conversation_id: string\n  tokens_used: number\n}\n\nclass AIAssistantService {\n  private getProjectId(): string {\n    return extractProjectCode() || 'default-project'\n  }\n\n  /**\n   * Check AI API health\n   */\n  async checkHealth(): Promise<AIApiResponse> {\n    try {\n      const response = await apiCall('GET', API_URLS.AI_HEALTH)\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Health check failed: ${error.message}`)\n    }\n  }\n\n  /**\n   * Get available agents\n   */\n  async getAgents(): Promise<AIApiResponse<string[]>> {\n    try {\n      const response = await apiCall('GET', API_URLS.AI_AGENTS)\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to fetch agents: ${error.message}`)\n    }\n  }\n\n  /**\n   * List all conversations\n   */\n  async listConversations(limit: number = 50, offset: number = 0): Promise<AIApiResponse<ListConversationsResponse>> {\n    try {\n      const params = { limit, offset }\n      const response = await apiCall('GET', API_URLS.AI_CONVERSATIONS, params)\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to fetch conversations: ${error.message}`)\n    }\n  }\n\n  /**\n   * Create a new conversation\n   */\n  async createConversation(request: CreateConversationRequest): Promise<AIApiResponse<ConversationResponse>> {\n    try {\n      const requestBody = {\n        project_id: request.projectId\n      }\n\n      const response = await apiCall('POST', API_URLS.AI_CONVERSATIONS, requestBody)\n\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to create conversation: ${error.message}`)\n    }\n  }\n\n  /**\n   * Delete a conversation\n   */\n  async deleteConversation(conversationId: string): Promise<AIApiResponse> {\n    try {\n      const response = await apiCall('DELETE', API_URLS.AI_DELETE_CONVERSATION(conversationId))\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to delete conversation: ${error.message}`)\n    }\n  }\n\n  /**\n   * List messages in a conversation\n   */\n  async listMessages(\n    conversationId: string,\n    limit: number = 100,\n    offset: number = 0\n  ): Promise<AIApiResponse<ListMessagesResponse>> {\n    try {\n      const params = { limit, offset }\n      const response = await apiCall('GET', API_URLS.AI_CONVERSATION_MESSAGES(conversationId), params)\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to fetch messages: ${error.message}`)\n    }\n  }\n\n  /**\n   * Send a message to the AI assistant\n   */\n  async sendMessage(request: SendMessageRequest): Promise<AIApiResponse<any>> {\n    try {\n      const messageRequest: MessageRequest = {\n        message: request.content,\n        stream: request.stream ?? true,\n        model: request.model ?? 'gpt-4.1',\n        agent_type: request.agentType,\n        project_id: request.projectId\n      }\n\n      const response = await apiCall(\n        'POST',\n        API_URLS.AI_CONVERSATION_MESSAGES(request.conversationId),\n        messageRequest\n      )\n\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to send message: ${error.message}`)\n    }\n  }\n\n  /**\n   * Send a streaming message to the AI assistant using Server-Sent Events\n   * The POST /v1/conversations/:id/messages endpoint returns an event stream directly\n   */\n  async sendStreamingMessage(\n    request: SendMessageRequest,\n    onChunk: (event: TeamRunResponseContentEvent) => void,\n    onComplete: () => void,\n    onError: (error: Error) => void\n  ): Promise<void> {\n    try {\n      const messageRequest: MessageRequest = {\n        message: request.content,\n        stream: true,\n        model: request.model ?? 'gpt-4.1',\n        agent_type: request.agentType,\n        project_id: request.projectId\n      }\n\n      // Create EventSource directly to the messages endpoint\n      const url = API_URLS.AI_CONVERSATION_MESSAGES(request.conversationId)\n\n      // We need to make a streaming request to the same endpoint\n      this.createStreamingRequest(url, messageRequest, onChunk, onComplete, onError)\n\n    } catch (error: any) {\n      onError(new Error(`Streaming failed: ${error.message}`))\n    }\n  }\n\n  /**\n   * Create a streaming request using fetch with ReadableStream\n   */\n  private async createStreamingRequest(\n    url: string,\n    messageRequest: MessageRequest,\n    onChunk: (event: TeamRunResponseContentEvent) => void,\n    onComplete: () => void,\n    onError: (error: Error) => void\n  ): Promise<void> {\n    try {\n      // Get auth token and project code for headers\n      const accessToken = localStorage.getItem('accessToken')\n      const projectCode = extractProjectCode()\n\n      const response = await fetch(url, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'ProjectCode': projectCode || '',\n          'Authentication': `Bearer ${accessToken}`,\n          'Authorization': `Bearer ${accessToken}`,\n          'Accept': 'text/event-stream',\n          'Cache-Control': 'no-cache'\n        },\n        body: JSON.stringify(messageRequest)\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`)\n      }\n\n      if (!response.body) {\n        throw new Error('No response body for streaming')\n      }\n\n      const reader = response.body.getReader()\n      const decoder = new TextDecoder()\n\n      try {\n        while (true) {\n          const { done, value } = await reader.read()\n\n          if (done) {\n            onComplete()\n            break\n          }\n\n          // Decode the chunk\n          const chunk = decoder.decode(value, { stream: true })\n\n          // Parse event stream data\n          this.parseEventStreamChunk(chunk, onChunk)\n        }\n      } finally {\n        reader.releaseLock()\n      }\n\n    } catch (error: any) {\n      onError(new Error(`Streaming request failed: ${error.message}`))\n    }\n  }\n\n  /**\n   * Parse event stream chunk and extract TeamRunResponseContent events\n   */\n  private parseEventStreamChunk(\n    chunk: string,\n    onChunk: (event: TeamRunResponseContentEvent) => void\n  ): void {\n    const lines = chunk.split('\\n')\n\n    for (const line of lines) {\n      if (line.startsWith('data: ')) {\n        try {\n          const jsonData = line.substring(6) // Remove 'data: ' prefix\n          const eventData = JSON.parse(jsonData) as TeamRunResponseContentEvent\n\n          // Only handle TeamRunResponseContent events\n          if (eventData.type === 'agent' && eventData.event === 'TeamRunResponseContent') {\n            onChunk(eventData)\n          }\n        } catch (parseError) {\n          console.warn('Failed to parse event line:', line, parseError)\n        }\n      }\n    }\n  }\n\n  /**\n   * Stop any active streaming connection\n   * Note: With fetch streams, the connection is managed by the reader\n   */\n  stopStreaming(): void {\n    // The streaming connection is managed by the fetch reader\n    // and will be automatically closed when the component unmounts\n    // or when the reader is released\n    console.log('Streaming stopped')\n  }\n\n  /**\n   * Legacy streaming method for backward compatibility\n   */\n  async sendStreamingMessageLegacy(\n    request: SendMessageRequest,\n    onChunk: (chunk: string) => void,\n    onComplete: (response: SendMessageResponse) => void,\n    onError: (error: Error) => void\n  ): Promise<void> {\n    try {\n      // For now, simulate streaming with a regular API call\n      // In a real implementation, this would use Server-Sent Events or WebSocket\n      const response = await this.sendMessage(request)\n\n      // Simulate streaming by sending chunks\n      const content = response.data.message.content\n      const chunks = content.split(' ')\n\n      for (let i = 0; i < chunks.length; i++) {\n        setTimeout(() => {\n          onChunk(chunks[i] + ' ')\n          if (i === chunks.length - 1) {\n            onComplete(response.data)\n          }\n        }, i * 100) // 100ms delay between chunks\n      }\n    } catch (error: any) {\n      onError(new Error(`Streaming failed: ${error.message}`))\n    }\n  }\n\n  /**\n   * Transform API conversation response to internal format\n   */\n  transformConversation(apiConversation: ConversationResponse): AIConversation {\n    return {\n      id: apiConversation.conversation_id,\n      title: apiConversation.title,\n      messages: [], // Messages will be loaded separately\n      totalTokens: 0,\n      createdAt: new Date(apiConversation.created_at),\n      updatedAt: new Date(apiConversation.created_at),\n      projectId: this.getProjectId()\n    }\n  }\n\n  /**\n   * Transform API message response to internal format\n   */\n  transformMessage(apiMessage: MessageResponse): AIMessage {\n    return {\n      id: apiMessage.id,\n      content: apiMessage.content,\n      type: apiMessage.role as 'user' | 'assistant' | 'system',\n      timestamp: new Date(apiMessage.timestamp),\n      tokens: apiMessage.tokens\n    }\n  }\n}\n\nexport default new AIAssistantService()\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/services/ai-assistant.service.ts b/src/services/ai-assistant.service.ts
--- a/src/services/ai-assistant.service.ts	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/src/services/ai-assistant.service.ts	(date 1751496906063)
@@ -11,6 +11,7 @@
   CreateConversationRequest,
   TeamRunResponseContentEvent
 } from '../modules/_shared/ai/types'
+import { EventStreamContentType, fetchEventSource } from '@microsoft/fetch-event-source'
 // Note: EventStreamService not needed since we're using fetch directly
 
 export interface AIApiResponse<T = any> {
@@ -228,13 +229,14 @@
     onChunk: (event: TeamRunResponseContentEvent) => void,
     onComplete: () => void,
     onError: (error: Error) => void
-  ): Promise<void> {
+  ): Promise<AbortController> {
+    const controller = new AbortController();
     try {
       // Get auth token and project code for headers
       const accessToken = localStorage.getItem('accessToken')
       const projectCode = extractProjectCode()
 
-      const response = await fetch(url, {
+      fetchEventSource(url, {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json',
@@ -244,42 +246,18 @@
           'Accept': 'text/event-stream',
           'Cache-Control': 'no-cache'
         },
-        body: JSON.stringify(messageRequest)
+        body: JSON.stringify(messageRequest),
+        signal: controller.signal,
+        onmessage: (event) => {
+          onChunk(JSON.parse(event.data) as TeamRunResponseContentEvent)
+        },
+        onclose: onComplete,
+        onerror: onError,
       })
-
-      if (!response.ok) {
-        throw new Error(`HTTP error! status: ${response.status}`)
-      }
-
-      if (!response.body) {
-        throw new Error('No response body for streaming')
-      }
-
-      const reader = response.body.getReader()
-      const decoder = new TextDecoder()
-
-      try {
-        while (true) {
-          const { done, value } = await reader.read()
-
-          if (done) {
-            onComplete()
-            break
-          }
-
-          // Decode the chunk
-          const chunk = decoder.decode(value, { stream: true })
-
-          // Parse event stream data
-          this.parseEventStreamChunk(chunk, onChunk)
-        }
-      } finally {
-        reader.releaseLock()
-      }
-
     } catch (error: any) {
       onError(new Error(`Streaming request failed: ${error.message}`))
     }
+    return controller;
   }
 
   /**
@@ -358,7 +336,9 @@
       id: apiConversation.conversation_id,
       title: apiConversation.title,
       messages: [], // Messages will be loaded separately
-      totalTokens: 0,
+      receiveToken: 0,
+      sentToken: 0,
+      totalCost: 0,
       createdAt: new Date(apiConversation.created_at),
       updatedAt: new Date(apiConversation.created_at),
       projectId: this.getProjectId()
Index: src/modules/_shared/ai/components/message-content.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import React from \"react\";\nimport Markdown from \"react-markdown\";\n\ntype MessageContentProps = {\n  content: string;\n}\n\nexport const MessageContent: React.FC<MessageContentProps> = ({ content }) => {\n  return <Markdown>{content}</Markdown>\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/components/message-content.tsx b/src/modules/_shared/ai/components/message-content.tsx
--- a/src/modules/_shared/ai/components/message-content.tsx	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/src/modules/_shared/ai/components/message-content.tsx	(date 1751496906060)
@@ -1,10 +1,12 @@
+import { LoadingOutlined } from "@ant-design/icons";
 import React from "react";
 import Markdown from "react-markdown";
 
 type MessageContentProps = {
   content: string;
+  isLoading?: boolean;
 }
 
-export const MessageContent: React.FC<MessageContentProps> = ({ content }) => {
-  return <Markdown>{content}</Markdown>
+export const MessageContent: React.FC<MessageContentProps> = ({ content, isLoading }) => {
+  return <><Markdown>{content}</Markdown> {isLoading && <LoadingOutlined />}</>
 }
\ No newline at end of file
Index: src/modules/_shared/ai/components/ai-assistant.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import React from 'react'\nimport { useSelector, useDispatch } from 'react-redux'\nimport { Tabs } from 'antd'\nimport { RobotOutlined, SettingOutlined, CloseOutlined } from '@ant-design/icons'\nimport { AIAssistantState } from '../types'\nimport { toggleAIChatPanel, toggleAISetingsPanel, setActiveTab, closeAllPanels } from '../actions'\nimport { AIChatBox } from './chatbox'\nimport AISettings from './ai-settings'\nimport { RightMenu } from './right-menu'\nimport AppState from '../../../../store/types'\nimport './ai-assistant.less'\n\nconst { TabPane } = Tabs\n\nexport const AIAssistant: React.FC = () => {\n  const dispatch = useDispatch()\n  const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState\n\n  const handleToggleAIChat = () => {\n    dispatch(toggleAIChatPanel())\n  }\n\n  const handleToggleSettings = () => {\n    dispatch(toggleAISetingsPanel())\n  }\n\n  const handleTabChange = (activeKey: string) => {\n    dispatch(setActiveTab(activeKey as 'ai-chat' | 'settings'))\n  }\n\n  const handleCloseAll = () => {\n    dispatch(closeAllPanels())\n  }\n\n  return (\n    <>\n      {/* Right Side Menu - Always visible */}\n      <RightMenu\n        onToggleAIChat={handleToggleAIChat}\n        onToggleSettings={handleToggleSettings}\n        isAIChatOpen={aiState.isAiPanelOpen && aiState.activeTab === 'ai-chat'}\n        isSettingsOpen={aiState.isAiPanelOpen && aiState.activeTab === 'settings'}\n      />\n\n      {/* Tabbed Interface - Shows when any panel is open */}\n      {aiState.isAiPanelOpen && (\n        <div className=\"ai-assistant-tabbed-container\">\n          <div className=\"ai-assistant-tabs-header\">\n            <Tabs\n              activeKey={aiState.activeTab || undefined}\n              onChange={handleTabChange}\n              type=\"card\"\n              size=\"small\"\n              tabBarExtraContent={\n                <div className=\"tab-extra-actions\">\n                  <CloseOutlined\n                    className=\"close-all-btn\"\n                    onClick={handleCloseAll}\n                    title=\"Close All Panels\"\n                  />\n                </div>\n              }\n            >\n              <TabPane\n                tab={\n                  <span>\n                    <RobotOutlined />\n                    BA Vista\n                  </span>\n                }\n                key=\"ai-chat\"\n              />\n              <TabPane\n                tab={\n                  <span>\n                    <SettingOutlined />\n                    Settings\n                  </span>\n                }\n                key=\"settings\"\n              />\n            </Tabs>\n          </div>\n\n          <div className=\"ai-assistant-tab-content\">\n            {aiState.activeTab === 'ai-chat' && (\n              <AIChatBox\n                isVisible={true}\n                onClose={handleToggleAIChat}\n                hideHeader={true}\n              />\n            )}\n            {aiState.activeTab === 'settings' && (\n              <AISettings />\n            )}\n          </div>\n        </div>\n      )}\n    </>\n  )\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/components/ai-assistant.tsx b/src/modules/_shared/ai/components/ai-assistant.tsx
--- a/src/modules/_shared/ai/components/ai-assistant.tsx	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/src/modules/_shared/ai/components/ai-assistant.tsx	(date 1751496906058)
@@ -1,7 +1,7 @@
 import React from 'react'
 import { useSelector, useDispatch } from 'react-redux'
-import { Tabs } from 'antd'
-import { RobotOutlined, SettingOutlined, CloseOutlined } from '@ant-design/icons'
+import { Button, Tabs } from 'antd'
+import { RobotOutlined, SettingOutlined, CloseOutlined, ExpandOutlined } from '@ant-design/icons'
 import { AIAssistantState } from '../types'
 import { toggleAIChatPanel, toggleAISetingsPanel, setActiveTab, closeAllPanels } from '../actions'
 import { AIChatBox } from './chatbox'
@@ -9,10 +9,15 @@
 import { RightMenu } from './right-menu'
 import AppState from '../../../../store/types'
 import './ai-assistant.less'
+import { CanvasEditor } from './canvas-editor'
 
 const { TabPane } = Tabs
 
-export const AIAssistant: React.FC = () => {
+type AIAssistantProps = {
+  collapsed: boolean
+}
+
+export const AIAssistant: React.FC<AIAssistantProps> = ({ collapsed }) => {
   const dispatch = useDispatch()
   const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState
 
@@ -41,7 +46,7 @@
         isAIChatOpen={aiState.isAiPanelOpen && aiState.activeTab === 'ai-chat'}
         isSettingsOpen={aiState.isAiPanelOpen && aiState.activeTab === 'settings'}
       />
-
+      {/* <CanvasEditor onClose={() => { }} onSave={async () => { }} collapsed={collapsed} /> */}
       {/* Tabbed Interface - Shows when any panel is open */}
       {aiState.isAiPanelOpen && (
         <div className="ai-assistant-tabbed-container">
@@ -53,11 +58,8 @@
               size="small"
               tabBarExtraContent={
                 <div className="tab-extra-actions">
-                  <CloseOutlined
-                    className="close-all-btn"
-                    onClick={handleCloseAll}
-                    title="Close All Panels"
-                  />
+                  <Button about='Toggle Full screen' type="text" onClick={() => { }} icon={<ExpandOutlined />} />
+                  <Button about='Close Chat Panel' type="text" onClick={handleCloseAll} icon={<CloseOutlined />} />
                 </div>
               }
             >
Index: package-lock.json
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/package-lock.json b/package-lock.json
--- a/package-lock.json	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/package-lock.json	(date 1751496906054)
@@ -16,12 +16,37 @@
         "@azure/msal-react": "^1.1.0",
         "@ckeditor/ckeditor5-react": "^3.0.3",
         "@craco/craco": "^6.4.2",
+        "@microsoft/fetch-event-source": "^2.0.1",
         "@redux-saga/core": "^1.3.0",
         "@reduxjs/toolkit": "^1.6.2",
         "@svgr/webpack": "^5.5.0",
         "@testing-library/jest-dom": "^5.14.1",
         "@testing-library/react": "^11.2.7",
         "@testing-library/user-event": "^12.8.3",
+        "@tiptap/extension-character-count": "^2.23.0",
+        "@tiptap/extension-code-block-lowlight": "^2.23.0",
+        "@tiptap/extension-color": "^2.23.0",
+        "@tiptap/extension-highlight": "^2.23.0",
+        "@tiptap/extension-horizontal-rule": "^2.23.0",
+        "@tiptap/extension-image": "^2.23.0",
+        "@tiptap/extension-link": "^2.23.0",
+        "@tiptap/extension-mention": "^2.23.0",
+        "@tiptap/extension-placeholder": "^2.23.0",
+        "@tiptap/extension-strike": "^2.23.0",
+        "@tiptap/extension-subscript": "^2.23.0",
+        "@tiptap/extension-superscript": "^2.23.0",
+        "@tiptap/extension-table": "^2.23.0",
+        "@tiptap/extension-table-cell": "^2.23.0",
+        "@tiptap/extension-table-header": "^2.23.0",
+        "@tiptap/extension-table-of-contents": "^2.23.0",
+        "@tiptap/extension-table-row": "^2.23.0",
+        "@tiptap/extension-task-item": "^2.23.0",
+        "@tiptap/extension-task-list": "^2.23.0",
+        "@tiptap/extension-text-align": "^2.23.0",
+        "@tiptap/extension-typography": "^2.23.0",
+        "@tiptap/extension-underline": "^2.23.0",
+        "@tiptap/react": "^2.23.0",
+        "@tiptap/starter-kit": "^2.23.0",
         "@types/jest": "^26.0.24",
         "@types/node": "^12.20.33",
         "@types/react": "^17.0.30",
@@ -45,7 +70,7 @@
         "react-highlight-words": "^0.17.0",
         "react-hotkeys": "^2.0.0",
         "react-intl": "^5.21.0",
-        "react-markdown": "^10.1.0",
+        "react-markdown": "^8.0.6",
         "react-redux": "^7.2.5",
         "react-router": "^5.3.4",
         "react-router-dom": "^5.3.0",
@@ -54,6 +79,7 @@
         "redux": "^4.2.1",
         "redux-injectors": "^1.3.0",
         "redux-saga": "^1.1.3",
+        "tiptap-markdown": "^0.8.1",
         "typescript": "^4.4.4",
         "web-vitals": "^1.1.2",
         "yarn": "^1.22.18"
@@ -6147,6 +6173,11 @@
         "gl-style-validate": "dist/gl-style-validate.mjs"
       }
     },
+    "node_modules/@microsoft/fetch-event-source": {
+      "version": "2.0.1",
+      "resolved": "https://registry.npmjs.org/@microsoft/fetch-event-source/-/fetch-event-source-2.0.1.tgz",
+      "integrity": "sha512-W6CLUJ2eBMw3Rec70qrsEW0jOm/3twwJv21mrmj2yORiaVmVYGS4sSS5yUwvQc1ZlDLYGPnClVWmUUMagKNsfA=="
+    },
     "node_modules/@mrmlnc/readdir-enhanced": {
       "version": "2.2.1",
       "resolved": "https://registry.npmjs.org/@mrmlnc/readdir-enhanced/-/readdir-enhanced-2.2.1.tgz",
@@ -6296,6 +6327,15 @@
         "node": ">= 8"
       }
     },
+    "node_modules/@popperjs/core": {
+      "version": "2.11.8",
+      "resolved": "https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz",
+      "integrity": "sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==",
+      "funding": {
+        "type": "opencollective",
+        "url": "https://opencollective.com/popperjs"
+      }
+    },
     "node_modules/@probe.gl/env": {
       "version": "3.6.0",
       "resolved": "https://registry.npmjs.org/@probe.gl/env/-/env-3.6.0.tgz",
@@ -6411,6 +6451,11 @@
         }
       }
     },
+    "node_modules/@remirror/core-constants": {
+      "version": "3.0.0",
+      "resolved": "https://registry.npmjs.org/@remirror/core-constants/-/core-constants-3.0.0.tgz",
+      "integrity": "sha512-42aWfPrimMfDKDi4YegyS7x+/0tlzaqwPQCULLanv3DMIlu96KTJR0fM5isWX2UViOqlGnX6YFgqWepcX+XMNg=="
+    },
     "node_modules/@rollup/plugin-node-resolve": {
       "version": "7.1.3",
       "resolved": "https://registry.npmjs.org/@rollup/plugin-node-resolve/-/plugin-node-resolve-7.1.3.tgz",
@@ -6796,6 +6841,637 @@
         "@testing-library/dom": ">=7.21.4"
       }
     },
+    "node_modules/@tiptap/core": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/core/-/core-2.23.1.tgz",
+      "integrity": "sha512-EURGKGsEPrwxvOPi9gA+BsczvsECJNV+xgTAGWHmEtU4YJ0AulYrCX3b7FK+aiduVhThIHDoG/Mmvmb/HPLRhQ==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/pm": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-blockquote": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-blockquote/-/extension-blockquote-2.23.1.tgz",
+      "integrity": "sha512-GI3s+uFU88LWRaDG20Z9yIu2av3Usn8kw2lkm2ntwX1K6/mQBS/zkGhWr/FSwWOlMtTzYFxF4Ttb0e+hn67A/A==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-bold": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-bold/-/extension-bold-2.23.1.tgz",
+      "integrity": "sha512-OM4RxuZeOqpYRN1G/YpXSE8tZ3sVtT2XlO3qKa74qf+htWz8W3x4X0oQCrHrRTDSAA1wbmeZU3QghAIHnbvP/A==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-bubble-menu": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-bubble-menu/-/extension-bubble-menu-2.23.1.tgz",
+      "integrity": "sha512-tupuvrlZMTziVZXJuCVjLwllUnux/an9BtTYHpoRyLX9Hg0v7Kh39k9x58zJaoW8Q/Oc/qxPhbJpyOqhE1rLeg==",
+      "dependencies": {
+        "tippy.js": "^6.3.7"
+      },
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-bullet-list": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-bullet-list/-/extension-bullet-list-2.23.1.tgz",
+      "integrity": "sha512-0g9U42m+boLJZP3x9KoJHDCp9WD5abaVdqNbTg9sFPDNsepb7Zaeu8AEB+yZLP/fuTI1I4ko6qkdr3UaaIYcmA==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-character-count": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-character-count/-/extension-character-count-2.23.1.tgz",
+      "integrity": "sha512-QGHiXrLEAuIQCLNqFJrYFsVlnOpdFuhuYoDbxyQe9Z5+twMYI8aO1e4NPKT+cpWtCWFDdnSVrUWZsEGXYEuCjg==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-code": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-code/-/extension-code-2.23.1.tgz",
+      "integrity": "sha512-3IOdE40m0UTR2+UXui69o/apLtutAbtzfgmMxD6q0qlRvVqz99QEfk9RPHDNlUqJtYCL4TD+sj7UclBsDdgVXA==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-code-block": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-code-block/-/extension-code-block-2.23.1.tgz",
+      "integrity": "sha512-eYzJVUR13BhSE/TYAMZihGBId+XiwhnTPqGcSFo+zx89It/vxwDLvAUn0PReMNI7ULKPTw8orUt2fVKSarb2DQ==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-code-block-lowlight": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-code-block-lowlight/-/extension-code-block-lowlight-2.23.1.tgz",
+      "integrity": "sha512-EQwLyO1zKViSXcwdsnzurWOdVS1W3oiNuFnkxxSGtU8hlRbkDP4+dY8c/jRE2XejfJuxz7zY7iGg594d8+5/SA==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/extension-code-block": "^2.7.0",
+        "@tiptap/pm": "^2.7.0",
+        "highlight.js": "^11",
+        "lowlight": "^2 || ^3"
+      }
+    },
+    "node_modules/@tiptap/extension-color": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-color/-/extension-color-2.23.1.tgz",
+      "integrity": "sha512-WkTvez+L/tuqp967PRi8t4L09qwZ5lOWCl8ppVgjjzgAOVpZ7Fgl7hg0h1b7nyO3Vf1JXr396p7iyw4TxKHRrw==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/extension-text-style": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-document": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-document/-/extension-document-2.23.1.tgz",
+      "integrity": "sha512-2nkIkGVsaMJkpd024E6vXK+5XNz8VOVWp/pM6bbXpuv0HnGPrfLdh4ruuFc+xTQ3WPOmpSu8ygtujt4I1o9/6g==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-dropcursor": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-dropcursor/-/extension-dropcursor-2.23.1.tgz",
+      "integrity": "sha512-GyVp+o/RVrKlLdrQvtIpJGphFGogiPjcPCkAFcrfY1vDY1EYxfVZELC96gG1mUT1BO8FUD3hmbpkWi9l8/6O4A==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-floating-menu": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-floating-menu/-/extension-floating-menu-2.23.1.tgz",
+      "integrity": "sha512-GMWkpH+p/OUOk1Y5UGOnKuHSDEVBN7DhYIJiWt5g9LK/mpPeuqoCmQg3RQDgjtZXb74SlxLK2pS/3YcAnemdfQ==",
+      "dependencies": {
+        "tippy.js": "^6.3.7"
+      },
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-gapcursor": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-gapcursor/-/extension-gapcursor-2.23.1.tgz",
+      "integrity": "sha512-iP+TiFIGZEbOvYAs04pI14mLI4xqbt64Da91TgMF1FNZUrG+9eWKjqbcHLQREuK3Qnjn5f0DI4nOBv61FlnPmA==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-hard-break": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-hard-break/-/extension-hard-break-2.23.1.tgz",
+      "integrity": "sha512-YF66EVxnBxt1bHPx6fUUSSXK1Vg+/9baJ0AfJ12hCSPCgSjUclRuNmWIH5ikVfByOmPV1xlrN9wryLoSEBcNRQ==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-heading": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-heading/-/extension-heading-2.23.1.tgz",
+      "integrity": "sha512-5BPoli9wudiAOgSyK8309jyRhFyu5vd02lNChfpHwxUudzIJ/L+0E6FcwrDcw+yXh23cx7F5SSjtFQ7AobxlDQ==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-highlight": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-highlight/-/extension-highlight-2.23.1.tgz",
+      "integrity": "sha512-cvfQ6Ise2aZp0eABRbKNfyfU1Fd304q8nAtAKDCRQKzGbSPTEM4zhCp1RcEmt/93I1cLvKaciQFBoldVl1xaGw==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-history": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-history/-/extension-history-2.23.1.tgz",
+      "integrity": "sha512-1rp2CRjM+P58oGEgeUUDSk0ch67ngIGbGJOOjiBGKU9GIVhI2j4uSwsYTAa9qYMjMUI6IyH1xJpsY2hLKcBOtg==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-horizontal-rule": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-horizontal-rule/-/extension-horizontal-rule-2.23.1.tgz",
+      "integrity": "sha512-uHEF0jpmhtgAxjKw8/s5ipEeTnu99f9RVMGAlmcthJ5Fx9TzH0MvtH4dtBNEu5MXC7+0bNsnncWo125AAbCohg==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-image": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-image/-/extension-image-2.23.1.tgz",
+      "integrity": "sha512-8j2FLBWKq6j27aQcOAlmyKixxHflW8j5FhxLgPPS6hithgFQVET4OYH+1c6r7Qd/T4YoAqt/0PmNZ/gYWI9gzg==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-italic": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-italic/-/extension-italic-2.23.1.tgz",
+      "integrity": "sha512-a+cPzffaC/1AKMmZ1Ka6l81xmTgcalf8NXfBuFCUTf5r7uI9NIgXnLo9hg+jR9F4K+bwhC4/UbMvQQzAjh0c0A==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-link": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-link/-/extension-link-2.23.1.tgz",
+      "integrity": "sha512-zMD0V8djkvwRYACzd8EvFXXNLQH5poJt6aHC9/8uest6njRhlrRjSjwG5oa+xHW4A76XfAH0A5BPj6ZxZnAUQg==",
+      "dependencies": {
+        "linkifyjs": "^4.2.0"
+      },
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-list-item": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-list-item/-/extension-list-item-2.23.1.tgz",
+      "integrity": "sha512-wVrRp6KAiyjFVFGmn+ojisP64Bsd+ZPdqQBYVbebBx1skZeW0uhG60d7vUkWHi0gCgxHZDfvDbXpfnOD0INRWw==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-mention": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-mention/-/extension-mention-2.23.1.tgz",
+      "integrity": "sha512-YaHg2ZqNb2Vur5hBoswzu8pJ3kV54PPonR8gHgelD83T1s8vBqdl03WL2NUVciovEbENnirePLDfhKaFjRXSzQ==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0",
+        "@tiptap/suggestion": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-ordered-list": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-ordered-list/-/extension-ordered-list-2.23.1.tgz",
+      "integrity": "sha512-Zp+qognyNgoaJ9bxkBwIuWJEnQ67RdsHXzv3YOdeGRbkUhd8LT6OL7P0mAuNbMBU8MwHxyJ7C7NsyzwzuVbFzA==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-paragraph": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-paragraph/-/extension-paragraph-2.23.1.tgz",
+      "integrity": "sha512-LLEPizt1ALE7Ek6prlJ1uhoUCT8C/a3PdZpCh3DshM1L3Kv9TENlaJL2GhFl8SVUCwHmWHvXg30+4tIRFBedaQ==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-placeholder": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-placeholder/-/extension-placeholder-2.23.1.tgz",
+      "integrity": "sha512-zSkCljVpxJh3GHW7ppFNYhHPjYKmS3Tw0e74BOGzb5TqP57GRvnPgDGg4fr6kDsSWMo9minnNM3PDA7kNT+PRQ==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-strike": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-strike/-/extension-strike-2.23.1.tgz",
+      "integrity": "sha512-hAT9peYkKezRGp/EcPQKtyYQT+2XGUbb26toTr9XIBQIeQCuCpT+FirPrDMrMVWPwcJt7Rv+AzoVjDuBs9wE0A==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-subscript": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-subscript/-/extension-subscript-2.23.1.tgz",
+      "integrity": "sha512-LFUwe90E90f38aES6ka0Jg7kUG3WTMq3M+7qRu6skEx4+izVB6ub5RTvA56trQlWefWiYeJZptf8xfIKdcwGSw==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-superscript": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-superscript/-/extension-superscript-2.23.1.tgz",
+      "integrity": "sha512-us1p+AZD6B3+eWuhO83WP3Kd9MBSApOh5c4S9MEavmmQxHAvFk7sv/tuyQPgBQNxvNnpV5z0mfFEtqiMrCo22A==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-table": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-table/-/extension-table-2.23.1.tgz",
+      "integrity": "sha512-v8qRKIM74U51KOFK90tpN1Yy1wj7wceifbXXnwySN4H7s0jmOdN//u73QPjqkynWw9xKo/dRjSWqiEoSRhno1w==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-table-cell": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-table-cell/-/extension-table-cell-2.23.1.tgz",
+      "integrity": "sha512-znCUmoZAZvXEw9nqZCzfAC+m++t5PaEh0//cmCqSuvB43c6s2oEFg06JiJnrxWBe8kMZUyzyCHMMoPMLE2vaDQ==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-table-header": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-table-header/-/extension-table-header-2.23.1.tgz",
+      "integrity": "sha512-LIdSdgLPvQ5fGZA3PtV1l6l3dAkgJ5OWsxM0srXBwteHDf9SHMlJ2VJ/X6YtYk3itLkLWeMpqDkPHw9Sq0lbVg==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-table-of-contents": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-table-of-contents/-/extension-table-of-contents-2.23.1.tgz",
+      "integrity": "sha512-WsUqSsCP42Ot5WRHchWzgq3QsxkuhmoCDYgs7X/EtqVhGixT9+fGqPufNAnaAI8v1hMoRNYKZWSH3Gg1BNs2fw==",
+      "dependencies": {
+        "uuid": "^10.0.0"
+      },
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-table-of-contents/node_modules/uuid": {
+      "version": "10.0.0",
+      "resolved": "https://registry.npmjs.org/uuid/-/uuid-10.0.0.tgz",
+      "integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
+      "funding": [
+        "https://github.com/sponsors/broofa",
+        "https://github.com/sponsors/ctavan"
+      ],
+      "bin": {
+        "uuid": "dist/bin/uuid"
+      }
+    },
+    "node_modules/@tiptap/extension-table-row": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-table-row/-/extension-table-row-2.23.1.tgz",
+      "integrity": "sha512-zREF2+tXI8LMapTJVQtbj+SIdzi3hwFte9JugzTDym+16kAPU7Smjsi5wLa7Y1AD8M882OwRWZ9qkHL7L99eZQ==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-task-item": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-task-item/-/extension-task-item-2.23.1.tgz",
+      "integrity": "sha512-v6lNiuKYlEUmoQq2ISzX3dH60ThuALXgKIZclrvVuQsYohHQ2A+5joKEzkdNoWmairNxouAfSa+1p+HEwDaORg==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-task-list": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-task-list/-/extension-task-list-2.23.1.tgz",
+      "integrity": "sha512-JN5Fai/H4PoYaUoqbxoPzUwW9PmN+qmBvbzt3ciCJEG2lXlmF0Z5vGYU/bJ5013C7jTyPJlp0qE7sZAPdF3WdQ==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-text": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-text/-/extension-text-2.23.1.tgz",
+      "integrity": "sha512-XK0D/eyS1Vm5yUrCtkS0AfgyKLJqpi8nJivCOux/JLhhC4x87R1+mI8NoFDYZJ5ic/afREPSBB8jORqOi0qIHg==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-text-align": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-text-align/-/extension-text-align-2.23.1.tgz",
+      "integrity": "sha512-AugfX5iJXM7RJfv/AoXfEmXw70ZFlAIRm0tdSxYwkHvt1f0fqdtdTsHth7OGPnudH91h4FoVgHBktcHcPpEUfg==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-text-style": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-text-style/-/extension-text-style-2.23.1.tgz",
+      "integrity": "sha512-fZn1GePlL27pUFfKXKoRZo4L4pZP9dUjNNiS/eltLpbi/SenJ15UKhAoHtN1KQvNGJsWkYN49FjnnltU8qvQ+Q==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-typography": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-typography/-/extension-typography-2.23.1.tgz",
+      "integrity": "sha512-G8QyK0jTs7VyR7yZPvOGAxU5O90sW7e/N9hmUjsYEY9ttUgFIsFMlihLMXlgqfLtHfgtyzjW5RqU4KVCfpuIQA==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/extension-underline": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-underline/-/extension-underline-2.23.1.tgz",
+      "integrity": "sha512-MTG+VlGStXD3uj7iPzZU8aJrqxoQyX45WX6xTouezaZzh/NQtTyVWwJqNGE7fsMhxirpJ+at0IZmqlDTjAhEDQ==",
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0"
+      }
+    },
+    "node_modules/@tiptap/pm": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/pm/-/pm-2.23.1.tgz",
+      "integrity": "sha512-iAx4rP0k4Xd9Ywh+Gpaz5IWfY2CYRpiwVXWekTHLlNRFtrVIWVpMxaQr2mvRU2g0Ca6rz5w3KzkHBMqrI3dIBA==",
+      "dependencies": {
+        "prosemirror-changeset": "^2.3.0",
+        "prosemirror-collab": "^1.3.1",
+        "prosemirror-commands": "^1.6.2",
+        "prosemirror-dropcursor": "^1.8.1",
+        "prosemirror-gapcursor": "^1.3.2",
+        "prosemirror-history": "^1.4.1",
+        "prosemirror-inputrules": "^1.4.0",
+        "prosemirror-keymap": "^1.2.2",
+        "prosemirror-markdown": "^1.13.1",
+        "prosemirror-menu": "^1.2.4",
+        "prosemirror-model": "^1.23.0",
+        "prosemirror-schema-basic": "^1.2.3",
+        "prosemirror-schema-list": "^1.4.1",
+        "prosemirror-state": "^1.4.3",
+        "prosemirror-tables": "^1.6.4",
+        "prosemirror-trailing-node": "^3.0.0",
+        "prosemirror-transform": "^1.10.2",
+        "prosemirror-view": "^1.37.0"
+      },
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      }
+    },
+    "node_modules/@tiptap/react": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/react/-/react-2.23.1.tgz",
+      "integrity": "sha512-eP8jksq9rY1PBIYKNUgG5c92gCTNK40bmzno+cEAu8RMYs5M6BSXwMaZSjjqHM53Juvj6ake90+7kLOM8XlXfA==",
+      "dependencies": {
+        "@tiptap/extension-bubble-menu": "^2.23.1",
+        "@tiptap/extension-floating-menu": "^2.23.1",
+        "@types/use-sync-external-store": "^0.0.6",
+        "fast-deep-equal": "^3",
+        "use-sync-external-store": "^1"
+      },
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.7.0",
+        "@tiptap/pm": "^2.7.0",
+        "react": "^17.0.0 || ^18.0.0 || ^19.0.0",
+        "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"
+      }
+    },
+    "node_modules/@tiptap/starter-kit": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/starter-kit/-/starter-kit-2.23.1.tgz",
+      "integrity": "sha512-rrImwzJbKSHoFa+WdNU4I0evXcMiQ4yRm737sxvNJwYItT6fXIxrbRT7nJDmtYu2TflcfT1KklEnSrzz1hhYRw==",
+      "dependencies": {
+        "@tiptap/core": "^2.23.1",
+        "@tiptap/extension-blockquote": "^2.23.1",
+        "@tiptap/extension-bold": "^2.23.1",
+        "@tiptap/extension-bullet-list": "^2.23.1",
+        "@tiptap/extension-code": "^2.23.1",
+        "@tiptap/extension-code-block": "^2.23.1",
+        "@tiptap/extension-document": "^2.23.1",
+        "@tiptap/extension-dropcursor": "^2.23.1",
+        "@tiptap/extension-gapcursor": "^2.23.1",
+        "@tiptap/extension-hard-break": "^2.23.1",
+        "@tiptap/extension-heading": "^2.23.1",
+        "@tiptap/extension-history": "^2.23.1",
+        "@tiptap/extension-horizontal-rule": "^2.23.1",
+        "@tiptap/extension-italic": "^2.23.1",
+        "@tiptap/extension-list-item": "^2.23.1",
+        "@tiptap/extension-ordered-list": "^2.23.1",
+        "@tiptap/extension-paragraph": "^2.23.1",
+        "@tiptap/extension-strike": "^2.23.1",
+        "@tiptap/extension-text": "^2.23.1",
+        "@tiptap/extension-text-style": "^2.23.1",
+        "@tiptap/pm": "^2.23.1"
+      },
+      "funding": {
+        "type": "github",
+        "url": "https://github.com/sponsors/ueberdosis"
+      }
+    },
     "node_modules/@tootallnate/once": {
       "version": "1.1.2",
       "resolved": "https://registry.npmjs.org/@tootallnate/once/-/once-1.1.2.tgz",
@@ -7092,14 +7768,6 @@
       "resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.8.tgz",
       "integrity": "sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w=="
     },
-    "node_modules/@types/estree-jsx": {
-      "version": "1.0.5",
-      "resolved": "https://registry.npmjs.org/@types/estree-jsx/-/estree-jsx-1.0.5.tgz",
-      "integrity": "sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg==",
-      "dependencies": {
-        "@types/estree": "*"
-      }
-    },
     "node_modules/@types/geojson": {
       "version": "7946.0.16",
       "resolved": "https://registry.npmjs.org/@types/geojson/-/geojson-7946.0.16.tgz",
@@ -7123,11 +7791,11 @@
       }
     },
     "node_modules/@types/hast": {
-      "version": "3.0.4",
-      "resolved": "https://registry.npmjs.org/@types/hast/-/hast-3.0.4.tgz",
-      "integrity": "sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==",
+      "version": "2.3.10",
+      "resolved": "https://registry.npmjs.org/@types/hast/-/hast-2.3.10.tgz",
+      "integrity": "sha512-McWspRw8xx8J9HurkVBfYj0xKoE25tOFlHGdx4MJ5xORQrMGZNqJhVQWaIbm6Oyla5kYOXtDiopzKRJzEOkwJw==",
       "dependencies": {
-        "@types/unist": "*"
+        "@types/unist": "^2"
       }
     },
     "node_modules/@types/history": {
@@ -7195,6 +7863,11 @@
       "resolved": "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz",
       "integrity": "sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ=="
     },
+    "node_modules/@types/linkify-it": {
+      "version": "5.0.0",
+      "resolved": "https://registry.npmjs.org/@types/linkify-it/-/linkify-it-5.0.0.tgz",
+      "integrity": "sha512-sVDA58zAw4eWAffKOaQH5/5j3XeayukzDk+ewSsnv3p4yJEZHCCzMDiZM8e0OUrRvmpGZ85jf4yDHkHsgBNr9Q=="
+    },
     "node_modules/@types/mapbox__point-geometry": {
       "version": "0.1.4",
       "resolved": "https://registry.npmjs.org/@types/mapbox__point-geometry/-/mapbox__point-geometry-0.1.4.tgz",
@@ -7210,13 +7883,27 @@
         "@types/pbf": "*"
       }
     },
+    "node_modules/@types/markdown-it": {
+      "version": "14.1.2",
+      "resolved": "https://registry.npmjs.org/@types/markdown-it/-/markdown-it-14.1.2.tgz",
+      "integrity": "sha512-promo4eFwuiW+TfGxhi+0x3czqTYJkG8qB17ZUJiVF10Xm7NLVRSLUsfRTU/6h1e24VvRnXCx+hG7li58lkzog==",
+      "dependencies": {
+        "@types/linkify-it": "^5",
+        "@types/mdurl": "^2"
+      }
+    },
     "node_modules/@types/mdast": {
-      "version": "4.0.4",
-      "resolved": "https://registry.npmjs.org/@types/mdast/-/mdast-4.0.4.tgz",
-      "integrity": "sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==",
+      "version": "3.0.15",
+      "resolved": "https://registry.npmjs.org/@types/mdast/-/mdast-3.0.15.tgz",
+      "integrity": "sha512-LnwD+mUEfxWMa1QpDraczIn6k0Ee3SMicuYSSzS6ZYl2gKS09EClnJYGd8Du6rfc5r/GZEk5o1mRb8TaTj03sQ==",
       "dependencies": {
-        "@types/unist": "*"
+        "@types/unist": "^2"
       }
+    },
+    "node_modules/@types/mdurl": {
+      "version": "2.0.0",
+      "resolved": "https://registry.npmjs.org/@types/mdurl/-/mdurl-2.0.0.tgz",
+      "integrity": "sha512-RGdgjQUZba5p6QEFAVx2OGb8rQDL/cPRG7GiedRzMcJ1tYnUANBncjbSB1NRGwbvjcPeikRABz2nshyPk1bhWg=="
     },
     "node_modules/@types/minimatch": {
       "version": "5.1.2",
@@ -7374,9 +8061,14 @@
       }
     },
     "node_modules/@types/unist": {
-      "version": "3.0.3",
-      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.3.tgz",
-      "integrity": "sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q=="
+      "version": "2.0.11",
+      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-2.0.11.tgz",
+      "integrity": "sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA=="
+    },
+    "node_modules/@types/use-sync-external-store": {
+      "version": "0.0.6",
+      "resolved": "https://registry.npmjs.org/@types/use-sync-external-store/-/use-sync-external-store-0.0.6.tgz",
+      "integrity": "sha512-zFDAD+tlpf2r4asuHEj0XH6pY6i0g5NeAHPn+15wk3BV6JA69eERFXC1gyGThDkVa1zCyKr5jox1+2LbV/AMLg=="
     },
     "node_modules/@types/webpack": {
       "version": "4.41.40",
@@ -7592,11 +8284,6 @@
         "react": "*"
       }
     },
-    "node_modules/@ungap/structured-clone": {
-      "version": "1.3.0",
-      "resolved": "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz",
-      "integrity": "sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g=="
-    },
     "node_modules/@webassemblyjs/ast": {
       "version": "1.9.0",
       "resolved": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.9.0.tgz",
@@ -9695,15 +10382,6 @@
         "node": ">=4"
       }
     },
-    "node_modules/ccount": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/ccount/-/ccount-2.0.1.tgz",
-      "integrity": "sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==",
-      "funding": {
-        "type": "github",
-        "url": "https://github.com/sponsors/wooorm"
-      }
-    },
     "node_modules/center-align": {
       "version": "0.1.3",
       "resolved": "https://registry.npmjs.org/center-align/-/center-align-0.1.3.tgz",
@@ -9745,33 +10423,6 @@
         "url": "https://github.com/sponsors/wooorm"
       }
     },
-    "node_modules/character-entities-html4": {
-      "version": "2.1.0",
-      "resolved": "https://registry.npmjs.org/character-entities-html4/-/character-entities-html4-2.1.0.tgz",
-      "integrity": "sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==",
-      "funding": {
-        "type": "github",
-        "url": "https://github.com/sponsors/wooorm"
-      }
-    },
-    "node_modules/character-entities-legacy": {
-      "version": "3.0.0",
-      "resolved": "https://registry.npmjs.org/character-entities-legacy/-/character-entities-legacy-3.0.0.tgz",
-      "integrity": "sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==",
-      "funding": {
-        "type": "github",
-        "url": "https://github.com/sponsors/wooorm"
-      }
-    },
-    "node_modules/character-reference-invalid": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/character-reference-invalid/-/character-reference-invalid-2.0.1.tgz",
-      "integrity": "sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==",
-      "funding": {
-        "type": "github",
-        "url": "https://github.com/sponsors/wooorm"
-      }
-    },
     "node_modules/check-types": {
       "version": "11.2.3",
       "resolved": "https://registry.npmjs.org/check-types/-/check-types-11.2.3.tgz",
@@ -10532,6 +11183,11 @@
       "resolved": "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz",
       "integrity": "sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ=="
     },
+    "node_modules/crelt": {
+      "version": "1.0.6",
+      "resolved": "https://registry.npmjs.org/crelt/-/crelt-1.0.6.tgz",
+      "integrity": "sha512-VQ2MBenTq1fWZUH9DJNGti7kKv6EeAuYr3cLwxUWhIu1baTaXh4Ib5W2CqHVqib4/MqbYGJqiL3Zb8GJZr3l4g=="
+    },
     "node_modules/cross-spawn": {
       "version": "7.0.6",
       "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz",
@@ -12000,18 +12656,6 @@
       "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz",
       "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="
     },
-    "node_modules/devlop": {
-      "version": "1.1.0",
-      "resolved": "https://registry.npmjs.org/devlop/-/devlop-1.1.0.tgz",
-      "integrity": "sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==",
-      "dependencies": {
-        "dequal": "^2.0.0"
-      },
-      "funding": {
-        "type": "github",
-        "url": "https://github.com/sponsors/wooorm"
-      }
-    },
     "node_modules/diff": {
       "version": "4.0.2",
       "resolved": "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz",
@@ -13518,15 +14162,6 @@
         "node": ">=4.0"
       }
     },
-    "node_modules/estree-util-is-identifier-name": {
-      "version": "3.0.0",
-      "resolved": "https://registry.npmjs.org/estree-util-is-identifier-name/-/estree-util-is-identifier-name-3.0.0.tgz",
-      "integrity": "sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg==",
-      "funding": {
-        "type": "opencollective",
-        "url": "https://opencollective.com/unified"
-      }
-    },
     "node_modules/estree-walker": {
       "version": "1.0.1",
       "resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-1.0.1.tgz",
@@ -15089,39 +15724,10 @@
         "node": ">= 0.4"
       }
     },
-    "node_modules/hast-util-to-jsx-runtime": {
-      "version": "2.3.6",
-      "resolved": "https://registry.npmjs.org/hast-util-to-jsx-runtime/-/hast-util-to-jsx-runtime-2.3.6.tgz",
-      "integrity": "sha512-zl6s8LwNyo1P9uw+XJGvZtdFF1GdAkOg8ujOw+4Pyb76874fLps4ueHXDhXWdk6YHQ6OgUtinliG7RsYvCbbBg==",
-      "dependencies": {
-        "@types/estree": "^1.0.0",
-        "@types/hast": "^3.0.0",
-        "@types/unist": "^3.0.0",
-        "comma-separated-tokens": "^2.0.0",
-        "devlop": "^1.0.0",
-        "estree-util-is-identifier-name": "^3.0.0",
-        "hast-util-whitespace": "^3.0.0",
-        "mdast-util-mdx-expression": "^2.0.0",
-        "mdast-util-mdx-jsx": "^3.0.0",
-        "mdast-util-mdxjs-esm": "^2.0.0",
-        "property-information": "^7.0.0",
-        "space-separated-tokens": "^2.0.0",
-        "style-to-js": "^1.0.0",
-        "unist-util-position": "^5.0.0",
-        "vfile-message": "^4.0.0"
-      },
-      "funding": {
-        "type": "opencollective",
-        "url": "https://opencollective.com/unified"
-      }
-    },
     "node_modules/hast-util-whitespace": {
-      "version": "3.0.0",
-      "resolved": "https://registry.npmjs.org/hast-util-whitespace/-/hast-util-whitespace-3.0.0.tgz",
-      "integrity": "sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw==",
-      "dependencies": {
-        "@types/hast": "^3.0.0"
-      },
+      "version": "2.0.1",
+      "resolved": "https://registry.npmjs.org/hast-util-whitespace/-/hast-util-whitespace-2.0.1.tgz",
+      "integrity": "sha512-nAxA0v8+vXSBDt3AnRUNjyRIQ0rD+ntpbAp4LnPkumc5M9yUbSMa4XDU9Q6etY4f1Wp4bNgvc1yjiZtsTTrSng==",
       "funding": {
         "type": "opencollective",
         "url": "https://opencollective.com/unified"
@@ -15264,15 +15870,6 @@
         "node": ">= 6"
       }
     },
-    "node_modules/html-url-attributes": {
-      "version": "3.0.1",
-      "resolved": "https://registry.npmjs.org/html-url-attributes/-/html-url-attributes-3.0.1.tgz",
-      "integrity": "sha512-ol6UPyBWqsrO6EJySPz2O7ZSr856WDrEzM5zMqp+FJJLGMW35cLYmmZnl0vztAZxRUoNZJFTCohfjuIJ8I4QBQ==",
-      "funding": {
-        "type": "opencollective",
-        "url": "https://opencollective.com/unified"
-      }
-    },
     "node_modules/html-webpack-plugin": {
       "version": "4.5.0",
       "resolved": "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-4.5.0.tgz",
@@ -15645,9 +16242,9 @@
       "integrity": "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew=="
     },
     "node_modules/inline-style-parser": {
-      "version": "0.2.4",
-      "resolved": "https://registry.npmjs.org/inline-style-parser/-/inline-style-parser-0.2.4.tgz",
-      "integrity": "sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q=="
+      "version": "0.1.1",
+      "resolved": "https://registry.npmjs.org/inline-style-parser/-/inline-style-parser-0.1.1.tgz",
+      "integrity": "sha512-7NXolsK4CAS5+xvdj5OMMbI962hU/wvwoxk+LWR9Ek9bVtyuuYScDN6eS0rUm6TxApFpw7CX1o4uJzcd4AyD3Q=="
     },
     "node_modules/inline-style-prefixer": {
       "version": "7.0.1",
@@ -15770,28 +16367,6 @@
         "node": ">= 0.10"
       }
     },
-    "node_modules/is-alphabetical": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/is-alphabetical/-/is-alphabetical-2.0.1.tgz",
-      "integrity": "sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==",
-      "funding": {
-        "type": "github",
-        "url": "https://github.com/sponsors/wooorm"
-      }
-    },
-    "node_modules/is-alphanumerical": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/is-alphanumerical/-/is-alphanumerical-2.0.1.tgz",
-      "integrity": "sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==",
-      "dependencies": {
-        "is-alphabetical": "^2.0.0",
-        "is-decimal": "^2.0.0"
-      },
-      "funding": {
-        "type": "github",
-        "url": "https://github.com/sponsors/wooorm"
-      }
-    },
     "node_modules/is-any-array": {
       "version": "2.0.1",
       "resolved": "https://registry.npmjs.org/is-any-array/-/is-any-array-2.0.1.tgz",
@@ -15988,15 +16563,6 @@
         "url": "https://github.com/sponsors/ljharb"
       }
     },
-    "node_modules/is-decimal": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/is-decimal/-/is-decimal-2.0.1.tgz",
-      "integrity": "sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==",
-      "funding": {
-        "type": "github",
-        "url": "https://github.com/sponsors/wooorm"
-      }
-    },
     "node_modules/is-descriptor": {
       "version": "1.0.3",
       "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.3.tgz",
@@ -16108,15 +16674,6 @@
         "node": ">=0.10.0"
       }
     },
-    "node_modules/is-hexadecimal": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/is-hexadecimal/-/is-hexadecimal-2.0.1.tgz",
-      "integrity": "sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==",
-      "funding": {
-        "type": "github",
-        "url": "https://github.com/sponsors/wooorm"
-      }
-    },
     "node_modules/is-map": {
       "version": "2.0.3",
       "resolved": "https://registry.npmjs.org/is-map/-/is-map-2.0.3.tgz",
@@ -18069,6 +18626,19 @@
       "resolved": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz",
       "integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg=="
     },
+    "node_modules/linkify-it": {
+      "version": "5.0.0",
+      "resolved": "https://registry.npmjs.org/linkify-it/-/linkify-it-5.0.0.tgz",
+      "integrity": "sha512-5aHCbzQRADcdP+ATqnDuhhJ/MRIqDkZX5pyjFHRRysS8vZ5AbqGEoFIb6pYHPZ+L/OC2Lc+xT8uHVVR5CAK/wQ==",
+      "dependencies": {
+        "uc.micro": "^2.0.0"
+      }
+    },
+    "node_modules/linkifyjs": {
+      "version": "4.3.1",
+      "resolved": "https://registry.npmjs.org/linkifyjs/-/linkifyjs-4.3.1.tgz",
+      "integrity": "sha512-DRSlB9DKVW04c4SUdGvKK5FR6be45lTU9M76JnngqPeeGDqPwYc0zdUErtsNVMtxPXgUWV4HbXbnC4sNyBxkYg=="
+    },
     "node_modules/loader-runner": {
       "version": "2.4.0",
       "resolved": "https://registry.npmjs.org/loader-runner/-/loader-runner-2.4.0.tgz",
@@ -18190,15 +18760,6 @@
         "node": ">=0.10.0"
       }
     },
-    "node_modules/longest-streak": {
-      "version": "3.1.0",
-      "resolved": "https://registry.npmjs.org/longest-streak/-/longest-streak-3.1.0.tgz",
-      "integrity": "sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==",
-      "funding": {
-        "type": "github",
-        "url": "https://github.com/sponsors/wooorm"
-      }
-    },
     "node_modules/loose-envify": {
       "version": "1.4.0",
       "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz",
@@ -18401,6 +18962,43 @@
         "kdbush": "^4.0.2"
       }
     },
+    "node_modules/markdown-it": {
+      "version": "14.1.0",
+      "resolved": "https://registry.npmjs.org/markdown-it/-/markdown-it-14.1.0.tgz",
+      "integrity": "sha512-a54IwgWPaeBCAAsv13YgmALOF1elABB08FxO9i+r4VFk5Vl4pKokRPeX8u5TCgSsPi6ec1otfLjdOpVcgbpshg==",
+      "dependencies": {
+        "argparse": "^2.0.1",
+        "entities": "^4.4.0",
+        "linkify-it": "^5.0.0",
+        "mdurl": "^2.0.0",
+        "punycode.js": "^2.3.1",
+        "uc.micro": "^2.1.0"
+      },
+      "bin": {
+        "markdown-it": "bin/markdown-it.mjs"
+      }
+    },
+    "node_modules/markdown-it-task-lists": {
+      "version": "2.1.1",
+      "resolved": "https://registry.npmjs.org/markdown-it-task-lists/-/markdown-it-task-lists-2.1.1.tgz",
+      "integrity": "sha512-TxFAc76Jnhb2OUu+n3yz9RMu4CwGfaT788br6HhEDlvWfdeJcLUsxk1Hgw2yJio0OXsxv7pyIPmvECY7bMbluA=="
+    },
+    "node_modules/markdown-it/node_modules/argparse": {
+      "version": "2.0.1",
+      "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz",
+      "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="
+    },
+    "node_modules/markdown-it/node_modules/entities": {
+      "version": "4.5.0",
+      "resolved": "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz",
+      "integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==",
+      "engines": {
+        "node": ">=0.12"
+      },
+      "funding": {
+        "url": "https://github.com/fb55/entities?sponsor=1"
+      }
+    },
     "node_modules/material-colors": {
       "version": "1.2.6",
       "resolved": "https://registry.npmjs.org/material-colors/-/material-colors-1.2.6.tgz",
@@ -18424,93 +19022,37 @@
         "safe-buffer": "^5.1.2"
       }
     },
-    "node_modules/mdast-util-from-markdown": {
-      "version": "2.0.2",
-      "resolved": "https://registry.npmjs.org/mdast-util-from-markdown/-/mdast-util-from-markdown-2.0.2.tgz",
-      "integrity": "sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA==",
+    "node_modules/mdast-util-definitions": {
+      "version": "5.1.2",
+      "resolved": "https://registry.npmjs.org/mdast-util-definitions/-/mdast-util-definitions-5.1.2.tgz",
+      "integrity": "sha512-8SVPMuHqlPME/z3gqVwWY4zVXn8lqKv/pAhC57FuJ40ImXyBpmO5ukh98zB2v7Blql2FiHjHv9LVztSIqjY+MA==",
       "dependencies": {
-        "@types/mdast": "^4.0.0",
-        "@types/unist": "^3.0.0",
-        "decode-named-character-reference": "^1.0.0",
-        "devlop": "^1.0.0",
-        "mdast-util-to-string": "^4.0.0",
-        "micromark": "^4.0.0",
-        "micromark-util-decode-numeric-character-reference": "^2.0.0",
-        "micromark-util-decode-string": "^2.0.0",
-        "micromark-util-normalize-identifier": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0",
-        "unist-util-stringify-position": "^4.0.0"
+        "@types/mdast": "^3.0.0",
+        "@types/unist": "^2.0.0",
+        "unist-util-visit": "^4.0.0"
       },
       "funding": {
         "type": "opencollective",
         "url": "https://opencollective.com/unified"
       }
     },
-    "node_modules/mdast-util-mdx-expression": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/mdast-util-mdx-expression/-/mdast-util-mdx-expression-2.0.1.tgz",
-      "integrity": "sha512-J6f+9hUp+ldTZqKRSg7Vw5V6MqjATc+3E4gf3CFNcuZNWD8XdyI6zQ8GqH7f8169MM6P7hMBRDVGnn7oHB9kXQ==",
+    "node_modules/mdast-util-from-markdown": {
+      "version": "1.3.1",
+      "resolved": "https://registry.npmjs.org/mdast-util-from-markdown/-/mdast-util-from-markdown-1.3.1.tgz",
+      "integrity": "sha512-4xTO/M8c82qBcnQc1tgpNtubGUW/Y1tBQ1B0i5CtSoelOLKFYlElIr3bvgREYYO5iRqbMY1YuqZng0GVOI8Qww==",
       "dependencies": {
-        "@types/estree-jsx": "^1.0.0",
-        "@types/hast": "^3.0.0",
-        "@types/mdast": "^4.0.0",
-        "devlop": "^1.0.0",
-        "mdast-util-from-markdown": "^2.0.0",
-        "mdast-util-to-markdown": "^2.0.0"
-      },
-      "funding": {
-        "type": "opencollective",
-        "url": "https://opencollective.com/unified"
-      }
-    },
-    "node_modules/mdast-util-mdx-jsx": {
-      "version": "3.2.0",
-      "resolved": "https://registry.npmjs.org/mdast-util-mdx-jsx/-/mdast-util-mdx-jsx-3.2.0.tgz",
-      "integrity": "sha512-lj/z8v0r6ZtsN/cGNNtemmmfoLAFZnjMbNyLzBafjzikOM+glrjNHPlf6lQDOTccj9n5b0PPihEBbhneMyGs1Q==",
-      "dependencies": {
-        "@types/estree-jsx": "^1.0.0",
-        "@types/hast": "^3.0.0",
-        "@types/mdast": "^4.0.0",
-        "@types/unist": "^3.0.0",
-        "ccount": "^2.0.0",
-        "devlop": "^1.1.0",
-        "mdast-util-from-markdown": "^2.0.0",
-        "mdast-util-to-markdown": "^2.0.0",
-        "parse-entities": "^4.0.0",
-        "stringify-entities": "^4.0.0",
-        "unist-util-stringify-position": "^4.0.0",
-        "vfile-message": "^4.0.0"
-      },
-      "funding": {
-        "type": "opencollective",
-        "url": "https://opencollective.com/unified"
-      }
-    },
-    "node_modules/mdast-util-mdxjs-esm": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/mdast-util-mdxjs-esm/-/mdast-util-mdxjs-esm-2.0.1.tgz",
-      "integrity": "sha512-EcmOpxsZ96CvlP03NghtH1EsLtr0n9Tm4lPUJUBccV9RwUOneqSycg19n5HGzCf+10LozMRSObtVr3ee1WoHtg==",
-      "dependencies": {
-        "@types/estree-jsx": "^1.0.0",
-        "@types/hast": "^3.0.0",
-        "@types/mdast": "^4.0.0",
-        "devlop": "^1.0.0",
-        "mdast-util-from-markdown": "^2.0.0",
-        "mdast-util-to-markdown": "^2.0.0"
-      },
-      "funding": {
-        "type": "opencollective",
-        "url": "https://opencollective.com/unified"
-      }
-    },
-    "node_modules/mdast-util-phrasing": {
-      "version": "4.1.0",
-      "resolved": "https://registry.npmjs.org/mdast-util-phrasing/-/mdast-util-phrasing-4.1.0.tgz",
-      "integrity": "sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==",
-      "dependencies": {
-        "@types/mdast": "^4.0.0",
-        "unist-util-is": "^6.0.0"
+        "@types/mdast": "^3.0.0",
+        "@types/unist": "^2.0.0",
+        "decode-named-character-reference": "^1.0.0",
+        "mdast-util-to-string": "^3.1.0",
+        "micromark": "^3.0.0",
+        "micromark-util-decode-numeric-character-reference": "^1.0.0",
+        "micromark-util-decode-string": "^1.0.0",
+        "micromark-util-normalize-identifier": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0",
+        "unist-util-stringify-position": "^3.0.0",
+        "uvu": "^0.5.0"
       },
       "funding": {
         "type": "opencollective",
@@ -18518,39 +19060,18 @@
       }
     },
     "node_modules/mdast-util-to-hast": {
-      "version": "13.2.0",
-      "resolved": "https://registry.npmjs.org/mdast-util-to-hast/-/mdast-util-to-hast-13.2.0.tgz",
-      "integrity": "sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA==",
+      "version": "12.3.0",
+      "resolved": "https://registry.npmjs.org/mdast-util-to-hast/-/mdast-util-to-hast-12.3.0.tgz",
+      "integrity": "sha512-pits93r8PhnIoU4Vy9bjW39M2jJ6/tdHyja9rrot9uujkN7UTU9SDnE6WNJz/IGyQk3XHX6yNNtrBH6cQzm8Hw==",
       "dependencies": {
-        "@types/hast": "^3.0.0",
-        "@types/mdast": "^4.0.0",
-        "@ungap/structured-clone": "^1.0.0",
-        "devlop": "^1.0.0",
-        "micromark-util-sanitize-uri": "^2.0.0",
+        "@types/hast": "^2.0.0",
+        "@types/mdast": "^3.0.0",
+        "mdast-util-definitions": "^5.0.0",
+        "micromark-util-sanitize-uri": "^1.1.0",
         "trim-lines": "^3.0.0",
-        "unist-util-position": "^5.0.0",
-        "unist-util-visit": "^5.0.0",
-        "vfile": "^6.0.0"
-      },
-      "funding": {
-        "type": "opencollective",
-        "url": "https://opencollective.com/unified"
-      }
-    },
-    "node_modules/mdast-util-to-markdown": {
-      "version": "2.1.2",
-      "resolved": "https://registry.npmjs.org/mdast-util-to-markdown/-/mdast-util-to-markdown-2.1.2.tgz",
-      "integrity": "sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA==",
-      "dependencies": {
-        "@types/mdast": "^4.0.0",
-        "@types/unist": "^3.0.0",
-        "longest-streak": "^3.0.0",
-        "mdast-util-phrasing": "^4.0.0",
-        "mdast-util-to-string": "^4.0.0",
-        "micromark-util-classify-character": "^2.0.0",
-        "micromark-util-decode-string": "^2.0.0",
-        "unist-util-visit": "^5.0.0",
-        "zwitch": "^2.0.0"
+        "unist-util-generated": "^2.0.0",
+        "unist-util-position": "^4.0.0",
+        "unist-util-visit": "^4.0.0"
       },
       "funding": {
         "type": "opencollective",
@@ -18558,11 +19079,11 @@
       }
     },
     "node_modules/mdast-util-to-string": {
-      "version": "4.0.0",
-      "resolved": "https://registry.npmjs.org/mdast-util-to-string/-/mdast-util-to-string-4.0.0.tgz",
-      "integrity": "sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==",
+      "version": "3.2.0",
+      "resolved": "https://registry.npmjs.org/mdast-util-to-string/-/mdast-util-to-string-3.2.0.tgz",
+      "integrity": "sha512-V4Zn/ncyN1QNSqSBxTrMOLpjr+IKdHl2v3KVLoWmDPscP4r9GcCi71gjgvUV1SFSKh92AjAG4peFuBl2/YgCJg==",
       "dependencies": {
-        "@types/mdast": "^4.0.0"
+        "@types/mdast": "^3.0.0"
       },
       "funding": {
         "type": "opencollective",
@@ -18574,6 +19095,11 @@
       "resolved": "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz",
       "integrity": "sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow=="
     },
+    "node_modules/mdurl": {
+      "version": "2.0.0",
+      "resolved": "https://registry.npmjs.org/mdurl/-/mdurl-2.0.0.tgz",
+      "integrity": "sha512-Lf+9+2r+Tdp5wXDXC4PcIBjTDtq4UKjCPMQhKIuzpJNW0b96kVqSwW0bT7FhRSfmAiFYgP+SCRvdrDozfh0U5w=="
+    },
     "node_modules/media-typer": {
       "version": "0.3.0",
       "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz",
@@ -18631,9 +19157,9 @@
       "integrity": "sha512-jo1OfR4TaEwd5HOrt5+tAZ9mqT4jmpNAusXtyfNzqVm9uiSYFZlKM1wYL4oU7azZW/PxQW53wM0S6OR1JHNa2g=="
     },
     "node_modules/micromark": {
-      "version": "4.0.2",
-      "resolved": "https://registry.npmjs.org/micromark/-/micromark-4.0.2.tgz",
-      "integrity": "sha512-zpe98Q6kvavpCr1NPVSCMebCKfD7CA2NqZ+rykeNhONIJBpc1tFKt9hucLGwha3jNTNI8lHpctWJWoimVF4PfA==",
+      "version": "3.2.0",
+      "resolved": "https://registry.npmjs.org/micromark/-/micromark-3.2.0.tgz",
+      "integrity": "sha512-uD66tJj54JLYq0De10AhWycZWGQNUvDI55xPgk2sQM5kn1JYlhbCMTtEeT27+vAhW2FBQxLlOmS3pmA7/2z4aA==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18648,26 +19174,26 @@
         "@types/debug": "^4.0.0",
         "debug": "^4.0.0",
         "decode-named-character-reference": "^1.0.0",
-        "devlop": "^1.0.0",
-        "micromark-core-commonmark": "^2.0.0",
-        "micromark-factory-space": "^2.0.0",
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-chunked": "^2.0.0",
-        "micromark-util-combine-extensions": "^2.0.0",
-        "micromark-util-decode-numeric-character-reference": "^2.0.0",
-        "micromark-util-encode": "^2.0.0",
-        "micromark-util-normalize-identifier": "^2.0.0",
-        "micromark-util-resolve-all": "^2.0.0",
-        "micromark-util-sanitize-uri": "^2.0.0",
-        "micromark-util-subtokenize": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-core-commonmark": "^1.0.1",
+        "micromark-factory-space": "^1.0.0",
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-chunked": "^1.0.0",
+        "micromark-util-combine-extensions": "^1.0.0",
+        "micromark-util-decode-numeric-character-reference": "^1.0.0",
+        "micromark-util-encode": "^1.0.0",
+        "micromark-util-normalize-identifier": "^1.0.0",
+        "micromark-util-resolve-all": "^1.0.0",
+        "micromark-util-sanitize-uri": "^1.0.0",
+        "micromark-util-subtokenize": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.1",
+        "uvu": "^0.5.0"
       }
     },
     "node_modules/micromark-core-commonmark": {
-      "version": "2.0.3",
-      "resolved": "https://registry.npmjs.org/micromark-core-commonmark/-/micromark-core-commonmark-2.0.3.tgz",
-      "integrity": "sha512-RDBrHEMSxVFLg6xvnXmb1Ayr2WzLAWjeSATAoxwKYJV94TeNavgoIdA0a9ytzDSVzBy2YKFK+emCPOEibLeCrg==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-core-commonmark/-/micromark-core-commonmark-1.1.0.tgz",
+      "integrity": "sha512-BgHO1aRbolh2hcrzL2d1La37V0Aoz73ymF8rAcKnohLy93titmv62E0gP8Hrx9PKcKrqCZ1BbLGbP3bEhoXYlw==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18680,27 +19206,27 @@
       ],
       "dependencies": {
         "decode-named-character-reference": "^1.0.0",
-        "devlop": "^1.0.0",
-        "micromark-factory-destination": "^2.0.0",
-        "micromark-factory-label": "^2.0.0",
-        "micromark-factory-space": "^2.0.0",
-        "micromark-factory-title": "^2.0.0",
-        "micromark-factory-whitespace": "^2.0.0",
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-chunked": "^2.0.0",
-        "micromark-util-classify-character": "^2.0.0",
-        "micromark-util-html-tag-name": "^2.0.0",
-        "micromark-util-normalize-identifier": "^2.0.0",
-        "micromark-util-resolve-all": "^2.0.0",
-        "micromark-util-subtokenize": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-factory-destination": "^1.0.0",
+        "micromark-factory-label": "^1.0.0",
+        "micromark-factory-space": "^1.0.0",
+        "micromark-factory-title": "^1.0.0",
+        "micromark-factory-whitespace": "^1.0.0",
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-chunked": "^1.0.0",
+        "micromark-util-classify-character": "^1.0.0",
+        "micromark-util-html-tag-name": "^1.0.0",
+        "micromark-util-normalize-identifier": "^1.0.0",
+        "micromark-util-resolve-all": "^1.0.0",
+        "micromark-util-subtokenize": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.1",
+        "uvu": "^0.5.0"
       }
     },
     "node_modules/micromark-factory-destination": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-factory-destination/-/micromark-factory-destination-2.0.1.tgz",
-      "integrity": "sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-factory-destination/-/micromark-factory-destination-1.1.0.tgz",
+      "integrity": "sha512-XaNDROBgx9SgSChd69pjiGKbV+nfHGDPVYFs5dOoDd7ZnMAE+Cuu91BCpsY8RT2NP9vo/B8pds2VQNCLiu0zhg==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18712,15 +19238,15 @@
         }
       ],
       "dependencies": {
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0"
       }
     },
     "node_modules/micromark-factory-label": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-factory-label/-/micromark-factory-label-2.0.1.tgz",
-      "integrity": "sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-factory-label/-/micromark-factory-label-1.1.0.tgz",
+      "integrity": "sha512-OLtyez4vZo/1NjxGhcpDSbHQ+m0IIGnT8BoPamh+7jVlzLJBH98zzuCoUeMxvM6WsNeh8wx8cKvqLiPHEACn0w==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18732,16 +19258,16 @@
         }
       ],
       "dependencies": {
-        "devlop": "^1.0.0",
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0",
+        "uvu": "^0.5.0"
       }
     },
     "node_modules/micromark-factory-space": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-factory-space/-/micromark-factory-space-2.0.1.tgz",
-      "integrity": "sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-factory-space/-/micromark-factory-space-1.1.0.tgz",
+      "integrity": "sha512-cRzEj7c0OL4Mw2v6nwzttyOZe8XY/Z8G0rzmWQZTBi/jjwyw/U4uqKtUORXQrR5bAZZnbTI/feRV/R7hc4jQYQ==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18753,14 +19279,14 @@
         }
       ],
       "dependencies": {
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-types": "^1.0.0"
       }
     },
     "node_modules/micromark-factory-title": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-factory-title/-/micromark-factory-title-2.0.1.tgz",
-      "integrity": "sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-factory-title/-/micromark-factory-title-1.1.0.tgz",
+      "integrity": "sha512-J7n9R3vMmgjDOCY8NPw55jiyaQnH5kBdV2/UXCtZIpnHH3P6nHUKaH7XXEYuWwx/xUJcawa8plLBEjMPU24HzQ==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18772,16 +19298,16 @@
         }
       ],
       "dependencies": {
-        "micromark-factory-space": "^2.0.0",
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-factory-space": "^1.0.0",
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0"
       }
     },
     "node_modules/micromark-factory-whitespace": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-factory-whitespace/-/micromark-factory-whitespace-2.0.1.tgz",
-      "integrity": "sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-factory-whitespace/-/micromark-factory-whitespace-1.1.0.tgz",
+      "integrity": "sha512-v2WlmiymVSp5oMg+1Q0N1Lxmt6pMhIHD457whWM7/GUlEks1hI9xj5w3zbc4uuMKXGisksZk8DzP2UyGbGqNsQ==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18793,16 +19319,16 @@
         }
       ],
       "dependencies": {
-        "micromark-factory-space": "^2.0.0",
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-factory-space": "^1.0.0",
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0"
       }
     },
     "node_modules/micromark-util-character": {
-      "version": "2.1.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-character/-/micromark-util-character-2.1.1.tgz",
-      "integrity": "sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==",
+      "version": "1.2.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-character/-/micromark-util-character-1.2.0.tgz",
+      "integrity": "sha512-lXraTwcX3yH/vMDaFWCQJP1uIszLVebzUa3ZHdrgxr7KEU/9mL4mVgCpGbyhvNLNlauROiNUq7WN5u7ndbY6xg==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18814,14 +19340,14 @@
         }
       ],
       "dependencies": {
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0"
       }
     },
     "node_modules/micromark-util-chunked": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-chunked/-/micromark-util-chunked-2.0.1.tgz",
-      "integrity": "sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-chunked/-/micromark-util-chunked-1.1.0.tgz",
+      "integrity": "sha512-Ye01HXpkZPNcV6FiyoW2fGZDUw4Yc7vT0E9Sad83+bEDiCJ1uXu0S3mr8WLpsz3HaG3x2q0HM6CTuPdcZcluFQ==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18833,13 +19359,13 @@
         }
       ],
       "dependencies": {
-        "micromark-util-symbol": "^2.0.0"
+        "micromark-util-symbol": "^1.0.0"
       }
     },
     "node_modules/micromark-util-classify-character": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-classify-character/-/micromark-util-classify-character-2.0.1.tgz",
-      "integrity": "sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-classify-character/-/micromark-util-classify-character-1.1.0.tgz",
+      "integrity": "sha512-SL0wLxtKSnklKSUplok1WQFoGhUdWYKggKUiqhX+Swala+BtptGCu5iPRc+xvzJ4PXE/hwM3FNXsfEVgoZsWbw==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18851,15 +19377,15 @@
         }
       ],
       "dependencies": {
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0"
       }
     },
     "node_modules/micromark-util-combine-extensions": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-combine-extensions/-/micromark-util-combine-extensions-2.0.1.tgz",
-      "integrity": "sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-combine-extensions/-/micromark-util-combine-extensions-1.1.0.tgz",
+      "integrity": "sha512-Q20sp4mfNf9yEqDL50WwuWZHUrCO4fEyeDCnMGmG5Pr0Cz15Uo7KBs6jq+dq0EgX4DPwwrh9m0X+zPV1ypFvUA==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18871,14 +19397,14 @@
         }
       ],
       "dependencies": {
-        "micromark-util-chunked": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-chunked": "^1.0.0",
+        "micromark-util-types": "^1.0.0"
       }
     },
     "node_modules/micromark-util-decode-numeric-character-reference": {
-      "version": "2.0.2",
-      "resolved": "https://registry.npmjs.org/micromark-util-decode-numeric-character-reference/-/micromark-util-decode-numeric-character-reference-2.0.2.tgz",
-      "integrity": "sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-decode-numeric-character-reference/-/micromark-util-decode-numeric-character-reference-1.1.0.tgz",
+      "integrity": "sha512-m9V0ExGv0jB1OT21mrWcuf4QhP46pH1KkfWy9ZEezqHKAxkj4mPCy3nIH1rkbdMlChLHX531eOrymlwyZIf2iw==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18890,13 +19416,13 @@
         }
       ],
       "dependencies": {
-        "micromark-util-symbol": "^2.0.0"
+        "micromark-util-symbol": "^1.0.0"
       }
     },
     "node_modules/micromark-util-decode-string": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-decode-string/-/micromark-util-decode-string-2.0.1.tgz",
-      "integrity": "sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-decode-string/-/micromark-util-decode-string-1.1.0.tgz",
+      "integrity": "sha512-YphLGCK8gM1tG1bd54azwyrQRjCFcmgj2S2GoJDNnh4vYtnL38JS8M4gpxzOPNyHdNEpheyWXCTnnTDY3N+NVQ==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18909,15 +19435,15 @@
       ],
       "dependencies": {
         "decode-named-character-reference": "^1.0.0",
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-decode-numeric-character-reference": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0"
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-decode-numeric-character-reference": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0"
       }
     },
     "node_modules/micromark-util-encode": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-encode/-/micromark-util-encode-2.0.1.tgz",
-      "integrity": "sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-encode/-/micromark-util-encode-1.1.0.tgz",
+      "integrity": "sha512-EuEzTWSTAj9PA5GOAs992GzNh2dGQO52UvAbtSOMvXTxv3Criqb6IOzJUBCmEqrrXSblJIJBbFFv6zPxpreiJw==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18930,9 +19456,9 @@
       ]
     },
     "node_modules/micromark-util-html-tag-name": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-html-tag-name/-/micromark-util-html-tag-name-2.0.1.tgz",
-      "integrity": "sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA==",
+      "version": "1.2.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-html-tag-name/-/micromark-util-html-tag-name-1.2.0.tgz",
+      "integrity": "sha512-VTQzcuQgFUD7yYztuQFKXT49KghjtETQ+Wv/zUjGSGBioZnkA4P1XXZPT1FHeJA6RwRXSF47yvJ1tsJdoxwO+Q==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18945,9 +19471,9 @@
       ]
     },
     "node_modules/micromark-util-normalize-identifier": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-normalize-identifier/-/micromark-util-normalize-identifier-2.0.1.tgz",
-      "integrity": "sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-normalize-identifier/-/micromark-util-normalize-identifier-1.1.0.tgz",
+      "integrity": "sha512-N+w5vhqrBihhjdpM8+5Xsxy71QWqGn7HYNUvch71iV2PM7+E3uWGox1Qp90loa1ephtCxG2ftRV/Conitc6P2Q==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18959,13 +19485,13 @@
         }
       ],
       "dependencies": {
-        "micromark-util-symbol": "^2.0.0"
+        "micromark-util-symbol": "^1.0.0"
       }
     },
     "node_modules/micromark-util-resolve-all": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-resolve-all/-/micromark-util-resolve-all-2.0.1.tgz",
-      "integrity": "sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-resolve-all/-/micromark-util-resolve-all-1.1.0.tgz",
+      "integrity": "sha512-b/G6BTMSg+bX+xVCshPTPyAu2tmA0E4X98NSR7eIbeC6ycCqCeE7wjfDIgzEbkzdEVJXRtOG4FbEm/uGbCRouA==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18977,13 +19503,13 @@
         }
       ],
       "dependencies": {
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-types": "^1.0.0"
       }
     },
     "node_modules/micromark-util-sanitize-uri": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-sanitize-uri/-/micromark-util-sanitize-uri-2.0.1.tgz",
-      "integrity": "sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==",
+      "version": "1.2.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-sanitize-uri/-/micromark-util-sanitize-uri-1.2.0.tgz",
+      "integrity": "sha512-QO4GXv0XZfWey4pYFndLUKEAktKkG5kZTdUNaTAkzbuJxn2tNBOr+QtxR2XpWaMhbImT2dPzyLrPXLlPhph34A==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -18995,15 +19521,15 @@
         }
       ],
       "dependencies": {
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-encode": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0"
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-encode": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0"
       }
     },
     "node_modules/micromark-util-subtokenize": {
-      "version": "2.1.0",
-      "resolved": "https://registry.npmjs.org/micromark-util-subtokenize/-/micromark-util-subtokenize-2.1.0.tgz",
-      "integrity": "sha512-XQLu552iSctvnEcgXw6+Sx75GflAPNED1qx7eBJ+wydBb2KCbRZe+NwvIEEMM83uml1+2WSXpBAcp9IUCgCYWA==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-subtokenize/-/micromark-util-subtokenize-1.1.0.tgz",
+      "integrity": "sha512-kUQHyzRoxvZO2PuLzMt2P/dwVsTiivCK8icYTeR+3WgbuPqfHgPPy7nFKbeqRivBvn/3N3GBiNC+JRTMSxEC7A==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -19015,16 +19541,16 @@
         }
       ],
       "dependencies": {
-        "devlop": "^1.0.0",
-        "micromark-util-chunked": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-chunked": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0",
+        "uvu": "^0.5.0"
       }
     },
     "node_modules/micromark-util-symbol": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-symbol/-/micromark-util-symbol-2.0.1.tgz",
-      "integrity": "sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-symbol/-/micromark-util-symbol-1.1.0.tgz",
+      "integrity": "sha512-uEjpEYY6KMs1g7QfJ2eX1SQEV+ZT4rUD3UcF6l57acZvLNK7PBZL+ty82Z1qhK1/yXIY4bdx04FKMgR0g4IAag==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -19037,9 +19563,9 @@
       ]
     },
     "node_modules/micromark-util-types": {
-      "version": "2.0.2",
-      "resolved": "https://registry.npmjs.org/micromark-util-types/-/micromark-util-types-2.0.2.tgz",
-      "integrity": "sha512-Yw0ECSpJoViF1qTU4DC6NwtC4aWGt1EkzaQB8KPPyCRR8z9TWeV0HbEFGTO+ZY1wB22zmxnJqhPyTpOVCpeHTA==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-types/-/micromark-util-types-1.1.0.tgz",
+      "integrity": "sha512-ukRBgie8TIAcacscVHSiddHjO4k/q3pnedmzMQ4iwDcK0FtFCohKOlFbaOL/mPgfnPsL3C1ZyxJa4sbWrBl3jg==",
       "funding": [
         {
           "type": "GitHub Sponsors",
@@ -19440,6 +19966,14 @@
         "rimraf": "bin.js"
       }
     },
+    "node_modules/mri": {
+      "version": "1.2.0",
+      "resolved": "https://registry.npmjs.org/mri/-/mri-1.2.0.tgz",
+      "integrity": "sha512-tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA==",
+      "engines": {
+        "node": ">=4"
+      }
+    },
     "node_modules/ms": {
       "version": "2.1.3",
       "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz",
@@ -20106,6 +20640,11 @@
         "node": ">= 0.8.0"
       }
     },
+    "node_modules/orderedmap": {
+      "version": "2.1.1",
+      "resolved": "https://registry.npmjs.org/orderedmap/-/orderedmap-2.1.1.tgz",
+      "integrity": "sha512-TvAWxi0nDe1j/rtMcWcIj94+Ffe6n7zhow33h40SKxmsmozs6dz/e+EajymfoFcHd7sxNn8yHM8839uixMOV6g=="
+    },
     "node_modules/os-browserify": {
       "version": "0.3.0",
       "resolved": "https://registry.npmjs.org/os-browserify/-/os-browserify-0.3.0.tgz",
@@ -20253,29 +20792,6 @@
         "node": ">= 0.10"
       }
     },
-    "node_modules/parse-entities": {
-      "version": "4.0.2",
-      "resolved": "https://registry.npmjs.org/parse-entities/-/parse-entities-4.0.2.tgz",
-      "integrity": "sha512-GG2AQYWoLgL877gQIKeRPGO1xF9+eG1ujIb5soS5gPvLQ1y2o8FL90w2QWNdf9I361Mpp7726c+lj3U0qK1uGw==",
-      "dependencies": {
-        "@types/unist": "^2.0.0",
-        "character-entities-legacy": "^3.0.0",
-        "character-reference-invalid": "^2.0.0",
-        "decode-named-character-reference": "^1.0.0",
-        "is-alphanumerical": "^2.0.0",
-        "is-decimal": "^2.0.0",
-        "is-hexadecimal": "^2.0.0"
-      },
-      "funding": {
-        "type": "github",
-        "url": "https://github.com/sponsors/wooorm"
-      }
-    },
-    "node_modules/parse-entities/node_modules/@types/unist": {
-      "version": "2.0.11",
-      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-2.0.11.tgz",
-      "integrity": "sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA=="
-    },
     "node_modules/parse-json": {
       "version": "5.2.0",
       "resolved": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz",
@@ -23613,14 +24129,191 @@
       "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="
     },
     "node_modules/property-information": {
-      "version": "7.1.0",
-      "resolved": "https://registry.npmjs.org/property-information/-/property-information-7.1.0.tgz",
-      "integrity": "sha512-TwEZ+X+yCJmYfL7TPUOcvBZ4QfoT5YenQiJuX//0th53DE6w0xxLEtfK3iyryQFddXuvkIk51EEgrJQ0WJkOmQ==",
+      "version": "6.5.0",
+      "resolved": "https://registry.npmjs.org/property-information/-/property-information-6.5.0.tgz",
+      "integrity": "sha512-PgTgs/BlvHxOu8QuEN7wi5A0OmXaBcHpmCSTehcs6Uuu9IkDIEo13Hy7n898RHfrQ49vKCoGeWZSaAK01nwVig==",
       "funding": {
         "type": "github",
         "url": "https://github.com/sponsors/wooorm"
       }
     },
+    "node_modules/prosemirror-changeset": {
+      "version": "2.3.1",
+      "resolved": "https://registry.npmjs.org/prosemirror-changeset/-/prosemirror-changeset-2.3.1.tgz",
+      "integrity": "sha512-j0kORIBm8ayJNl3zQvD1TTPHJX3g042et6y/KQhZhnPrruO8exkTgG8X+NRpj7kIyMMEx74Xb3DyMIBtO0IKkQ==",
+      "dependencies": {
+        "prosemirror-transform": "^1.0.0"
+      }
+    },
+    "node_modules/prosemirror-collab": {
+      "version": "1.3.1",
+      "resolved": "https://registry.npmjs.org/prosemirror-collab/-/prosemirror-collab-1.3.1.tgz",
+      "integrity": "sha512-4SnynYR9TTYaQVXd/ieUvsVV4PDMBzrq2xPUWutHivDuOshZXqQ5rGbZM84HEaXKbLdItse7weMGOUdDVcLKEQ==",
+      "dependencies": {
+        "prosemirror-state": "^1.0.0"
+      }
+    },
+    "node_modules/prosemirror-commands": {
+      "version": "1.7.1",
+      "resolved": "https://registry.npmjs.org/prosemirror-commands/-/prosemirror-commands-1.7.1.tgz",
+      "integrity": "sha512-rT7qZnQtx5c0/y/KlYaGvtG411S97UaL6gdp6RIZ23DLHanMYLyfGBV5DtSnZdthQql7W+lEVbpSfwtO8T+L2w==",
+      "dependencies": {
+        "prosemirror-model": "^1.0.0",
+        "prosemirror-state": "^1.0.0",
+        "prosemirror-transform": "^1.10.2"
+      }
+    },
+    "node_modules/prosemirror-dropcursor": {
+      "version": "1.8.2",
+      "resolved": "https://registry.npmjs.org/prosemirror-dropcursor/-/prosemirror-dropcursor-1.8.2.tgz",
+      "integrity": "sha512-CCk6Gyx9+Tt2sbYk5NK0nB1ukHi2ryaRgadV/LvyNuO3ena1payM2z6Cg0vO1ebK8cxbzo41ku2DE5Axj1Zuiw==",
+      "dependencies": {
+        "prosemirror-state": "^1.0.0",
+        "prosemirror-transform": "^1.1.0",
+        "prosemirror-view": "^1.1.0"
+      }
+    },
+    "node_modules/prosemirror-gapcursor": {
+      "version": "1.3.2",
+      "resolved": "https://registry.npmjs.org/prosemirror-gapcursor/-/prosemirror-gapcursor-1.3.2.tgz",
+      "integrity": "sha512-wtjswVBd2vaQRrnYZaBCbyDqr232Ed4p2QPtRIUK5FuqHYKGWkEwl08oQM4Tw7DOR0FsasARV5uJFvMZWxdNxQ==",
+      "dependencies": {
+        "prosemirror-keymap": "^1.0.0",
+        "prosemirror-model": "^1.0.0",
+        "prosemirror-state": "^1.0.0",
+        "prosemirror-view": "^1.0.0"
+      }
+    },
+    "node_modules/prosemirror-history": {
+      "version": "1.4.1",
+      "resolved": "https://registry.npmjs.org/prosemirror-history/-/prosemirror-history-1.4.1.tgz",
+      "integrity": "sha512-2JZD8z2JviJrboD9cPuX/Sv/1ChFng+xh2tChQ2X4bB2HeK+rra/bmJ3xGntCcjhOqIzSDG6Id7e8RJ9QPXLEQ==",
+      "dependencies": {
+        "prosemirror-state": "^1.2.2",
+        "prosemirror-transform": "^1.0.0",
+        "prosemirror-view": "^1.31.0",
+        "rope-sequence": "^1.3.0"
+      }
+    },
+    "node_modules/prosemirror-inputrules": {
+      "version": "1.5.0",
+      "resolved": "https://registry.npmjs.org/prosemirror-inputrules/-/prosemirror-inputrules-1.5.0.tgz",
+      "integrity": "sha512-K0xJRCmt+uSw7xesnHmcn72yBGTbY45vm8gXI4LZXbx2Z0jwh5aF9xrGQgrVPu0WbyFVFF3E/o9VhJYz6SQWnA==",
+      "dependencies": {
+        "prosemirror-state": "^1.0.0",
+        "prosemirror-transform": "^1.0.0"
+      }
+    },
+    "node_modules/prosemirror-keymap": {
+      "version": "1.2.3",
+      "resolved": "https://registry.npmjs.org/prosemirror-keymap/-/prosemirror-keymap-1.2.3.tgz",
+      "integrity": "sha512-4HucRlpiLd1IPQQXNqeo81BGtkY8Ai5smHhKW9jjPKRc2wQIxksg7Hl1tTI2IfT2B/LgX6bfYvXxEpJl7aKYKw==",
+      "dependencies": {
+        "prosemirror-state": "^1.0.0",
+        "w3c-keyname": "^2.2.0"
+      }
+    },
+    "node_modules/prosemirror-markdown": {
+      "version": "1.13.2",
+      "resolved": "https://registry.npmjs.org/prosemirror-markdown/-/prosemirror-markdown-1.13.2.tgz",
+      "integrity": "sha512-FPD9rHPdA9fqzNmIIDhhnYQ6WgNoSWX9StUZ8LEKapaXU9i6XgykaHKhp6XMyXlOWetmaFgGDS/nu/w9/vUc5g==",
+      "dependencies": {
+        "@types/markdown-it": "^14.0.0",
+        "markdown-it": "^14.0.0",
+        "prosemirror-model": "^1.25.0"
+      }
+    },
+    "node_modules/prosemirror-menu": {
+      "version": "1.2.5",
+      "resolved": "https://registry.npmjs.org/prosemirror-menu/-/prosemirror-menu-1.2.5.tgz",
+      "integrity": "sha512-qwXzynnpBIeg1D7BAtjOusR+81xCp53j7iWu/IargiRZqRjGIlQuu1f3jFi+ehrHhWMLoyOQTSRx/IWZJqOYtQ==",
+      "dependencies": {
+        "crelt": "^1.0.0",
+        "prosemirror-commands": "^1.0.0",
+        "prosemirror-history": "^1.0.0",
+        "prosemirror-state": "^1.0.0"
+      }
+    },
+    "node_modules/prosemirror-model": {
+      "version": "1.25.1",
+      "resolved": "https://registry.npmjs.org/prosemirror-model/-/prosemirror-model-1.25.1.tgz",
+      "integrity": "sha512-AUvbm7qqmpZa5d9fPKMvH1Q5bqYQvAZWOGRvxsB6iFLyycvC9MwNemNVjHVrWgjaoxAfY8XVg7DbvQ/qxvI9Eg==",
+      "dependencies": {
+        "orderedmap": "^2.0.0"
+      }
+    },
+    "node_modules/prosemirror-schema-basic": {
+      "version": "1.2.4",
+      "resolved": "https://registry.npmjs.org/prosemirror-schema-basic/-/prosemirror-schema-basic-1.2.4.tgz",
+      "integrity": "sha512-ELxP4TlX3yr2v5rM7Sb70SqStq5NvI15c0j9j/gjsrO5vaw+fnnpovCLEGIcpeGfifkuqJwl4fon6b+KdrODYQ==",
+      "dependencies": {
+        "prosemirror-model": "^1.25.0"
+      }
+    },
+    "node_modules/prosemirror-schema-list": {
+      "version": "1.5.1",
+      "resolved": "https://registry.npmjs.org/prosemirror-schema-list/-/prosemirror-schema-list-1.5.1.tgz",
+      "integrity": "sha512-927lFx/uwyQaGwJxLWCZRkjXG0p48KpMj6ueoYiu4JX05GGuGcgzAy62dfiV8eFZftgyBUvLx76RsMe20fJl+Q==",
+      "dependencies": {
+        "prosemirror-model": "^1.0.0",
+        "prosemirror-state": "^1.0.0",
+        "prosemirror-transform": "^1.7.3"
+      }
+    },
+    "node_modules/prosemirror-state": {
+      "version": "1.4.3",
+      "resolved": "https://registry.npmjs.org/prosemirror-state/-/prosemirror-state-1.4.3.tgz",
+      "integrity": "sha512-goFKORVbvPuAQaXhpbemJFRKJ2aixr+AZMGiquiqKxaucC6hlpHNZHWgz5R7dS4roHiwq9vDctE//CZ++o0W1Q==",
+      "dependencies": {
+        "prosemirror-model": "^1.0.0",
+        "prosemirror-transform": "^1.0.0",
+        "prosemirror-view": "^1.27.0"
+      }
+    },
+    "node_modules/prosemirror-tables": {
+      "version": "1.7.1",
+      "resolved": "https://registry.npmjs.org/prosemirror-tables/-/prosemirror-tables-1.7.1.tgz",
+      "integrity": "sha512-eRQ97Bf+i9Eby99QbyAiyov43iOKgWa7QCGly+lrDt7efZ1v8NWolhXiB43hSDGIXT1UXgbs4KJN3a06FGpr1Q==",
+      "dependencies": {
+        "prosemirror-keymap": "^1.2.2",
+        "prosemirror-model": "^1.25.0",
+        "prosemirror-state": "^1.4.3",
+        "prosemirror-transform": "^1.10.3",
+        "prosemirror-view": "^1.39.1"
+      }
+    },
+    "node_modules/prosemirror-trailing-node": {
+      "version": "3.0.0",
+      "resolved": "https://registry.npmjs.org/prosemirror-trailing-node/-/prosemirror-trailing-node-3.0.0.tgz",
+      "integrity": "sha512-xiun5/3q0w5eRnGYfNlW1uU9W6x5MoFKWwq/0TIRgt09lv7Hcser2QYV8t4muXbEr+Fwo0geYn79Xs4GKywrRQ==",
+      "dependencies": {
+        "@remirror/core-constants": "3.0.0",
+        "escape-string-regexp": "^4.0.0"
+      },
+      "peerDependencies": {
+        "prosemirror-model": "^1.22.1",
+        "prosemirror-state": "^1.4.2",
+        "prosemirror-view": "^1.33.8"
+      }
+    },
+    "node_modules/prosemirror-transform": {
+      "version": "1.10.4",
+      "resolved": "https://registry.npmjs.org/prosemirror-transform/-/prosemirror-transform-1.10.4.tgz",
+      "integrity": "sha512-pwDy22nAnGqNR1feOQKHxoFkkUtepoFAd3r2hbEDsnf4wp57kKA36hXsB3njA9FtONBEwSDnDeCiJe+ItD+ykw==",
+      "dependencies": {
+        "prosemirror-model": "^1.21.0"
+      }
+    },
+    "node_modules/prosemirror-view": {
+      "version": "1.40.0",
+      "resolved": "https://registry.npmjs.org/prosemirror-view/-/prosemirror-view-1.40.0.tgz",
+      "integrity": "sha512-2G3svX0Cr1sJjkD/DYWSe3cfV5VPVTBOxI9XQEGWJDFEpsZb/gh4MV29ctv+OJx2RFX4BLt09i+6zaGM/ldkCw==",
+      "dependencies": {
+        "prosemirror-model": "^1.20.0",
+        "prosemirror-state": "^1.0.0",
+        "prosemirror-transform": "^1.1.0"
+      }
+    },
     "node_modules/protocol-buffers-schema": {
       "version": "3.6.0",
       "resolved": "https://registry.npmjs.org/protocol-buffers-schema/-/protocol-buffers-schema-3.6.0.tgz",
@@ -23713,6 +24406,14 @@
       "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz",
       "integrity": "sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ=="
     },
+    "node_modules/punycode.js": {
+      "version": "2.3.1",
+      "resolved": "https://registry.npmjs.org/punycode.js/-/punycode.js-2.3.1.tgz",
+      "integrity": "sha512-uxFIHU0YlHYhDQtV4R9J6a52SLx28BCjT+4ieh7IGbgwVJWO+km431c4yRlREUAsAmt/uMjQUyQHNEPf0M39CA==",
+      "engines": {
+        "node": ">=6"
+      }
+    },
     "node_modules/q": {
       "version": "1.5.1",
       "resolved": "https://registry.npmjs.org/q/-/q-1.5.1.tgz",
@@ -24878,30 +25579,39 @@
       "integrity": "sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA=="
     },
     "node_modules/react-markdown": {
-      "version": "10.1.0",
-      "resolved": "https://registry.npmjs.org/react-markdown/-/react-markdown-10.1.0.tgz",
-      "integrity": "sha512-qKxVopLT/TyA6BX3Ue5NwabOsAzm0Q7kAPwq6L+wWDwisYs7R8vZ0nRXqq6rkueboxpkjvLGU9fWifiX/ZZFxQ==",
+      "version": "8.0.7",
+      "resolved": "https://registry.npmjs.org/react-markdown/-/react-markdown-8.0.7.tgz",
+      "integrity": "sha512-bvWbzG4MtOU62XqBx3Xx+zB2raaFFsq4mYiAzfjXJMEz2sixgeAfraA3tvzULF02ZdOMUOKTBFFaZJDDrq+BJQ==",
       "dependencies": {
-        "@types/hast": "^3.0.0",
-        "@types/mdast": "^4.0.0",
-        "devlop": "^1.0.0",
-        "hast-util-to-jsx-runtime": "^2.0.0",
-        "html-url-attributes": "^3.0.0",
-        "mdast-util-to-hast": "^13.0.0",
-        "remark-parse": "^11.0.0",
-        "remark-rehype": "^11.0.0",
-        "unified": "^11.0.0",
-        "unist-util-visit": "^5.0.0",
-        "vfile": "^6.0.0"
+        "@types/hast": "^2.0.0",
+        "@types/prop-types": "^15.0.0",
+        "@types/unist": "^2.0.0",
+        "comma-separated-tokens": "^2.0.0",
+        "hast-util-whitespace": "^2.0.0",
+        "prop-types": "^15.0.0",
+        "property-information": "^6.0.0",
+        "react-is": "^18.0.0",
+        "remark-parse": "^10.0.0",
+        "remark-rehype": "^10.0.0",
+        "space-separated-tokens": "^2.0.0",
+        "style-to-object": "^0.4.0",
+        "unified": "^10.0.0",
+        "unist-util-visit": "^4.0.0",
+        "vfile": "^5.0.0"
       },
       "funding": {
         "type": "opencollective",
         "url": "https://opencollective.com/unified"
       },
       "peerDependencies": {
-        "@types/react": ">=18",
-        "react": ">=18"
+        "@types/react": ">=16",
+        "react": ">=16"
       }
+    },
+    "node_modules/react-markdown/node_modules/react-is": {
+      "version": "18.3.1",
+      "resolved": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz",
+      "integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg=="
     },
     "node_modules/react-redux": {
       "version": "7.2.9",
@@ -25595,14 +26305,13 @@
       }
     },
     "node_modules/remark-parse": {
-      "version": "11.0.0",
-      "resolved": "https://registry.npmjs.org/remark-parse/-/remark-parse-11.0.0.tgz",
-      "integrity": "sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==",
+      "version": "10.0.2",
+      "resolved": "https://registry.npmjs.org/remark-parse/-/remark-parse-10.0.2.tgz",
+      "integrity": "sha512-3ydxgHa/ZQzG8LvC7jTXccARYDcRld3VfcgIIFs7bI6vbRSxJJmzgLEIIoYKyrfhaY+ujuWaf/PJiMZXoiCXgw==",
       "dependencies": {
-        "@types/mdast": "^4.0.0",
-        "mdast-util-from-markdown": "^2.0.0",
-        "micromark-util-types": "^2.0.0",
-        "unified": "^11.0.0"
+        "@types/mdast": "^3.0.0",
+        "mdast-util-from-markdown": "^1.0.0",
+        "unified": "^10.0.0"
       },
       "funding": {
         "type": "opencollective",
@@ -25610,15 +26319,14 @@
       }
     },
     "node_modules/remark-rehype": {
-      "version": "11.1.2",
-      "resolved": "https://registry.npmjs.org/remark-rehype/-/remark-rehype-11.1.2.tgz",
-      "integrity": "sha512-Dh7l57ianaEoIpzbp0PC9UKAdCSVklD8E5Rpw7ETfbTl3FqcOOgq5q2LVDhgGCkaBv7p24JXikPdvhhmHvKMsw==",
+      "version": "10.1.0",
+      "resolved": "https://registry.npmjs.org/remark-rehype/-/remark-rehype-10.1.0.tgz",
+      "integrity": "sha512-EFmR5zppdBp0WQeDVZ/b66CWJipB2q2VLNFMabzDSGR66Z2fQii83G5gTBbgGEnEEA0QRussvrFHxk1HWGJskw==",
       "dependencies": {
-        "@types/hast": "^3.0.0",
-        "@types/mdast": "^4.0.0",
-        "mdast-util-to-hast": "^13.0.0",
-        "unified": "^11.0.0",
-        "vfile": "^6.0.0"
+        "@types/hast": "^2.0.0",
+        "@types/mdast": "^3.0.0",
+        "mdast-util-to-hast": "^12.1.0",
+        "unified": "^10.0.0"
       },
       "funding": {
         "type": "opencollective",
@@ -26135,6 +26843,11 @@
         "node": ">=0.4.0"
       }
     },
+    "node_modules/rope-sequence": {
+      "version": "1.3.4",
+      "resolved": "https://registry.npmjs.org/rope-sequence/-/rope-sequence-1.3.4.tgz",
+      "integrity": "sha512-UT5EDe2cu2E/6O4igUr5PSFs23nvvukicWHx6GnOPlHAiiYbzNuCRQCuiUdHJQcqKalLKlrYJnjY0ySGsXNQXQ=="
+    },
     "node_modules/rsvp": {
       "version": "4.8.5",
       "resolved": "https://registry.npmjs.org/rsvp/-/rsvp-4.8.5.tgz",
@@ -26202,6 +26915,17 @@
       "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz",
       "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
     },
+    "node_modules/sade": {
+      "version": "1.8.1",
+      "resolved": "https://registry.npmjs.org/sade/-/sade-1.8.1.tgz",
+      "integrity": "sha512-xal3CZX1Xlo/k4ApwCFrHVACi9fBqJ7V+mwhBsuf/1IOKbBy098Fex+Wa/5QMubw09pSZ/u8EY8PWgevJsXp1A==",
+      "dependencies": {
+        "mri": "^1.1.0"
+      },
+      "engines": {
+        "node": ">=6"
+      }
+    },
     "node_modules/safe-array-concat": {
       "version": "1.1.3",
       "resolved": "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.3.tgz",
@@ -27701,19 +28425,6 @@
         "url": "https://github.com/sponsors/ljharb"
       }
     },
-    "node_modules/stringify-entities": {
-      "version": "4.0.4",
-      "resolved": "https://registry.npmjs.org/stringify-entities/-/stringify-entities-4.0.4.tgz",
-      "integrity": "sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==",
-      "dependencies": {
-        "character-entities-html4": "^2.0.0",
-        "character-entities-legacy": "^3.0.0"
-      },
-      "funding": {
-        "type": "github",
-        "url": "https://github.com/sponsors/wooorm"
-      }
-    },
     "node_modules/stringify-object": {
       "version": "3.3.0",
       "resolved": "https://registry.npmjs.org/stringify-object/-/stringify-object-3.3.0.tgz",
@@ -27832,20 +28543,12 @@
         "url": "https://opencollective.com/webpack"
       }
     },
-    "node_modules/style-to-js": {
-      "version": "1.1.17",
-      "resolved": "https://registry.npmjs.org/style-to-js/-/style-to-js-1.1.17.tgz",
-      "integrity": "sha512-xQcBGDxJb6jjFCTzvQtfiPn6YvvP2O8U1MDIPNfJQlWMYfktPy+iGsHE7cssjs7y84d9fQaK4UF3RIJaAHSoYA==",
-      "dependencies": {
-        "style-to-object": "1.0.9"
-      }
-    },
     "node_modules/style-to-object": {
-      "version": "1.0.9",
-      "resolved": "https://registry.npmjs.org/style-to-object/-/style-to-object-1.0.9.tgz",
-      "integrity": "sha512-G4qppLgKu/k6FwRpHiGiKPaPTFcG3g4wNVX/Qsfu+RqQM30E7Tyu/TEgxcL9PNLF5pdRLwQdE3YKKf+KF2Dzlw==",
+      "version": "0.4.4",
+      "resolved": "https://registry.npmjs.org/style-to-object/-/style-to-object-0.4.4.tgz",
+      "integrity": "sha512-HYNoHZa2GorYNyqiCaBgsxvcJIn7OHq6inEga+E6Ke3m5JkoqpQbnFssk4jwe+K7AhGa2fcha4wSOf1Kn01dMg==",
       "dependencies": {
-        "inline-style-parser": "0.2.4"
+        "inline-style-parser": "0.1.1"
       }
     },
     "node_modules/stylehacks": {
@@ -28575,6 +29278,86 @@
       "resolved": "https://registry.npmjs.org/tinyqueue/-/tinyqueue-2.0.3.tgz",
       "integrity": "sha512-ppJZNDuKGgxzkHihX8v9v9G5f+18gzaTfrukGrq6ueg0lmH4nqVnA2IPG0AEH3jKEk2GRJCUhDoqpoiw3PHLBA=="
     },
+    "node_modules/tippy.js": {
+      "version": "6.3.7",
+      "resolved": "https://registry.npmjs.org/tippy.js/-/tippy.js-6.3.7.tgz",
+      "integrity": "sha512-E1d3oP2emgJ9dRQZdf3Kkn0qJgI6ZLpyS5z6ZkY1DF3kaQaBsGZsndEpHwx+eC+tYM41HaSNvNtLx8tU57FzTQ==",
+      "dependencies": {
+        "@popperjs/core": "^2.9.0"
+      }
+    },
+    "node_modules/tiptap-markdown": {
+      "version": "0.8.1",
+      "resolved": "https://registry.npmjs.org/tiptap-markdown/-/tiptap-markdown-0.8.1.tgz",
+      "integrity": "sha512-4bg6UHXF+1cIJHcAkcWd0d8zIweCvdIAY2dJjhm/e5oNWprP3gLUYa4aNAEFOKpD/Wh4VBqA7vi7yMj5vIy6xw==",
+      "dependencies": {
+        "@types/markdown-it": "^12.2.3",
+        "markdown-it": "^13.0.1",
+        "markdown-it-task-lists": "^2.1.1",
+        "prosemirror-markdown": "^1.11.0"
+      },
+      "peerDependencies": {
+        "@tiptap/core": "^2.0.3"
+      }
+    },
+    "node_modules/tiptap-markdown/node_modules/@types/markdown-it": {
+      "version": "12.2.3",
+      "resolved": "https://registry.npmjs.org/@types/markdown-it/-/markdown-it-12.2.3.tgz",
+      "integrity": "sha512-GKMHFfv3458yYy+v/N8gjufHO6MSZKCOXpZc5GXIWWy8uldwfmPn98vp81gZ5f9SVw8YYBctgfJ22a2d7AOMeQ==",
+      "dependencies": {
+        "@types/linkify-it": "*",
+        "@types/mdurl": "*"
+      }
+    },
+    "node_modules/tiptap-markdown/node_modules/argparse": {
+      "version": "2.0.1",
+      "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz",
+      "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="
+    },
+    "node_modules/tiptap-markdown/node_modules/entities": {
+      "version": "3.0.1",
+      "resolved": "https://registry.npmjs.org/entities/-/entities-3.0.1.tgz",
+      "integrity": "sha512-WiyBqoomrwMdFG1e0kqvASYfnlb0lp8M5o5Fw2OFq1hNZxxcNk8Ik0Xm7LxzBhuidnZB/UtBqVCgUz3kBOP51Q==",
+      "engines": {
+        "node": ">=0.12"
+      },
+      "funding": {
+        "url": "https://github.com/fb55/entities?sponsor=1"
+      }
+    },
+    "node_modules/tiptap-markdown/node_modules/linkify-it": {
+      "version": "4.0.1",
+      "resolved": "https://registry.npmjs.org/linkify-it/-/linkify-it-4.0.1.tgz",
+      "integrity": "sha512-C7bfi1UZmoj8+PQx22XyeXCuBlokoyWQL5pWSP+EI6nzRylyThouddufc2c1NDIcP9k5agmN9fLpA7VNJfIiqw==",
+      "dependencies": {
+        "uc.micro": "^1.0.1"
+      }
+    },
+    "node_modules/tiptap-markdown/node_modules/markdown-it": {
+      "version": "13.0.2",
+      "resolved": "https://registry.npmjs.org/markdown-it/-/markdown-it-13.0.2.tgz",
+      "integrity": "sha512-FtwnEuuK+2yVU7goGn/MJ0WBZMM9ZPgU9spqlFs7/A/pDIUNSOQZhUgOqYCficIuR2QaFnrt8LHqBWsbTAoI5w==",
+      "dependencies": {
+        "argparse": "^2.0.1",
+        "entities": "~3.0.1",
+        "linkify-it": "^4.0.1",
+        "mdurl": "^1.0.1",
+        "uc.micro": "^1.0.5"
+      },
+      "bin": {
+        "markdown-it": "bin/markdown-it.js"
+      }
+    },
+    "node_modules/tiptap-markdown/node_modules/mdurl": {
+      "version": "1.0.1",
+      "resolved": "https://registry.npmjs.org/mdurl/-/mdurl-1.0.1.tgz",
+      "integrity": "sha512-/sKlQJCBYVY9Ers9hqzKou4H6V5UWc/M59TH2dvkt+84itfnq7uFOMLpOiOS4ujvHP4etln18fmIxA5R5fll0g=="
+    },
+    "node_modules/tiptap-markdown/node_modules/uc.micro": {
+      "version": "1.0.6",
+      "resolved": "https://registry.npmjs.org/uc.micro/-/uc.micro-1.0.6.tgz",
+      "integrity": "sha512-8Y75pvTYkLJW2hWQHXxoqRgV7qb9B+9vFEtidML+7koHUFapnVJAZ6cKs+Qjz5Aw3aZWHMC6u0wJE3At+nSGwA=="
+    },
     "node_modules/tmpl": {
       "version": "1.0.5",
       "resolved": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz",
@@ -29106,6 +29889,11 @@
         "node": "*"
       }
     },
+    "node_modules/uc.micro": {
+      "version": "2.1.0",
+      "resolved": "https://registry.npmjs.org/uc.micro/-/uc.micro-2.1.0.tgz",
+      "integrity": "sha512-ARDJmphmdvUk6Glw7y9DQ2bFkKBHwQHLi2lsaH6PPmz/Ka9sFOBsBluozhDltWmnv9u/cF6Rt87znRTPV+yp/A=="
+    },
     "node_modules/uglify-js": {
       "version": "2.8.29",
       "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.8.29.tgz",
@@ -29226,23 +30014,45 @@
       }
     },
     "node_modules/unified": {
-      "version": "11.0.5",
-      "resolved": "https://registry.npmjs.org/unified/-/unified-11.0.5.tgz",
-      "integrity": "sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==",
+      "version": "10.1.2",
+      "resolved": "https://registry.npmjs.org/unified/-/unified-10.1.2.tgz",
+      "integrity": "sha512-pUSWAi/RAnVy1Pif2kAoeWNBa3JVrx0MId2LASj8G+7AiHWoKZNTomq6LG326T68U7/e263X6fTdcXIy7XnF7Q==",
       "dependencies": {
-        "@types/unist": "^3.0.0",
+        "@types/unist": "^2.0.0",
         "bail": "^2.0.0",
-        "devlop": "^1.0.0",
         "extend": "^3.0.0",
+        "is-buffer": "^2.0.0",
         "is-plain-obj": "^4.0.0",
         "trough": "^2.0.0",
-        "vfile": "^6.0.0"
+        "vfile": "^5.0.0"
       },
       "funding": {
         "type": "opencollective",
         "url": "https://opencollective.com/unified"
       }
     },
+    "node_modules/unified/node_modules/is-buffer": {
+      "version": "2.0.5",
+      "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-2.0.5.tgz",
+      "integrity": "sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ==",
+      "funding": [
+        {
+          "type": "github",
+          "url": "https://github.com/sponsors/feross"
+        },
+        {
+          "type": "patreon",
+          "url": "https://www.patreon.com/feross"
+        },
+        {
+          "type": "consulting",
+          "url": "https://feross.org/support"
+        }
+      ],
+      "engines": {
+        "node": ">=4"
+      }
+    },
     "node_modules/unified/node_modules/is-plain-obj": {
       "version": "4.1.0",
       "resolved": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-4.1.0.tgz",
@@ -29313,12 +30123,21 @@
         "node": ">=4"
       }
     },
+    "node_modules/unist-util-generated": {
+      "version": "2.0.1",
+      "resolved": "https://registry.npmjs.org/unist-util-generated/-/unist-util-generated-2.0.1.tgz",
+      "integrity": "sha512-qF72kLmPxAw0oN2fwpWIqbXAVyEqUzDHMsbtPvOudIlUzXYFIeQIuxXQCRCFh22B7cixvU0MG7m3MW8FTq/S+A==",
+      "funding": {
+        "type": "opencollective",
+        "url": "https://opencollective.com/unified"
+      }
+    },
     "node_modules/unist-util-is": {
-      "version": "6.0.0",
-      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
-      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
+      "version": "5.2.1",
+      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-5.2.1.tgz",
+      "integrity": "sha512-u9njyyfEh43npf1M+yGKDGVPbY/JWEemg5nH05ncKPfi+kBbKBJoTdsogMu33uhytuLlv9y0O7GH7fEdwLdLQw==",
       "dependencies": {
-        "@types/unist": "^3.0.0"
+        "@types/unist": "^2.0.0"
       },
       "funding": {
         "type": "opencollective",
@@ -29326,11 +30145,11 @@
       }
     },
     "node_modules/unist-util-position": {
-      "version": "5.0.0",
-      "resolved": "https://registry.npmjs.org/unist-util-position/-/unist-util-position-5.0.0.tgz",
-      "integrity": "sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA==",
+      "version": "4.0.4",
+      "resolved": "https://registry.npmjs.org/unist-util-position/-/unist-util-position-4.0.4.tgz",
+      "integrity": "sha512-kUBE91efOWfIVBo8xzh/uZQ7p9ffYRtUbMRZBNFYwf0RK8koUMx6dGUfwylLOKmaT2cs4wSW96QoYUSXAyEtpg==",
       "dependencies": {
-        "@types/unist": "^3.0.0"
+        "@types/unist": "^2.0.0"
       },
       "funding": {
         "type": "opencollective",
@@ -29338,11 +30157,11 @@
       }
     },
     "node_modules/unist-util-stringify-position": {
-      "version": "4.0.0",
-      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
-      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
+      "version": "3.0.3",
+      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-3.0.3.tgz",
+      "integrity": "sha512-k5GzIBZ/QatR8N5X2y+drfpWG8IDBzdnVj6OInRNWm1oXrzydiaAT2OQiA8DPRRZyAKb9b6I2a6PxYklZD0gKg==",
       "dependencies": {
-        "@types/unist": "^3.0.0"
+        "@types/unist": "^2.0.0"
       },
       "funding": {
         "type": "opencollective",
@@ -29350,13 +30169,13 @@
       }
     },
     "node_modules/unist-util-visit": {
-      "version": "5.0.0",
-      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz",
-      "integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",
+      "version": "4.1.2",
+      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-4.1.2.tgz",
+      "integrity": "sha512-MSd8OUGISqHdVvfY9TPhyK2VdUrPgxkUtWSuMHF6XAAFuL4LokseigBnZtPnJMu+FbynTkFNnFlyjxpVKujMRg==",
       "dependencies": {
-        "@types/unist": "^3.0.0",
-        "unist-util-is": "^6.0.0",
-        "unist-util-visit-parents": "^6.0.0"
+        "@types/unist": "^2.0.0",
+        "unist-util-is": "^5.0.0",
+        "unist-util-visit-parents": "^5.1.1"
       },
       "funding": {
         "type": "opencollective",
@@ -29364,12 +30183,12 @@
       }
     },
     "node_modules/unist-util-visit-parents": {
-      "version": "6.0.1",
-      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",
-      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
+      "version": "5.1.3",
+      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-5.1.3.tgz",
+      "integrity": "sha512-x6+y8g7wWMyQhL1iZfhIPhDAs7Xwbn9nRosDXl7qoPTSCy0yNxnKc+hWokFifWQIDGi154rdUqKvbCa4+1kLhg==",
       "dependencies": {
-        "@types/unist": "^3.0.0",
-        "unist-util-is": "^6.0.0"
+        "@types/unist": "^2.0.0",
+        "unist-util-is": "^5.0.0"
       },
       "funding": {
         "type": "opencollective",
@@ -29604,6 +30423,14 @@
         "react": ">=16.9.0"
       }
     },
+    "node_modules/use-sync-external-store": {
+      "version": "1.5.0",
+      "resolved": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz",
+      "integrity": "sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==",
+      "peerDependencies": {
+        "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"
+      }
+    },
     "node_modules/util": {
       "version": "0.11.1",
       "resolved": "https://registry.npmjs.org/util/-/util-0.11.1.tgz",
@@ -29660,6 +30487,39 @@
         "uuid": "dist/bin/uuid"
       }
     },
+    "node_modules/uvu": {
+      "version": "0.5.6",
+      "resolved": "https://registry.npmjs.org/uvu/-/uvu-0.5.6.tgz",
+      "integrity": "sha512-+g8ENReyr8YsOc6fv/NVJs2vFdHBnBNdfE49rshrTzDWOlUx4Gq7KOS2GD8eqhy2j+Ejq29+SbKH8yjkAqXqoA==",
+      "dependencies": {
+        "dequal": "^2.0.0",
+        "diff": "^5.0.0",
+        "kleur": "^4.0.3",
+        "sade": "^1.7.3"
+      },
+      "bin": {
+        "uvu": "bin.js"
+      },
+      "engines": {
+        "node": ">=8"
+      }
+    },
+    "node_modules/uvu/node_modules/diff": {
+      "version": "5.2.0",
+      "resolved": "https://registry.npmjs.org/diff/-/diff-5.2.0.tgz",
+      "integrity": "sha512-uIFDxqpRZGZ6ThOk84hEfqWoHx2devRFvpTZcTHur85vImfaxUbTW9Ryh4CpCuDnToOP1CEtXKIgytHBPVff5A==",
+      "engines": {
+        "node": ">=0.3.1"
+      }
+    },
+    "node_modules/uvu/node_modules/kleur": {
+      "version": "4.1.5",
+      "resolved": "https://registry.npmjs.org/kleur/-/kleur-4.1.5.tgz",
+      "integrity": "sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==",
+      "engines": {
+        "node": ">=6"
+      }
+    },
     "node_modules/v8-compile-cache": {
       "version": "2.4.0",
       "resolved": "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.4.0.tgz",
@@ -29728,12 +30588,14 @@
       }
     },
     "node_modules/vfile": {
-      "version": "6.0.3",
-      "resolved": "https://registry.npmjs.org/vfile/-/vfile-6.0.3.tgz",
-      "integrity": "sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==",
+      "version": "5.3.7",
+      "resolved": "https://registry.npmjs.org/vfile/-/vfile-5.3.7.tgz",
+      "integrity": "sha512-r7qlzkgErKjobAmyNIkkSpizsFPYiUPuJb5pNW1RB4JcYVZhs4lIbVqk8XPk033CV/1z8ss5pkax8SuhGpcG8g==",
       "dependencies": {
-        "@types/unist": "^3.0.0",
-        "vfile-message": "^4.0.0"
+        "@types/unist": "^2.0.0",
+        "is-buffer": "^2.0.0",
+        "unist-util-stringify-position": "^3.0.0",
+        "vfile-message": "^3.0.0"
       },
       "funding": {
         "type": "opencollective",
@@ -29741,18 +30603,40 @@
       }
     },
     "node_modules/vfile-message": {
-      "version": "4.0.2",
-      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
-      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
+      "version": "3.1.4",
+      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-3.1.4.tgz",
+      "integrity": "sha512-fa0Z6P8HUrQN4BZaX05SIVXic+7kE3b05PWAtPuYP9QLHsLKYR7/AlLW3NtOrpXRLeawpDLMsVkmk5DG0NXgWw==",
       "dependencies": {
-        "@types/unist": "^3.0.0",
-        "unist-util-stringify-position": "^4.0.0"
+        "@types/unist": "^2.0.0",
+        "unist-util-stringify-position": "^3.0.0"
       },
       "funding": {
         "type": "opencollective",
         "url": "https://opencollective.com/unified"
       }
     },
+    "node_modules/vfile/node_modules/is-buffer": {
+      "version": "2.0.5",
+      "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-2.0.5.tgz",
+      "integrity": "sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ==",
+      "funding": [
+        {
+          "type": "github",
+          "url": "https://github.com/sponsors/feross"
+        },
+        {
+          "type": "patreon",
+          "url": "https://www.patreon.com/feross"
+        },
+        {
+          "type": "consulting",
+          "url": "https://feross.org/support"
+        }
+      ],
+      "engines": {
+        "node": ">=4"
+      }
+    },
     "node_modules/viewport-mercator-project": {
       "version": "6.2.3",
       "resolved": "https://registry.npmjs.org/viewport-mercator-project/-/viewport-mercator-project-6.2.3.tgz",
@@ -29787,6 +30671,11 @@
         "browser-process-hrtime": "^1.0.0"
       }
     },
+    "node_modules/w3c-keyname": {
+      "version": "2.2.8",
+      "resolved": "https://registry.npmjs.org/w3c-keyname/-/w3c-keyname-2.2.8.tgz",
+      "integrity": "sha512-dpojBhNsCNN7T82Tm7k26A6G9ML3NkhDsnw9n/eoxSRlVBB4CEtIQ/KTCLI2Fwf3ataSXRhYFkQi3SlnFwPvPQ=="
+    },
     "node_modules/w3c-xmlserializer": {
       "version": "2.0.0",
       "resolved": "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-2.0.0.tgz",
@@ -31480,15 +32369,6 @@
       "funding": {
         "url": "https://github.com/sponsors/sindresorhus"
       }
-    },
-    "node_modules/zwitch": {
-      "version": "2.0.4",
-      "resolved": "https://registry.npmjs.org/zwitch/-/zwitch-2.0.4.tgz",
-      "integrity": "sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==",
-      "funding": {
-        "type": "github",
-        "url": "https://github.com/sponsors/wooorm"
-      }
     }
   },
   "dependencies": {
@@ -36122,6 +37002,11 @@
         "sort-object": "^3.0.3"
       }
     },
+    "@microsoft/fetch-event-source": {
+      "version": "2.0.1",
+      "resolved": "https://registry.npmjs.org/@microsoft/fetch-event-source/-/fetch-event-source-2.0.1.tgz",
+      "integrity": "sha512-W6CLUJ2eBMw3Rec70qrsEW0jOm/3twwJv21mrmj2yORiaVmVYGS4sSS5yUwvQc1ZlDLYGPnClVWmUUMagKNsfA=="
+    },
     "@mrmlnc/readdir-enhanced": {
       "version": "2.2.1",
       "resolved": "https://registry.npmjs.org/@mrmlnc/readdir-enhanced/-/readdir-enhanced-2.2.1.tgz",
@@ -36210,6 +37095,11 @@
         }
       }
     },
+    "@popperjs/core": {
+      "version": "2.11.8",
+      "resolved": "https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz",
+      "integrity": "sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A=="
+    },
     "@probe.gl/env": {
       "version": "3.6.0",
       "resolved": "https://registry.npmjs.org/@probe.gl/env/-/env-3.6.0.tgz",
@@ -36302,6 +37192,11 @@
         "reselect": "^4.1.8"
       }
     },
+    "@remirror/core-constants": {
+      "version": "3.0.0",
+      "resolved": "https://registry.npmjs.org/@remirror/core-constants/-/core-constants-3.0.0.tgz",
+      "integrity": "sha512-42aWfPrimMfDKDi4YegyS7x+/0tlzaqwPQCULLanv3DMIlu96KTJR0fM5isWX2UViOqlGnX6YFgqWepcX+XMNg=="
+    },
     "@rollup/plugin-node-resolve": {
       "version": "7.1.3",
       "resolved": "https://registry.npmjs.org/@rollup/plugin-node-resolve/-/plugin-node-resolve-7.1.3.tgz",
@@ -36547,6 +37442,300 @@
         "@babel/runtime": "^7.12.5"
       }
     },
+    "@tiptap/core": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/core/-/core-2.23.1.tgz",
+      "integrity": "sha512-EURGKGsEPrwxvOPi9gA+BsczvsECJNV+xgTAGWHmEtU4YJ0AulYrCX3b7FK+aiduVhThIHDoG/Mmvmb/HPLRhQ=="
+    },
+    "@tiptap/extension-blockquote": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-blockquote/-/extension-blockquote-2.23.1.tgz",
+      "integrity": "sha512-GI3s+uFU88LWRaDG20Z9yIu2av3Usn8kw2lkm2ntwX1K6/mQBS/zkGhWr/FSwWOlMtTzYFxF4Ttb0e+hn67A/A=="
+    },
+    "@tiptap/extension-bold": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-bold/-/extension-bold-2.23.1.tgz",
+      "integrity": "sha512-OM4RxuZeOqpYRN1G/YpXSE8tZ3sVtT2XlO3qKa74qf+htWz8W3x4X0oQCrHrRTDSAA1wbmeZU3QghAIHnbvP/A=="
+    },
+    "@tiptap/extension-bubble-menu": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-bubble-menu/-/extension-bubble-menu-2.23.1.tgz",
+      "integrity": "sha512-tupuvrlZMTziVZXJuCVjLwllUnux/an9BtTYHpoRyLX9Hg0v7Kh39k9x58zJaoW8Q/Oc/qxPhbJpyOqhE1rLeg==",
+      "requires": {
+        "tippy.js": "^6.3.7"
+      }
+    },
+    "@tiptap/extension-bullet-list": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-bullet-list/-/extension-bullet-list-2.23.1.tgz",
+      "integrity": "sha512-0g9U42m+boLJZP3x9KoJHDCp9WD5abaVdqNbTg9sFPDNsepb7Zaeu8AEB+yZLP/fuTI1I4ko6qkdr3UaaIYcmA=="
+    },
+    "@tiptap/extension-character-count": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-character-count/-/extension-character-count-2.23.1.tgz",
+      "integrity": "sha512-QGHiXrLEAuIQCLNqFJrYFsVlnOpdFuhuYoDbxyQe9Z5+twMYI8aO1e4NPKT+cpWtCWFDdnSVrUWZsEGXYEuCjg=="
+    },
+    "@tiptap/extension-code": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-code/-/extension-code-2.23.1.tgz",
+      "integrity": "sha512-3IOdE40m0UTR2+UXui69o/apLtutAbtzfgmMxD6q0qlRvVqz99QEfk9RPHDNlUqJtYCL4TD+sj7UclBsDdgVXA=="
+    },
+    "@tiptap/extension-code-block": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-code-block/-/extension-code-block-2.23.1.tgz",
+      "integrity": "sha512-eYzJVUR13BhSE/TYAMZihGBId+XiwhnTPqGcSFo+zx89It/vxwDLvAUn0PReMNI7ULKPTw8orUt2fVKSarb2DQ=="
+    },
+    "@tiptap/extension-code-block-lowlight": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-code-block-lowlight/-/extension-code-block-lowlight-2.23.1.tgz",
+      "integrity": "sha512-EQwLyO1zKViSXcwdsnzurWOdVS1W3oiNuFnkxxSGtU8hlRbkDP4+dY8c/jRE2XejfJuxz7zY7iGg594d8+5/SA=="
+    },
+    "@tiptap/extension-color": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-color/-/extension-color-2.23.1.tgz",
+      "integrity": "sha512-WkTvez+L/tuqp967PRi8t4L09qwZ5lOWCl8ppVgjjzgAOVpZ7Fgl7hg0h1b7nyO3Vf1JXr396p7iyw4TxKHRrw=="
+    },
+    "@tiptap/extension-document": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-document/-/extension-document-2.23.1.tgz",
+      "integrity": "sha512-2nkIkGVsaMJkpd024E6vXK+5XNz8VOVWp/pM6bbXpuv0HnGPrfLdh4ruuFc+xTQ3WPOmpSu8ygtujt4I1o9/6g=="
+    },
+    "@tiptap/extension-dropcursor": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-dropcursor/-/extension-dropcursor-2.23.1.tgz",
+      "integrity": "sha512-GyVp+o/RVrKlLdrQvtIpJGphFGogiPjcPCkAFcrfY1vDY1EYxfVZELC96gG1mUT1BO8FUD3hmbpkWi9l8/6O4A=="
+    },
+    "@tiptap/extension-floating-menu": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-floating-menu/-/extension-floating-menu-2.23.1.tgz",
+      "integrity": "sha512-GMWkpH+p/OUOk1Y5UGOnKuHSDEVBN7DhYIJiWt5g9LK/mpPeuqoCmQg3RQDgjtZXb74SlxLK2pS/3YcAnemdfQ==",
+      "requires": {
+        "tippy.js": "^6.3.7"
+      }
+    },
+    "@tiptap/extension-gapcursor": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-gapcursor/-/extension-gapcursor-2.23.1.tgz",
+      "integrity": "sha512-iP+TiFIGZEbOvYAs04pI14mLI4xqbt64Da91TgMF1FNZUrG+9eWKjqbcHLQREuK3Qnjn5f0DI4nOBv61FlnPmA=="
+    },
+    "@tiptap/extension-hard-break": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-hard-break/-/extension-hard-break-2.23.1.tgz",
+      "integrity": "sha512-YF66EVxnBxt1bHPx6fUUSSXK1Vg+/9baJ0AfJ12hCSPCgSjUclRuNmWIH5ikVfByOmPV1xlrN9wryLoSEBcNRQ=="
+    },
+    "@tiptap/extension-heading": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-heading/-/extension-heading-2.23.1.tgz",
+      "integrity": "sha512-5BPoli9wudiAOgSyK8309jyRhFyu5vd02lNChfpHwxUudzIJ/L+0E6FcwrDcw+yXh23cx7F5SSjtFQ7AobxlDQ=="
+    },
+    "@tiptap/extension-highlight": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-highlight/-/extension-highlight-2.23.1.tgz",
+      "integrity": "sha512-cvfQ6Ise2aZp0eABRbKNfyfU1Fd304q8nAtAKDCRQKzGbSPTEM4zhCp1RcEmt/93I1cLvKaciQFBoldVl1xaGw=="
+    },
+    "@tiptap/extension-history": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-history/-/extension-history-2.23.1.tgz",
+      "integrity": "sha512-1rp2CRjM+P58oGEgeUUDSk0ch67ngIGbGJOOjiBGKU9GIVhI2j4uSwsYTAa9qYMjMUI6IyH1xJpsY2hLKcBOtg=="
+    },
+    "@tiptap/extension-horizontal-rule": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-horizontal-rule/-/extension-horizontal-rule-2.23.1.tgz",
+      "integrity": "sha512-uHEF0jpmhtgAxjKw8/s5ipEeTnu99f9RVMGAlmcthJ5Fx9TzH0MvtH4dtBNEu5MXC7+0bNsnncWo125AAbCohg=="
+    },
+    "@tiptap/extension-image": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-image/-/extension-image-2.23.1.tgz",
+      "integrity": "sha512-8j2FLBWKq6j27aQcOAlmyKixxHflW8j5FhxLgPPS6hithgFQVET4OYH+1c6r7Qd/T4YoAqt/0PmNZ/gYWI9gzg=="
+    },
+    "@tiptap/extension-italic": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-italic/-/extension-italic-2.23.1.tgz",
+      "integrity": "sha512-a+cPzffaC/1AKMmZ1Ka6l81xmTgcalf8NXfBuFCUTf5r7uI9NIgXnLo9hg+jR9F4K+bwhC4/UbMvQQzAjh0c0A=="
+    },
+    "@tiptap/extension-link": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-link/-/extension-link-2.23.1.tgz",
+      "integrity": "sha512-zMD0V8djkvwRYACzd8EvFXXNLQH5poJt6aHC9/8uest6njRhlrRjSjwG5oa+xHW4A76XfAH0A5BPj6ZxZnAUQg==",
+      "requires": {
+        "linkifyjs": "^4.2.0"
+      }
+    },
+    "@tiptap/extension-list-item": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-list-item/-/extension-list-item-2.23.1.tgz",
+      "integrity": "sha512-wVrRp6KAiyjFVFGmn+ojisP64Bsd+ZPdqQBYVbebBx1skZeW0uhG60d7vUkWHi0gCgxHZDfvDbXpfnOD0INRWw=="
+    },
+    "@tiptap/extension-mention": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-mention/-/extension-mention-2.23.1.tgz",
+      "integrity": "sha512-YaHg2ZqNb2Vur5hBoswzu8pJ3kV54PPonR8gHgelD83T1s8vBqdl03WL2NUVciovEbENnirePLDfhKaFjRXSzQ=="
+    },
+    "@tiptap/extension-ordered-list": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-ordered-list/-/extension-ordered-list-2.23.1.tgz",
+      "integrity": "sha512-Zp+qognyNgoaJ9bxkBwIuWJEnQ67RdsHXzv3YOdeGRbkUhd8LT6OL7P0mAuNbMBU8MwHxyJ7C7NsyzwzuVbFzA=="
+    },
+    "@tiptap/extension-paragraph": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-paragraph/-/extension-paragraph-2.23.1.tgz",
+      "integrity": "sha512-LLEPizt1ALE7Ek6prlJ1uhoUCT8C/a3PdZpCh3DshM1L3Kv9TENlaJL2GhFl8SVUCwHmWHvXg30+4tIRFBedaQ=="
+    },
+    "@tiptap/extension-placeholder": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-placeholder/-/extension-placeholder-2.23.1.tgz",
+      "integrity": "sha512-zSkCljVpxJh3GHW7ppFNYhHPjYKmS3Tw0e74BOGzb5TqP57GRvnPgDGg4fr6kDsSWMo9minnNM3PDA7kNT+PRQ=="
+    },
+    "@tiptap/extension-strike": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-strike/-/extension-strike-2.23.1.tgz",
+      "integrity": "sha512-hAT9peYkKezRGp/EcPQKtyYQT+2XGUbb26toTr9XIBQIeQCuCpT+FirPrDMrMVWPwcJt7Rv+AzoVjDuBs9wE0A=="
+    },
+    "@tiptap/extension-subscript": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-subscript/-/extension-subscript-2.23.1.tgz",
+      "integrity": "sha512-LFUwe90E90f38aES6ka0Jg7kUG3WTMq3M+7qRu6skEx4+izVB6ub5RTvA56trQlWefWiYeJZptf8xfIKdcwGSw=="
+    },
+    "@tiptap/extension-superscript": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-superscript/-/extension-superscript-2.23.1.tgz",
+      "integrity": "sha512-us1p+AZD6B3+eWuhO83WP3Kd9MBSApOh5c4S9MEavmmQxHAvFk7sv/tuyQPgBQNxvNnpV5z0mfFEtqiMrCo22A=="
+    },
+    "@tiptap/extension-table": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-table/-/extension-table-2.23.1.tgz",
+      "integrity": "sha512-v8qRKIM74U51KOFK90tpN1Yy1wj7wceifbXXnwySN4H7s0jmOdN//u73QPjqkynWw9xKo/dRjSWqiEoSRhno1w=="
+    },
+    "@tiptap/extension-table-cell": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-table-cell/-/extension-table-cell-2.23.1.tgz",
+      "integrity": "sha512-znCUmoZAZvXEw9nqZCzfAC+m++t5PaEh0//cmCqSuvB43c6s2oEFg06JiJnrxWBe8kMZUyzyCHMMoPMLE2vaDQ=="
+    },
+    "@tiptap/extension-table-header": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-table-header/-/extension-table-header-2.23.1.tgz",
+      "integrity": "sha512-LIdSdgLPvQ5fGZA3PtV1l6l3dAkgJ5OWsxM0srXBwteHDf9SHMlJ2VJ/X6YtYk3itLkLWeMpqDkPHw9Sq0lbVg=="
+    },
+    "@tiptap/extension-table-of-contents": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-table-of-contents/-/extension-table-of-contents-2.23.1.tgz",
+      "integrity": "sha512-WsUqSsCP42Ot5WRHchWzgq3QsxkuhmoCDYgs7X/EtqVhGixT9+fGqPufNAnaAI8v1hMoRNYKZWSH3Gg1BNs2fw==",
+      "requires": {
+        "uuid": "^10.0.0"
+      },
+      "dependencies": {
+        "uuid": {
+          "version": "10.0.0",
+          "resolved": "https://registry.npmjs.org/uuid/-/uuid-10.0.0.tgz",
+          "integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ=="
+        }
+      }
+    },
+    "@tiptap/extension-table-row": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-table-row/-/extension-table-row-2.23.1.tgz",
+      "integrity": "sha512-zREF2+tXI8LMapTJVQtbj+SIdzi3hwFte9JugzTDym+16kAPU7Smjsi5wLa7Y1AD8M882OwRWZ9qkHL7L99eZQ=="
+    },
+    "@tiptap/extension-task-item": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-task-item/-/extension-task-item-2.23.1.tgz",
+      "integrity": "sha512-v6lNiuKYlEUmoQq2ISzX3dH60ThuALXgKIZclrvVuQsYohHQ2A+5joKEzkdNoWmairNxouAfSa+1p+HEwDaORg=="
+    },
+    "@tiptap/extension-task-list": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-task-list/-/extension-task-list-2.23.1.tgz",
+      "integrity": "sha512-JN5Fai/H4PoYaUoqbxoPzUwW9PmN+qmBvbzt3ciCJEG2lXlmF0Z5vGYU/bJ5013C7jTyPJlp0qE7sZAPdF3WdQ=="
+    },
+    "@tiptap/extension-text": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-text/-/extension-text-2.23.1.tgz",
+      "integrity": "sha512-XK0D/eyS1Vm5yUrCtkS0AfgyKLJqpi8nJivCOux/JLhhC4x87R1+mI8NoFDYZJ5ic/afREPSBB8jORqOi0qIHg=="
+    },
+    "@tiptap/extension-text-align": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-text-align/-/extension-text-align-2.23.1.tgz",
+      "integrity": "sha512-AugfX5iJXM7RJfv/AoXfEmXw70ZFlAIRm0tdSxYwkHvt1f0fqdtdTsHth7OGPnudH91h4FoVgHBktcHcPpEUfg=="
+    },
+    "@tiptap/extension-text-style": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-text-style/-/extension-text-style-2.23.1.tgz",
+      "integrity": "sha512-fZn1GePlL27pUFfKXKoRZo4L4pZP9dUjNNiS/eltLpbi/SenJ15UKhAoHtN1KQvNGJsWkYN49FjnnltU8qvQ+Q=="
+    },
+    "@tiptap/extension-typography": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-typography/-/extension-typography-2.23.1.tgz",
+      "integrity": "sha512-G8QyK0jTs7VyR7yZPvOGAxU5O90sW7e/N9hmUjsYEY9ttUgFIsFMlihLMXlgqfLtHfgtyzjW5RqU4KVCfpuIQA=="
+    },
+    "@tiptap/extension-underline": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/extension-underline/-/extension-underline-2.23.1.tgz",
+      "integrity": "sha512-MTG+VlGStXD3uj7iPzZU8aJrqxoQyX45WX6xTouezaZzh/NQtTyVWwJqNGE7fsMhxirpJ+at0IZmqlDTjAhEDQ=="
+    },
+    "@tiptap/pm": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/pm/-/pm-2.23.1.tgz",
+      "integrity": "sha512-iAx4rP0k4Xd9Ywh+Gpaz5IWfY2CYRpiwVXWekTHLlNRFtrVIWVpMxaQr2mvRU2g0Ca6rz5w3KzkHBMqrI3dIBA==",
+      "requires": {
+        "prosemirror-changeset": "^2.3.0",
+        "prosemirror-collab": "^1.3.1",
+        "prosemirror-commands": "^1.6.2",
+        "prosemirror-dropcursor": "^1.8.1",
+        "prosemirror-gapcursor": "^1.3.2",
+        "prosemirror-history": "^1.4.1",
+        "prosemirror-inputrules": "^1.4.0",
+        "prosemirror-keymap": "^1.2.2",
+        "prosemirror-markdown": "^1.13.1",
+        "prosemirror-menu": "^1.2.4",
+        "prosemirror-model": "^1.23.0",
+        "prosemirror-schema-basic": "^1.2.3",
+        "prosemirror-schema-list": "^1.4.1",
+        "prosemirror-state": "^1.4.3",
+        "prosemirror-tables": "^1.6.4",
+        "prosemirror-trailing-node": "^3.0.0",
+        "prosemirror-transform": "^1.10.2",
+        "prosemirror-view": "^1.37.0"
+      }
+    },
+    "@tiptap/react": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/react/-/react-2.23.1.tgz",
+      "integrity": "sha512-eP8jksq9rY1PBIYKNUgG5c92gCTNK40bmzno+cEAu8RMYs5M6BSXwMaZSjjqHM53Juvj6ake90+7kLOM8XlXfA==",
+      "requires": {
+        "@tiptap/extension-bubble-menu": "^2.23.1",
+        "@tiptap/extension-floating-menu": "^2.23.1",
+        "@types/use-sync-external-store": "^0.0.6",
+        "fast-deep-equal": "^3",
+        "use-sync-external-store": "^1"
+      }
+    },
+    "@tiptap/starter-kit": {
+      "version": "2.23.1",
+      "resolved": "https://registry.npmjs.org/@tiptap/starter-kit/-/starter-kit-2.23.1.tgz",
+      "integrity": "sha512-rrImwzJbKSHoFa+WdNU4I0evXcMiQ4yRm737sxvNJwYItT6fXIxrbRT7nJDmtYu2TflcfT1KklEnSrzz1hhYRw==",
+      "requires": {
+        "@tiptap/core": "^2.23.1",
+        "@tiptap/extension-blockquote": "^2.23.1",
+        "@tiptap/extension-bold": "^2.23.1",
+        "@tiptap/extension-bullet-list": "^2.23.1",
+        "@tiptap/extension-code": "^2.23.1",
+        "@tiptap/extension-code-block": "^2.23.1",
+        "@tiptap/extension-document": "^2.23.1",
+        "@tiptap/extension-dropcursor": "^2.23.1",
+        "@tiptap/extension-gapcursor": "^2.23.1",
+        "@tiptap/extension-hard-break": "^2.23.1",
+        "@tiptap/extension-heading": "^2.23.1",
+        "@tiptap/extension-history": "^2.23.1",
+        "@tiptap/extension-horizontal-rule": "^2.23.1",
+        "@tiptap/extension-italic": "^2.23.1",
+        "@tiptap/extension-list-item": "^2.23.1",
+        "@tiptap/extension-ordered-list": "^2.23.1",
+        "@tiptap/extension-paragraph": "^2.23.1",
+        "@tiptap/extension-strike": "^2.23.1",
+        "@tiptap/extension-text": "^2.23.1",
+        "@tiptap/extension-text-style": "^2.23.1",
+        "@tiptap/pm": "^2.23.1"
+      }
+    },
     "@tootallnate/once": {
       "version": "1.1.2",
       "resolved": "https://registry.npmjs.org/@tootallnate/once/-/once-1.1.2.tgz",
@@ -36816,14 +38005,6 @@
       "resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.8.tgz",
       "integrity": "sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w=="
     },
-    "@types/estree-jsx": {
-      "version": "1.0.5",
-      "resolved": "https://registry.npmjs.org/@types/estree-jsx/-/estree-jsx-1.0.5.tgz",
-      "integrity": "sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg==",
-      "requires": {
-        "@types/estree": "*"
-      }
-    },
     "@types/geojson": {
       "version": "7946.0.16",
       "resolved": "https://registry.npmjs.org/@types/geojson/-/geojson-7946.0.16.tgz",
@@ -36847,11 +38028,11 @@
       }
     },
     "@types/hast": {
-      "version": "3.0.4",
-      "resolved": "https://registry.npmjs.org/@types/hast/-/hast-3.0.4.tgz",
-      "integrity": "sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==",
+      "version": "2.3.10",
+      "resolved": "https://registry.npmjs.org/@types/hast/-/hast-2.3.10.tgz",
+      "integrity": "sha512-McWspRw8xx8J9HurkVBfYj0xKoE25tOFlHGdx4MJ5xORQrMGZNqJhVQWaIbm6Oyla5kYOXtDiopzKRJzEOkwJw==",
       "requires": {
-        "@types/unist": "*"
+        "@types/unist": "^2"
       }
     },
     "@types/history": {
@@ -36919,6 +38100,11 @@
       "resolved": "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz",
       "integrity": "sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ=="
     },
+    "@types/linkify-it": {
+      "version": "5.0.0",
+      "resolved": "https://registry.npmjs.org/@types/linkify-it/-/linkify-it-5.0.0.tgz",
+      "integrity": "sha512-sVDA58zAw4eWAffKOaQH5/5j3XeayukzDk+ewSsnv3p4yJEZHCCzMDiZM8e0OUrRvmpGZ85jf4yDHkHsgBNr9Q=="
+    },
     "@types/mapbox__point-geometry": {
       "version": "0.1.4",
       "resolved": "https://registry.npmjs.org/@types/mapbox__point-geometry/-/mapbox__point-geometry-0.1.4.tgz",
@@ -36934,13 +38120,27 @@
         "@types/pbf": "*"
       }
     },
+    "@types/markdown-it": {
+      "version": "14.1.2",
+      "resolved": "https://registry.npmjs.org/@types/markdown-it/-/markdown-it-14.1.2.tgz",
+      "integrity": "sha512-promo4eFwuiW+TfGxhi+0x3czqTYJkG8qB17ZUJiVF10Xm7NLVRSLUsfRTU/6h1e24VvRnXCx+hG7li58lkzog==",
+      "requires": {
+        "@types/linkify-it": "^5",
+        "@types/mdurl": "^2"
+      }
+    },
     "@types/mdast": {
-      "version": "4.0.4",
-      "resolved": "https://registry.npmjs.org/@types/mdast/-/mdast-4.0.4.tgz",
-      "integrity": "sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==",
+      "version": "3.0.15",
+      "resolved": "https://registry.npmjs.org/@types/mdast/-/mdast-3.0.15.tgz",
+      "integrity": "sha512-LnwD+mUEfxWMa1QpDraczIn6k0Ee3SMicuYSSzS6ZYl2gKS09EClnJYGd8Du6rfc5r/GZEk5o1mRb8TaTj03sQ==",
       "requires": {
-        "@types/unist": "*"
+        "@types/unist": "^2"
       }
+    },
+    "@types/mdurl": {
+      "version": "2.0.0",
+      "resolved": "https://registry.npmjs.org/@types/mdurl/-/mdurl-2.0.0.tgz",
+      "integrity": "sha512-RGdgjQUZba5p6QEFAVx2OGb8rQDL/cPRG7GiedRzMcJ1tYnUANBncjbSB1NRGwbvjcPeikRABz2nshyPk1bhWg=="
     },
     "@types/minimatch": {
       "version": "5.1.2",
@@ -37092,9 +38292,14 @@
       }
     },
     "@types/unist": {
-      "version": "3.0.3",
-      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-3.0.3.tgz",
-      "integrity": "sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q=="
+      "version": "2.0.11",
+      "resolved": "https://registry.npmjs.org/@types/unist/-/unist-2.0.11.tgz",
+      "integrity": "sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA=="
+    },
+    "@types/use-sync-external-store": {
+      "version": "0.0.6",
+      "resolved": "https://registry.npmjs.org/@types/use-sync-external-store/-/use-sync-external-store-0.0.6.tgz",
+      "integrity": "sha512-zFDAD+tlpf2r4asuHEj0XH6pY6i0g5NeAHPn+15wk3BV6JA69eERFXC1gyGThDkVa1zCyKr5jox1+2LbV/AMLg=="
     },
     "@types/webpack": {
       "version": "4.41.40",
@@ -37231,11 +38436,6 @@
       "resolved": "https://registry.npmjs.org/@umijs/use-params/-/use-params-1.0.9.tgz",
       "integrity": "sha512-QlN0RJSBVQBwLRNxbxjQ5qzqYIGn+K7USppMoIOVlf7fxXHsnQZ2bEsa6Pm74bt6DVQxpUE8HqvdStn6Y9FV1w=="
     },
-    "@ungap/structured-clone": {
-      "version": "1.3.0",
-      "resolved": "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz",
-      "integrity": "sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g=="
-    },
     "@webassemblyjs/ast": {
       "version": "1.9.0",
       "resolved": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.9.0.tgz",
@@ -38865,11 +40065,6 @@
       "resolved": "https://registry.npmjs.org/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.3.0.tgz",
       "integrity": "sha512-/4YgnZS8y1UXXmC02xD5rRrBEu6T5ub+mQHLNRj0fzTRbgdBYhsNo2V5EqwgqrExjxsjtF/OpAKAMkKsxbD5XQ=="
     },
-    "ccount": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/ccount/-/ccount-2.0.1.tgz",
-      "integrity": "sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg=="
-    },
     "center-align": {
       "version": "0.1.3",
       "resolved": "https://registry.npmjs.org/center-align/-/center-align-0.1.3.tgz",
@@ -38898,21 +40093,6 @@
       "resolved": "https://registry.npmjs.org/character-entities/-/character-entities-2.0.2.tgz",
       "integrity": "sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ=="
     },
-    "character-entities-html4": {
-      "version": "2.1.0",
-      "resolved": "https://registry.npmjs.org/character-entities-html4/-/character-entities-html4-2.1.0.tgz",
-      "integrity": "sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA=="
-    },
-    "character-entities-legacy": {
-      "version": "3.0.0",
-      "resolved": "https://registry.npmjs.org/character-entities-legacy/-/character-entities-legacy-3.0.0.tgz",
-      "integrity": "sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ=="
-    },
-    "character-reference-invalid": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/character-reference-invalid/-/character-reference-invalid-2.0.1.tgz",
-      "integrity": "sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw=="
-    },
     "check-types": {
       "version": "11.2.3",
       "resolved": "https://registry.npmjs.org/check-types/-/check-types-11.2.3.tgz",
@@ -39751,6 +40931,11 @@
       "resolved": "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz",
       "integrity": "sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ=="
     },
+    "crelt": {
+      "version": "1.0.6",
+      "resolved": "https://registry.npmjs.org/crelt/-/crelt-1.0.6.tgz",
+      "integrity": "sha512-VQ2MBenTq1fWZUH9DJNGti7kKv6EeAuYr3cLwxUWhIu1baTaXh4Ib5W2CqHVqib4/MqbYGJqiL3Zb8GJZr3l4g=="
+    },
     "cross-spawn": {
       "version": "7.0.6",
       "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz",
@@ -40860,14 +42045,6 @@
         }
       }
     },
-    "devlop": {
-      "version": "1.1.0",
-      "resolved": "https://registry.npmjs.org/devlop/-/devlop-1.1.0.tgz",
-      "integrity": "sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==",
-      "requires": {
-        "dequal": "^2.0.0"
-      }
-    },
     "diff": {
       "version": "4.0.2",
       "resolved": "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz",
@@ -41977,11 +43154,6 @@
       "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz",
       "integrity": "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="
     },
-    "estree-util-is-identifier-name": {
-      "version": "3.0.0",
-      "resolved": "https://registry.npmjs.org/estree-util-is-identifier-name/-/estree-util-is-identifier-name-3.0.0.tgz",
-      "integrity": "sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg=="
-    },
     "estree-walker": {
       "version": "1.0.1",
       "resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-1.0.1.tgz",
@@ -43173,35 +44345,10 @@
         "function-bind": "^1.1.2"
       }
     },
-    "hast-util-to-jsx-runtime": {
-      "version": "2.3.6",
-      "resolved": "https://registry.npmjs.org/hast-util-to-jsx-runtime/-/hast-util-to-jsx-runtime-2.3.6.tgz",
-      "integrity": "sha512-zl6s8LwNyo1P9uw+XJGvZtdFF1GdAkOg8ujOw+4Pyb76874fLps4ueHXDhXWdk6YHQ6OgUtinliG7RsYvCbbBg==",
-      "requires": {
-        "@types/estree": "^1.0.0",
-        "@types/hast": "^3.0.0",
-        "@types/unist": "^3.0.0",
-        "comma-separated-tokens": "^2.0.0",
-        "devlop": "^1.0.0",
-        "estree-util-is-identifier-name": "^3.0.0",
-        "hast-util-whitespace": "^3.0.0",
-        "mdast-util-mdx-expression": "^2.0.0",
-        "mdast-util-mdx-jsx": "^3.0.0",
-        "mdast-util-mdxjs-esm": "^2.0.0",
-        "property-information": "^7.0.0",
-        "space-separated-tokens": "^2.0.0",
-        "style-to-js": "^1.0.0",
-        "unist-util-position": "^5.0.0",
-        "vfile-message": "^4.0.0"
-      }
-    },
     "hast-util-whitespace": {
-      "version": "3.0.0",
-      "resolved": "https://registry.npmjs.org/hast-util-whitespace/-/hast-util-whitespace-3.0.0.tgz",
-      "integrity": "sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw==",
-      "requires": {
-        "@types/hast": "^3.0.0"
-      }
+      "version": "2.0.1",
+      "resolved": "https://registry.npmjs.org/hast-util-whitespace/-/hast-util-whitespace-2.0.1.tgz",
+      "integrity": "sha512-nAxA0v8+vXSBDt3AnRUNjyRIQ0rD+ntpbAp4LnPkumc5M9yUbSMa4XDU9Q6etY4f1Wp4bNgvc1yjiZtsTTrSng=="
     },
     "he": {
       "version": "1.2.0",
@@ -43326,11 +44473,6 @@
         }
       }
     },
-    "html-url-attributes": {
-      "version": "3.0.1",
-      "resolved": "https://registry.npmjs.org/html-url-attributes/-/html-url-attributes-3.0.1.tgz",
-      "integrity": "sha512-ol6UPyBWqsrO6EJySPz2O7ZSr856WDrEzM5zMqp+FJJLGMW35cLYmmZnl0vztAZxRUoNZJFTCohfjuIJ8I4QBQ=="
-    },
     "html-webpack-plugin": {
       "version": "4.5.0",
       "resolved": "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-4.5.0.tgz",
@@ -43601,9 +44743,9 @@
       "integrity": "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew=="
     },
     "inline-style-parser": {
-      "version": "0.2.4",
-      "resolved": "https://registry.npmjs.org/inline-style-parser/-/inline-style-parser-0.2.4.tgz",
-      "integrity": "sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q=="
+      "version": "0.1.1",
+      "resolved": "https://registry.npmjs.org/inline-style-parser/-/inline-style-parser-0.1.1.tgz",
+      "integrity": "sha512-7NXolsK4CAS5+xvdj5OMMbI962hU/wvwoxk+LWR9Ek9bVtyuuYScDN6eS0rUm6TxApFpw7CX1o4uJzcd4AyD3Q=="
     },
     "inline-style-prefixer": {
       "version": "7.0.1",
@@ -43705,20 +44847,6 @@
         "hasown": "^2.0.0"
       }
     },
-    "is-alphabetical": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/is-alphabetical/-/is-alphabetical-2.0.1.tgz",
-      "integrity": "sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ=="
-    },
-    "is-alphanumerical": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/is-alphanumerical/-/is-alphanumerical-2.0.1.tgz",
-      "integrity": "sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==",
-      "requires": {
-        "is-alphabetical": "^2.0.0",
-        "is-decimal": "^2.0.0"
-      }
-    },
     "is-any-array": {
       "version": "2.0.1",
       "resolved": "https://registry.npmjs.org/is-any-array/-/is-any-array-2.0.1.tgz",
@@ -43852,11 +44980,6 @@
         "has-tostringtag": "^1.0.2"
       }
     },
-    "is-decimal": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/is-decimal/-/is-decimal-2.0.1.tgz",
-      "integrity": "sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A=="
-    },
     "is-descriptor": {
       "version": "1.0.3",
       "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.3.tgz",
@@ -43926,11 +45049,6 @@
         "is-extglob": "^2.1.1"
       }
     },
-    "is-hexadecimal": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/is-hexadecimal/-/is-hexadecimal-2.0.1.tgz",
-      "integrity": "sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg=="
-    },
     "is-map": {
       "version": "2.0.3",
       "resolved": "https://registry.npmjs.org/is-map/-/is-map-2.0.3.tgz",
@@ -45385,6 +46503,19 @@
       "resolved": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz",
       "integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg=="
     },
+    "linkify-it": {
+      "version": "5.0.0",
+      "resolved": "https://registry.npmjs.org/linkify-it/-/linkify-it-5.0.0.tgz",
+      "integrity": "sha512-5aHCbzQRADcdP+ATqnDuhhJ/MRIqDkZX5pyjFHRRysS8vZ5AbqGEoFIb6pYHPZ+L/OC2Lc+xT8uHVVR5CAK/wQ==",
+      "requires": {
+        "uc.micro": "^2.0.0"
+      }
+    },
+    "linkifyjs": {
+      "version": "4.3.1",
+      "resolved": "https://registry.npmjs.org/linkifyjs/-/linkifyjs-4.3.1.tgz",
+      "integrity": "sha512-DRSlB9DKVW04c4SUdGvKK5FR6be45lTU9M76JnngqPeeGDqPwYc0zdUErtsNVMtxPXgUWV4HbXbnC4sNyBxkYg=="
+    },
     "loader-runner": {
       "version": "2.4.0",
       "resolved": "https://registry.npmjs.org/loader-runner/-/loader-runner-2.4.0.tgz",
@@ -45485,11 +46616,6 @@
       "resolved": "https://registry.npmjs.org/longest/-/longest-1.0.1.tgz",
       "integrity": "sha512-k+yt5n3l48JU4k8ftnKG6V7u32wyH2NfKzeMto9F/QRE0amxy/LayxwlvjjkZEIzqR+19IrtFO8p5kB9QaYUFg=="
     },
-    "longest-streak": {
-      "version": "3.1.0",
-      "resolved": "https://registry.npmjs.org/longest-streak/-/longest-streak-3.1.0.tgz",
-      "integrity": "sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g=="
-    },
     "loose-envify": {
       "version": "1.4.0",
       "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz",
@@ -45670,6 +46796,36 @@
         }
       }
     },
+    "markdown-it": {
+      "version": "14.1.0",
+      "resolved": "https://registry.npmjs.org/markdown-it/-/markdown-it-14.1.0.tgz",
+      "integrity": "sha512-a54IwgWPaeBCAAsv13YgmALOF1elABB08FxO9i+r4VFk5Vl4pKokRPeX8u5TCgSsPi6ec1otfLjdOpVcgbpshg==",
+      "requires": {
+        "argparse": "^2.0.1",
+        "entities": "^4.4.0",
+        "linkify-it": "^5.0.0",
+        "mdurl": "^2.0.0",
+        "punycode.js": "^2.3.1",
+        "uc.micro": "^2.1.0"
+      },
+      "dependencies": {
+        "argparse": {
+          "version": "2.0.1",
+          "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz",
+          "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="
+        },
+        "entities": {
+          "version": "4.5.0",
+          "resolved": "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz",
+          "integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw=="
+        }
+      }
+    },
+    "markdown-it-task-lists": {
+      "version": "2.1.1",
+      "resolved": "https://registry.npmjs.org/markdown-it-task-lists/-/markdown-it-task-lists-2.1.1.tgz",
+      "integrity": "sha512-TxFAc76Jnhb2OUu+n3yz9RMu4CwGfaT788br6HhEDlvWfdeJcLUsxk1Hgw2yJio0OXsxv7pyIPmvECY7bMbluA=="
+    },
     "material-colors": {
       "version": "1.2.6",
       "resolved": "https://registry.npmjs.org/material-colors/-/material-colors-1.2.6.tgz",
@@ -45690,117 +46846,56 @@
         "safe-buffer": "^5.1.2"
       }
     },
+    "mdast-util-definitions": {
+      "version": "5.1.2",
+      "resolved": "https://registry.npmjs.org/mdast-util-definitions/-/mdast-util-definitions-5.1.2.tgz",
+      "integrity": "sha512-8SVPMuHqlPME/z3gqVwWY4zVXn8lqKv/pAhC57FuJ40ImXyBpmO5ukh98zB2v7Blql2FiHjHv9LVztSIqjY+MA==",
+      "requires": {
+        "@types/mdast": "^3.0.0",
+        "@types/unist": "^2.0.0",
+        "unist-util-visit": "^4.0.0"
+      }
+    },
     "mdast-util-from-markdown": {
-      "version": "2.0.2",
-      "resolved": "https://registry.npmjs.org/mdast-util-from-markdown/-/mdast-util-from-markdown-2.0.2.tgz",
-      "integrity": "sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA==",
+      "version": "1.3.1",
+      "resolved": "https://registry.npmjs.org/mdast-util-from-markdown/-/mdast-util-from-markdown-1.3.1.tgz",
+      "integrity": "sha512-4xTO/M8c82qBcnQc1tgpNtubGUW/Y1tBQ1B0i5CtSoelOLKFYlElIr3bvgREYYO5iRqbMY1YuqZng0GVOI8Qww==",
       "requires": {
-        "@types/mdast": "^4.0.0",
-        "@types/unist": "^3.0.0",
+        "@types/mdast": "^3.0.0",
+        "@types/unist": "^2.0.0",
         "decode-named-character-reference": "^1.0.0",
-        "devlop": "^1.0.0",
-        "mdast-util-to-string": "^4.0.0",
-        "micromark": "^4.0.0",
-        "micromark-util-decode-numeric-character-reference": "^2.0.0",
-        "micromark-util-decode-string": "^2.0.0",
-        "micromark-util-normalize-identifier": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0",
-        "unist-util-stringify-position": "^4.0.0"
-      }
-    },
-    "mdast-util-mdx-expression": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/mdast-util-mdx-expression/-/mdast-util-mdx-expression-2.0.1.tgz",
-      "integrity": "sha512-J6f+9hUp+ldTZqKRSg7Vw5V6MqjATc+3E4gf3CFNcuZNWD8XdyI6zQ8GqH7f8169MM6P7hMBRDVGnn7oHB9kXQ==",
-      "requires": {
-        "@types/estree-jsx": "^1.0.0",
-        "@types/hast": "^3.0.0",
-        "@types/mdast": "^4.0.0",
-        "devlop": "^1.0.0",
-        "mdast-util-from-markdown": "^2.0.0",
-        "mdast-util-to-markdown": "^2.0.0"
-      }
-    },
-    "mdast-util-mdx-jsx": {
-      "version": "3.2.0",
-      "resolved": "https://registry.npmjs.org/mdast-util-mdx-jsx/-/mdast-util-mdx-jsx-3.2.0.tgz",
-      "integrity": "sha512-lj/z8v0r6ZtsN/cGNNtemmmfoLAFZnjMbNyLzBafjzikOM+glrjNHPlf6lQDOTccj9n5b0PPihEBbhneMyGs1Q==",
-      "requires": {
-        "@types/estree-jsx": "^1.0.0",
-        "@types/hast": "^3.0.0",
-        "@types/mdast": "^4.0.0",
-        "@types/unist": "^3.0.0",
-        "ccount": "^2.0.0",
-        "devlop": "^1.1.0",
-        "mdast-util-from-markdown": "^2.0.0",
-        "mdast-util-to-markdown": "^2.0.0",
-        "parse-entities": "^4.0.0",
-        "stringify-entities": "^4.0.0",
-        "unist-util-stringify-position": "^4.0.0",
-        "vfile-message": "^4.0.0"
-      }
-    },
-    "mdast-util-mdxjs-esm": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/mdast-util-mdxjs-esm/-/mdast-util-mdxjs-esm-2.0.1.tgz",
-      "integrity": "sha512-EcmOpxsZ96CvlP03NghtH1EsLtr0n9Tm4lPUJUBccV9RwUOneqSycg19n5HGzCf+10LozMRSObtVr3ee1WoHtg==",
-      "requires": {
-        "@types/estree-jsx": "^1.0.0",
-        "@types/hast": "^3.0.0",
-        "@types/mdast": "^4.0.0",
-        "devlop": "^1.0.0",
-        "mdast-util-from-markdown": "^2.0.0",
-        "mdast-util-to-markdown": "^2.0.0"
-      }
-    },
-    "mdast-util-phrasing": {
-      "version": "4.1.0",
-      "resolved": "https://registry.npmjs.org/mdast-util-phrasing/-/mdast-util-phrasing-4.1.0.tgz",
-      "integrity": "sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==",
-      "requires": {
-        "@types/mdast": "^4.0.0",
-        "unist-util-is": "^6.0.0"
+        "mdast-util-to-string": "^3.1.0",
+        "micromark": "^3.0.0",
+        "micromark-util-decode-numeric-character-reference": "^1.0.0",
+        "micromark-util-decode-string": "^1.0.0",
+        "micromark-util-normalize-identifier": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0",
+        "unist-util-stringify-position": "^3.0.0",
+        "uvu": "^0.5.0"
       }
     },
     "mdast-util-to-hast": {
-      "version": "13.2.0",
-      "resolved": "https://registry.npmjs.org/mdast-util-to-hast/-/mdast-util-to-hast-13.2.0.tgz",
-      "integrity": "sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA==",
+      "version": "12.3.0",
+      "resolved": "https://registry.npmjs.org/mdast-util-to-hast/-/mdast-util-to-hast-12.3.0.tgz",
+      "integrity": "sha512-pits93r8PhnIoU4Vy9bjW39M2jJ6/tdHyja9rrot9uujkN7UTU9SDnE6WNJz/IGyQk3XHX6yNNtrBH6cQzm8Hw==",
       "requires": {
-        "@types/hast": "^3.0.0",
-        "@types/mdast": "^4.0.0",
-        "@ungap/structured-clone": "^1.0.0",
-        "devlop": "^1.0.0",
-        "micromark-util-sanitize-uri": "^2.0.0",
+        "@types/hast": "^2.0.0",
+        "@types/mdast": "^3.0.0",
+        "mdast-util-definitions": "^5.0.0",
+        "micromark-util-sanitize-uri": "^1.1.0",
         "trim-lines": "^3.0.0",
-        "unist-util-position": "^5.0.0",
-        "unist-util-visit": "^5.0.0",
-        "vfile": "^6.0.0"
-      }
-    },
-    "mdast-util-to-markdown": {
-      "version": "2.1.2",
-      "resolved": "https://registry.npmjs.org/mdast-util-to-markdown/-/mdast-util-to-markdown-2.1.2.tgz",
-      "integrity": "sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA==",
-      "requires": {
-        "@types/mdast": "^4.0.0",
-        "@types/unist": "^3.0.0",
-        "longest-streak": "^3.0.0",
-        "mdast-util-phrasing": "^4.0.0",
-        "mdast-util-to-string": "^4.0.0",
-        "micromark-util-classify-character": "^2.0.0",
-        "micromark-util-decode-string": "^2.0.0",
-        "unist-util-visit": "^5.0.0",
-        "zwitch": "^2.0.0"
+        "unist-util-generated": "^2.0.0",
+        "unist-util-position": "^4.0.0",
+        "unist-util-visit": "^4.0.0"
       }
     },
     "mdast-util-to-string": {
-      "version": "4.0.0",
-      "resolved": "https://registry.npmjs.org/mdast-util-to-string/-/mdast-util-to-string-4.0.0.tgz",
-      "integrity": "sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==",
+      "version": "3.2.0",
+      "resolved": "https://registry.npmjs.org/mdast-util-to-string/-/mdast-util-to-string-3.2.0.tgz",
+      "integrity": "sha512-V4Zn/ncyN1QNSqSBxTrMOLpjr+IKdHl2v3KVLoWmDPscP4r9GcCi71gjgvUV1SFSKh92AjAG4peFuBl2/YgCJg==",
       "requires": {
-        "@types/mdast": "^4.0.0"
+        "@types/mdast": "^3.0.0"
       }
     },
     "mdn-data": {
@@ -45808,6 +46903,11 @@
       "resolved": "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz",
       "integrity": "sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow=="
     },
+    "mdurl": {
+      "version": "2.0.0",
+      "resolved": "https://registry.npmjs.org/mdurl/-/mdurl-2.0.0.tgz",
+      "integrity": "sha512-Lf+9+2r+Tdp5wXDXC4PcIBjTDtq4UKjCPMQhKIuzpJNW0b96kVqSwW0bT7FhRSfmAiFYgP+SCRvdrDozfh0U5w=="
+    },
     "media-typer": {
       "version": "0.3.0",
       "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz",
@@ -45853,215 +46953,215 @@
       "integrity": "sha512-jo1OfR4TaEwd5HOrt5+tAZ9mqT4jmpNAusXtyfNzqVm9uiSYFZlKM1wYL4oU7azZW/PxQW53wM0S6OR1JHNa2g=="
     },
     "micromark": {
-      "version": "4.0.2",
-      "resolved": "https://registry.npmjs.org/micromark/-/micromark-4.0.2.tgz",
-      "integrity": "sha512-zpe98Q6kvavpCr1NPVSCMebCKfD7CA2NqZ+rykeNhONIJBpc1tFKt9hucLGwha3jNTNI8lHpctWJWoimVF4PfA==",
+      "version": "3.2.0",
+      "resolved": "https://registry.npmjs.org/micromark/-/micromark-3.2.0.tgz",
+      "integrity": "sha512-uD66tJj54JLYq0De10AhWycZWGQNUvDI55xPgk2sQM5kn1JYlhbCMTtEeT27+vAhW2FBQxLlOmS3pmA7/2z4aA==",
       "requires": {
         "@types/debug": "^4.0.0",
         "debug": "^4.0.0",
         "decode-named-character-reference": "^1.0.0",
-        "devlop": "^1.0.0",
-        "micromark-core-commonmark": "^2.0.0",
-        "micromark-factory-space": "^2.0.0",
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-chunked": "^2.0.0",
-        "micromark-util-combine-extensions": "^2.0.0",
-        "micromark-util-decode-numeric-character-reference": "^2.0.0",
-        "micromark-util-encode": "^2.0.0",
-        "micromark-util-normalize-identifier": "^2.0.0",
-        "micromark-util-resolve-all": "^2.0.0",
-        "micromark-util-sanitize-uri": "^2.0.0",
-        "micromark-util-subtokenize": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-core-commonmark": "^1.0.1",
+        "micromark-factory-space": "^1.0.0",
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-chunked": "^1.0.0",
+        "micromark-util-combine-extensions": "^1.0.0",
+        "micromark-util-decode-numeric-character-reference": "^1.0.0",
+        "micromark-util-encode": "^1.0.0",
+        "micromark-util-normalize-identifier": "^1.0.0",
+        "micromark-util-resolve-all": "^1.0.0",
+        "micromark-util-sanitize-uri": "^1.0.0",
+        "micromark-util-subtokenize": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.1",
+        "uvu": "^0.5.0"
       }
     },
     "micromark-core-commonmark": {
-      "version": "2.0.3",
-      "resolved": "https://registry.npmjs.org/micromark-core-commonmark/-/micromark-core-commonmark-2.0.3.tgz",
-      "integrity": "sha512-RDBrHEMSxVFLg6xvnXmb1Ayr2WzLAWjeSATAoxwKYJV94TeNavgoIdA0a9ytzDSVzBy2YKFK+emCPOEibLeCrg==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-core-commonmark/-/micromark-core-commonmark-1.1.0.tgz",
+      "integrity": "sha512-BgHO1aRbolh2hcrzL2d1La37V0Aoz73ymF8rAcKnohLy93titmv62E0gP8Hrx9PKcKrqCZ1BbLGbP3bEhoXYlw==",
       "requires": {
         "decode-named-character-reference": "^1.0.0",
-        "devlop": "^1.0.0",
-        "micromark-factory-destination": "^2.0.0",
-        "micromark-factory-label": "^2.0.0",
-        "micromark-factory-space": "^2.0.0",
-        "micromark-factory-title": "^2.0.0",
-        "micromark-factory-whitespace": "^2.0.0",
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-chunked": "^2.0.0",
-        "micromark-util-classify-character": "^2.0.0",
-        "micromark-util-html-tag-name": "^2.0.0",
-        "micromark-util-normalize-identifier": "^2.0.0",
-        "micromark-util-resolve-all": "^2.0.0",
-        "micromark-util-subtokenize": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-factory-destination": "^1.0.0",
+        "micromark-factory-label": "^1.0.0",
+        "micromark-factory-space": "^1.0.0",
+        "micromark-factory-title": "^1.0.0",
+        "micromark-factory-whitespace": "^1.0.0",
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-chunked": "^1.0.0",
+        "micromark-util-classify-character": "^1.0.0",
+        "micromark-util-html-tag-name": "^1.0.0",
+        "micromark-util-normalize-identifier": "^1.0.0",
+        "micromark-util-resolve-all": "^1.0.0",
+        "micromark-util-subtokenize": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.1",
+        "uvu": "^0.5.0"
       }
     },
     "micromark-factory-destination": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-factory-destination/-/micromark-factory-destination-2.0.1.tgz",
-      "integrity": "sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-factory-destination/-/micromark-factory-destination-1.1.0.tgz",
+      "integrity": "sha512-XaNDROBgx9SgSChd69pjiGKbV+nfHGDPVYFs5dOoDd7ZnMAE+Cuu91BCpsY8RT2NP9vo/B8pds2VQNCLiu0zhg==",
       "requires": {
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0"
       }
     },
     "micromark-factory-label": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-factory-label/-/micromark-factory-label-2.0.1.tgz",
-      "integrity": "sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-factory-label/-/micromark-factory-label-1.1.0.tgz",
+      "integrity": "sha512-OLtyez4vZo/1NjxGhcpDSbHQ+m0IIGnT8BoPamh+7jVlzLJBH98zzuCoUeMxvM6WsNeh8wx8cKvqLiPHEACn0w==",
       "requires": {
-        "devlop": "^1.0.0",
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0",
+        "uvu": "^0.5.0"
       }
     },
     "micromark-factory-space": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-factory-space/-/micromark-factory-space-2.0.1.tgz",
-      "integrity": "sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-factory-space/-/micromark-factory-space-1.1.0.tgz",
+      "integrity": "sha512-cRzEj7c0OL4Mw2v6nwzttyOZe8XY/Z8G0rzmWQZTBi/jjwyw/U4uqKtUORXQrR5bAZZnbTI/feRV/R7hc4jQYQ==",
       "requires": {
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-types": "^1.0.0"
       }
     },
     "micromark-factory-title": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-factory-title/-/micromark-factory-title-2.0.1.tgz",
-      "integrity": "sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-factory-title/-/micromark-factory-title-1.1.0.tgz",
+      "integrity": "sha512-J7n9R3vMmgjDOCY8NPw55jiyaQnH5kBdV2/UXCtZIpnHH3P6nHUKaH7XXEYuWwx/xUJcawa8plLBEjMPU24HzQ==",
       "requires": {
-        "micromark-factory-space": "^2.0.0",
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-factory-space": "^1.0.0",
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0"
       }
     },
     "micromark-factory-whitespace": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-factory-whitespace/-/micromark-factory-whitespace-2.0.1.tgz",
-      "integrity": "sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-factory-whitespace/-/micromark-factory-whitespace-1.1.0.tgz",
+      "integrity": "sha512-v2WlmiymVSp5oMg+1Q0N1Lxmt6pMhIHD457whWM7/GUlEks1hI9xj5w3zbc4uuMKXGisksZk8DzP2UyGbGqNsQ==",
       "requires": {
-        "micromark-factory-space": "^2.0.0",
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-factory-space": "^1.0.0",
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0"
       }
     },
     "micromark-util-character": {
-      "version": "2.1.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-character/-/micromark-util-character-2.1.1.tgz",
-      "integrity": "sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==",
+      "version": "1.2.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-character/-/micromark-util-character-1.2.0.tgz",
+      "integrity": "sha512-lXraTwcX3yH/vMDaFWCQJP1uIszLVebzUa3ZHdrgxr7KEU/9mL4mVgCpGbyhvNLNlauROiNUq7WN5u7ndbY6xg==",
       "requires": {
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0"
       }
     },
     "micromark-util-chunked": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-chunked/-/micromark-util-chunked-2.0.1.tgz",
-      "integrity": "sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-chunked/-/micromark-util-chunked-1.1.0.tgz",
+      "integrity": "sha512-Ye01HXpkZPNcV6FiyoW2fGZDUw4Yc7vT0E9Sad83+bEDiCJ1uXu0S3mr8WLpsz3HaG3x2q0HM6CTuPdcZcluFQ==",
       "requires": {
-        "micromark-util-symbol": "^2.0.0"
+        "micromark-util-symbol": "^1.0.0"
       }
     },
     "micromark-util-classify-character": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-classify-character/-/micromark-util-classify-character-2.0.1.tgz",
-      "integrity": "sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-classify-character/-/micromark-util-classify-character-1.1.0.tgz",
+      "integrity": "sha512-SL0wLxtKSnklKSUplok1WQFoGhUdWYKggKUiqhX+Swala+BtptGCu5iPRc+xvzJ4PXE/hwM3FNXsfEVgoZsWbw==",
       "requires": {
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0"
       }
     },
     "micromark-util-combine-extensions": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-combine-extensions/-/micromark-util-combine-extensions-2.0.1.tgz",
-      "integrity": "sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-combine-extensions/-/micromark-util-combine-extensions-1.1.0.tgz",
+      "integrity": "sha512-Q20sp4mfNf9yEqDL50WwuWZHUrCO4fEyeDCnMGmG5Pr0Cz15Uo7KBs6jq+dq0EgX4DPwwrh9m0X+zPV1ypFvUA==",
       "requires": {
-        "micromark-util-chunked": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-chunked": "^1.0.0",
+        "micromark-util-types": "^1.0.0"
       }
     },
     "micromark-util-decode-numeric-character-reference": {
-      "version": "2.0.2",
-      "resolved": "https://registry.npmjs.org/micromark-util-decode-numeric-character-reference/-/micromark-util-decode-numeric-character-reference-2.0.2.tgz",
-      "integrity": "sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-decode-numeric-character-reference/-/micromark-util-decode-numeric-character-reference-1.1.0.tgz",
+      "integrity": "sha512-m9V0ExGv0jB1OT21mrWcuf4QhP46pH1KkfWy9ZEezqHKAxkj4mPCy3nIH1rkbdMlChLHX531eOrymlwyZIf2iw==",
       "requires": {
-        "micromark-util-symbol": "^2.0.0"
+        "micromark-util-symbol": "^1.0.0"
       }
     },
     "micromark-util-decode-string": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-decode-string/-/micromark-util-decode-string-2.0.1.tgz",
-      "integrity": "sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-decode-string/-/micromark-util-decode-string-1.1.0.tgz",
+      "integrity": "sha512-YphLGCK8gM1tG1bd54azwyrQRjCFcmgj2S2GoJDNnh4vYtnL38JS8M4gpxzOPNyHdNEpheyWXCTnnTDY3N+NVQ==",
       "requires": {
         "decode-named-character-reference": "^1.0.0",
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-decode-numeric-character-reference": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0"
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-decode-numeric-character-reference": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0"
       }
     },
     "micromark-util-encode": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-encode/-/micromark-util-encode-2.0.1.tgz",
-      "integrity": "sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw=="
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-encode/-/micromark-util-encode-1.1.0.tgz",
+      "integrity": "sha512-EuEzTWSTAj9PA5GOAs992GzNh2dGQO52UvAbtSOMvXTxv3Criqb6IOzJUBCmEqrrXSblJIJBbFFv6zPxpreiJw=="
     },
     "micromark-util-html-tag-name": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-html-tag-name/-/micromark-util-html-tag-name-2.0.1.tgz",
-      "integrity": "sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA=="
+      "version": "1.2.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-html-tag-name/-/micromark-util-html-tag-name-1.2.0.tgz",
+      "integrity": "sha512-VTQzcuQgFUD7yYztuQFKXT49KghjtETQ+Wv/zUjGSGBioZnkA4P1XXZPT1FHeJA6RwRXSF47yvJ1tsJdoxwO+Q=="
     },
     "micromark-util-normalize-identifier": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-normalize-identifier/-/micromark-util-normalize-identifier-2.0.1.tgz",
-      "integrity": "sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-normalize-identifier/-/micromark-util-normalize-identifier-1.1.0.tgz",
+      "integrity": "sha512-N+w5vhqrBihhjdpM8+5Xsxy71QWqGn7HYNUvch71iV2PM7+E3uWGox1Qp90loa1ephtCxG2ftRV/Conitc6P2Q==",
       "requires": {
-        "micromark-util-symbol": "^2.0.0"
+        "micromark-util-symbol": "^1.0.0"
       }
     },
     "micromark-util-resolve-all": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-resolve-all/-/micromark-util-resolve-all-2.0.1.tgz",
-      "integrity": "sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-resolve-all/-/micromark-util-resolve-all-1.1.0.tgz",
+      "integrity": "sha512-b/G6BTMSg+bX+xVCshPTPyAu2tmA0E4X98NSR7eIbeC6ycCqCeE7wjfDIgzEbkzdEVJXRtOG4FbEm/uGbCRouA==",
       "requires": {
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-types": "^1.0.0"
       }
     },
     "micromark-util-sanitize-uri": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-sanitize-uri/-/micromark-util-sanitize-uri-2.0.1.tgz",
-      "integrity": "sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==",
+      "version": "1.2.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-sanitize-uri/-/micromark-util-sanitize-uri-1.2.0.tgz",
+      "integrity": "sha512-QO4GXv0XZfWey4pYFndLUKEAktKkG5kZTdUNaTAkzbuJxn2tNBOr+QtxR2XpWaMhbImT2dPzyLrPXLlPhph34A==",
       "requires": {
-        "micromark-util-character": "^2.0.0",
-        "micromark-util-encode": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0"
+        "micromark-util-character": "^1.0.0",
+        "micromark-util-encode": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0"
       }
     },
     "micromark-util-subtokenize": {
-      "version": "2.1.0",
-      "resolved": "https://registry.npmjs.org/micromark-util-subtokenize/-/micromark-util-subtokenize-2.1.0.tgz",
-      "integrity": "sha512-XQLu552iSctvnEcgXw6+Sx75GflAPNED1qx7eBJ+wydBb2KCbRZe+NwvIEEMM83uml1+2WSXpBAcp9IUCgCYWA==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-subtokenize/-/micromark-util-subtokenize-1.1.0.tgz",
+      "integrity": "sha512-kUQHyzRoxvZO2PuLzMt2P/dwVsTiivCK8icYTeR+3WgbuPqfHgPPy7nFKbeqRivBvn/3N3GBiNC+JRTMSxEC7A==",
       "requires": {
-        "devlop": "^1.0.0",
-        "micromark-util-chunked": "^2.0.0",
-        "micromark-util-symbol": "^2.0.0",
-        "micromark-util-types": "^2.0.0"
+        "micromark-util-chunked": "^1.0.0",
+        "micromark-util-symbol": "^1.0.0",
+        "micromark-util-types": "^1.0.0",
+        "uvu": "^0.5.0"
       }
     },
     "micromark-util-symbol": {
-      "version": "2.0.1",
-      "resolved": "https://registry.npmjs.org/micromark-util-symbol/-/micromark-util-symbol-2.0.1.tgz",
-      "integrity": "sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q=="
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-symbol/-/micromark-util-symbol-1.1.0.tgz",
+      "integrity": "sha512-uEjpEYY6KMs1g7QfJ2eX1SQEV+ZT4rUD3UcF6l57acZvLNK7PBZL+ty82Z1qhK1/yXIY4bdx04FKMgR0g4IAag=="
     },
     "micromark-util-types": {
-      "version": "2.0.2",
-      "resolved": "https://registry.npmjs.org/micromark-util-types/-/micromark-util-types-2.0.2.tgz",
-      "integrity": "sha512-Yw0ECSpJoViF1qTU4DC6NwtC4aWGt1EkzaQB8KPPyCRR8z9TWeV0HbEFGTO+ZY1wB22zmxnJqhPyTpOVCpeHTA=="
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/micromark-util-types/-/micromark-util-types-1.1.0.tgz",
+      "integrity": "sha512-ukRBgie8TIAcacscVHSiddHjO4k/q3pnedmzMQ4iwDcK0FtFCohKOlFbaOL/mPgfnPsL3C1ZyxJa4sbWrBl3jg=="
     },
     "micromatch": {
       "version": "3.1.10",
@@ -46379,6 +47479,11 @@
         }
       }
     },
+    "mri": {
+      "version": "1.2.0",
+      "resolved": "https://registry.npmjs.org/mri/-/mri-1.2.0.tgz",
+      "integrity": "sha512-tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA=="
+    },
     "ms": {
       "version": "2.1.3",
       "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz",
@@ -46897,6 +48002,11 @@
         }
       }
     },
+    "orderedmap": {
+      "version": "2.1.1",
+      "resolved": "https://registry.npmjs.org/orderedmap/-/orderedmap-2.1.1.tgz",
+      "integrity": "sha512-TvAWxi0nDe1j/rtMcWcIj94+Ffe6n7zhow33h40SKxmsmozs6dz/e+EajymfoFcHd7sxNn8yHM8839uixMOV6g=="
+    },
     "os-browserify": {
       "version": "0.3.0",
       "resolved": "https://registry.npmjs.org/os-browserify/-/os-browserify-0.3.0.tgz",
@@ -47005,27 +48115,6 @@
         "safe-buffer": "^5.2.1"
       }
     },
-    "parse-entities": {
-      "version": "4.0.2",
-      "resolved": "https://registry.npmjs.org/parse-entities/-/parse-entities-4.0.2.tgz",
-      "integrity": "sha512-GG2AQYWoLgL877gQIKeRPGO1xF9+eG1ujIb5soS5gPvLQ1y2o8FL90w2QWNdf9I361Mpp7726c+lj3U0qK1uGw==",
-      "requires": {
-        "@types/unist": "^2.0.0",
-        "character-entities-legacy": "^3.0.0",
-        "character-reference-invalid": "^2.0.0",
-        "decode-named-character-reference": "^1.0.0",
-        "is-alphanumerical": "^2.0.0",
-        "is-decimal": "^2.0.0",
-        "is-hexadecimal": "^2.0.0"
-      },
-      "dependencies": {
-        "@types/unist": {
-          "version": "2.0.11",
-          "resolved": "https://registry.npmjs.org/@types/unist/-/unist-2.0.11.tgz",
-          "integrity": "sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA=="
-        }
-      }
-    },
     "parse-json": {
       "version": "5.2.0",
       "resolved": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz",
@@ -49615,9 +50704,181 @@
       }
     },
     "property-information": {
-      "version": "7.1.0",
-      "resolved": "https://registry.npmjs.org/property-information/-/property-information-7.1.0.tgz",
-      "integrity": "sha512-TwEZ+X+yCJmYfL7TPUOcvBZ4QfoT5YenQiJuX//0th53DE6w0xxLEtfK3iyryQFddXuvkIk51EEgrJQ0WJkOmQ=="
+      "version": "6.5.0",
+      "resolved": "https://registry.npmjs.org/property-information/-/property-information-6.5.0.tgz",
+      "integrity": "sha512-PgTgs/BlvHxOu8QuEN7wi5A0OmXaBcHpmCSTehcs6Uuu9IkDIEo13Hy7n898RHfrQ49vKCoGeWZSaAK01nwVig=="
+    },
+    "prosemirror-changeset": {
+      "version": "2.3.1",
+      "resolved": "https://registry.npmjs.org/prosemirror-changeset/-/prosemirror-changeset-2.3.1.tgz",
+      "integrity": "sha512-j0kORIBm8ayJNl3zQvD1TTPHJX3g042et6y/KQhZhnPrruO8exkTgG8X+NRpj7kIyMMEx74Xb3DyMIBtO0IKkQ==",
+      "requires": {
+        "prosemirror-transform": "^1.0.0"
+      }
+    },
+    "prosemirror-collab": {
+      "version": "1.3.1",
+      "resolved": "https://registry.npmjs.org/prosemirror-collab/-/prosemirror-collab-1.3.1.tgz",
+      "integrity": "sha512-4SnynYR9TTYaQVXd/ieUvsVV4PDMBzrq2xPUWutHivDuOshZXqQ5rGbZM84HEaXKbLdItse7weMGOUdDVcLKEQ==",
+      "requires": {
+        "prosemirror-state": "^1.0.0"
+      }
+    },
+    "prosemirror-commands": {
+      "version": "1.7.1",
+      "resolved": "https://registry.npmjs.org/prosemirror-commands/-/prosemirror-commands-1.7.1.tgz",
+      "integrity": "sha512-rT7qZnQtx5c0/y/KlYaGvtG411S97UaL6gdp6RIZ23DLHanMYLyfGBV5DtSnZdthQql7W+lEVbpSfwtO8T+L2w==",
+      "requires": {
+        "prosemirror-model": "^1.0.0",
+        "prosemirror-state": "^1.0.0",
+        "prosemirror-transform": "^1.10.2"
+      }
+    },
+    "prosemirror-dropcursor": {
+      "version": "1.8.2",
+      "resolved": "https://registry.npmjs.org/prosemirror-dropcursor/-/prosemirror-dropcursor-1.8.2.tgz",
+      "integrity": "sha512-CCk6Gyx9+Tt2sbYk5NK0nB1ukHi2ryaRgadV/LvyNuO3ena1payM2z6Cg0vO1ebK8cxbzo41ku2DE5Axj1Zuiw==",
+      "requires": {
+        "prosemirror-state": "^1.0.0",
+        "prosemirror-transform": "^1.1.0",
+        "prosemirror-view": "^1.1.0"
+      }
+    },
+    "prosemirror-gapcursor": {
+      "version": "1.3.2",
+      "resolved": "https://registry.npmjs.org/prosemirror-gapcursor/-/prosemirror-gapcursor-1.3.2.tgz",
+      "integrity": "sha512-wtjswVBd2vaQRrnYZaBCbyDqr232Ed4p2QPtRIUK5FuqHYKGWkEwl08oQM4Tw7DOR0FsasARV5uJFvMZWxdNxQ==",
+      "requires": {
+        "prosemirror-keymap": "^1.0.0",
+        "prosemirror-model": "^1.0.0",
+        "prosemirror-state": "^1.0.0",
+        "prosemirror-view": "^1.0.0"
+      }
+    },
+    "prosemirror-history": {
+      "version": "1.4.1",
+      "resolved": "https://registry.npmjs.org/prosemirror-history/-/prosemirror-history-1.4.1.tgz",
+      "integrity": "sha512-2JZD8z2JviJrboD9cPuX/Sv/1ChFng+xh2tChQ2X4bB2HeK+rra/bmJ3xGntCcjhOqIzSDG6Id7e8RJ9QPXLEQ==",
+      "requires": {
+        "prosemirror-state": "^1.2.2",
+        "prosemirror-transform": "^1.0.0",
+        "prosemirror-view": "^1.31.0",
+        "rope-sequence": "^1.3.0"
+      }
+    },
+    "prosemirror-inputrules": {
+      "version": "1.5.0",
+      "resolved": "https://registry.npmjs.org/prosemirror-inputrules/-/prosemirror-inputrules-1.5.0.tgz",
+      "integrity": "sha512-K0xJRCmt+uSw7xesnHmcn72yBGTbY45vm8gXI4LZXbx2Z0jwh5aF9xrGQgrVPu0WbyFVFF3E/o9VhJYz6SQWnA==",
+      "requires": {
+        "prosemirror-state": "^1.0.0",
+        "prosemirror-transform": "^1.0.0"
+      }
+    },
+    "prosemirror-keymap": {
+      "version": "1.2.3",
+      "resolved": "https://registry.npmjs.org/prosemirror-keymap/-/prosemirror-keymap-1.2.3.tgz",
+      "integrity": "sha512-4HucRlpiLd1IPQQXNqeo81BGtkY8Ai5smHhKW9jjPKRc2wQIxksg7Hl1tTI2IfT2B/LgX6bfYvXxEpJl7aKYKw==",
+      "requires": {
+        "prosemirror-state": "^1.0.0",
+        "w3c-keyname": "^2.2.0"
+      }
+    },
+    "prosemirror-markdown": {
+      "version": "1.13.2",
+      "resolved": "https://registry.npmjs.org/prosemirror-markdown/-/prosemirror-markdown-1.13.2.tgz",
+      "integrity": "sha512-FPD9rHPdA9fqzNmIIDhhnYQ6WgNoSWX9StUZ8LEKapaXU9i6XgykaHKhp6XMyXlOWetmaFgGDS/nu/w9/vUc5g==",
+      "requires": {
+        "@types/markdown-it": "^14.0.0",
+        "markdown-it": "^14.0.0",
+        "prosemirror-model": "^1.25.0"
+      }
+    },
+    "prosemirror-menu": {
+      "version": "1.2.5",
+      "resolved": "https://registry.npmjs.org/prosemirror-menu/-/prosemirror-menu-1.2.5.tgz",
+      "integrity": "sha512-qwXzynnpBIeg1D7BAtjOusR+81xCp53j7iWu/IargiRZqRjGIlQuu1f3jFi+ehrHhWMLoyOQTSRx/IWZJqOYtQ==",
+      "requires": {
+        "crelt": "^1.0.0",
+        "prosemirror-commands": "^1.0.0",
+        "prosemirror-history": "^1.0.0",
+        "prosemirror-state": "^1.0.0"
+      }
+    },
+    "prosemirror-model": {
+      "version": "1.25.1",
+      "resolved": "https://registry.npmjs.org/prosemirror-model/-/prosemirror-model-1.25.1.tgz",
+      "integrity": "sha512-AUvbm7qqmpZa5d9fPKMvH1Q5bqYQvAZWOGRvxsB6iFLyycvC9MwNemNVjHVrWgjaoxAfY8XVg7DbvQ/qxvI9Eg==",
+      "requires": {
+        "orderedmap": "^2.0.0"
+      }
+    },
+    "prosemirror-schema-basic": {
+      "version": "1.2.4",
+      "resolved": "https://registry.npmjs.org/prosemirror-schema-basic/-/prosemirror-schema-basic-1.2.4.tgz",
+      "integrity": "sha512-ELxP4TlX3yr2v5rM7Sb70SqStq5NvI15c0j9j/gjsrO5vaw+fnnpovCLEGIcpeGfifkuqJwl4fon6b+KdrODYQ==",
+      "requires": {
+        "prosemirror-model": "^1.25.0"
+      }
+    },
+    "prosemirror-schema-list": {
+      "version": "1.5.1",
+      "resolved": "https://registry.npmjs.org/prosemirror-schema-list/-/prosemirror-schema-list-1.5.1.tgz",
+      "integrity": "sha512-927lFx/uwyQaGwJxLWCZRkjXG0p48KpMj6ueoYiu4JX05GGuGcgzAy62dfiV8eFZftgyBUvLx76RsMe20fJl+Q==",
+      "requires": {
+        "prosemirror-model": "^1.0.0",
+        "prosemirror-state": "^1.0.0",
+        "prosemirror-transform": "^1.7.3"
+      }
+    },
+    "prosemirror-state": {
+      "version": "1.4.3",
+      "resolved": "https://registry.npmjs.org/prosemirror-state/-/prosemirror-state-1.4.3.tgz",
+      "integrity": "sha512-goFKORVbvPuAQaXhpbemJFRKJ2aixr+AZMGiquiqKxaucC6hlpHNZHWgz5R7dS4roHiwq9vDctE//CZ++o0W1Q==",
+      "requires": {
+        "prosemirror-model": "^1.0.0",
+        "prosemirror-transform": "^1.0.0",
+        "prosemirror-view": "^1.27.0"
+      }
+    },
+    "prosemirror-tables": {
+      "version": "1.7.1",
+      "resolved": "https://registry.npmjs.org/prosemirror-tables/-/prosemirror-tables-1.7.1.tgz",
+      "integrity": "sha512-eRQ97Bf+i9Eby99QbyAiyov43iOKgWa7QCGly+lrDt7efZ1v8NWolhXiB43hSDGIXT1UXgbs4KJN3a06FGpr1Q==",
+      "requires": {
+        "prosemirror-keymap": "^1.2.2",
+        "prosemirror-model": "^1.25.0",
+        "prosemirror-state": "^1.4.3",
+        "prosemirror-transform": "^1.10.3",
+        "prosemirror-view": "^1.39.1"
+      }
+    },
+    "prosemirror-trailing-node": {
+      "version": "3.0.0",
+      "resolved": "https://registry.npmjs.org/prosemirror-trailing-node/-/prosemirror-trailing-node-3.0.0.tgz",
+      "integrity": "sha512-xiun5/3q0w5eRnGYfNlW1uU9W6x5MoFKWwq/0TIRgt09lv7Hcser2QYV8t4muXbEr+Fwo0geYn79Xs4GKywrRQ==",
+      "requires": {
+        "@remirror/core-constants": "3.0.0",
+        "escape-string-regexp": "^4.0.0"
+      }
+    },
+    "prosemirror-transform": {
+      "version": "1.10.4",
+      "resolved": "https://registry.npmjs.org/prosemirror-transform/-/prosemirror-transform-1.10.4.tgz",
+      "integrity": "sha512-pwDy22nAnGqNR1feOQKHxoFkkUtepoFAd3r2hbEDsnf4wp57kKA36hXsB3njA9FtONBEwSDnDeCiJe+ItD+ykw==",
+      "requires": {
+        "prosemirror-model": "^1.21.0"
+      }
+    },
+    "prosemirror-view": {
+      "version": "1.40.0",
+      "resolved": "https://registry.npmjs.org/prosemirror-view/-/prosemirror-view-1.40.0.tgz",
+      "integrity": "sha512-2G3svX0Cr1sJjkD/DYWSe3cfV5VPVTBOxI9XQEGWJDFEpsZb/gh4MV29ctv+OJx2RFX4BLt09i+6zaGM/ldkCw==",
+      "requires": {
+        "prosemirror-model": "^1.20.0",
+        "prosemirror-state": "^1.0.0",
+        "prosemirror-transform": "^1.1.0"
+      }
     },
     "protocol-buffers-schema": {
       "version": "3.6.0",
@@ -49708,6 +50969,11 @@
       "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz",
       "integrity": "sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ=="
     },
+    "punycode.js": {
+      "version": "2.3.1",
+      "resolved": "https://registry.npmjs.org/punycode.js/-/punycode.js-2.3.1.tgz",
+      "integrity": "sha512-uxFIHU0YlHYhDQtV4R9J6a52SLx28BCjT+4ieh7IGbgwVJWO+km431c4yRlREUAsAmt/uMjQUyQHNEPf0M39CA=="
+    },
     "q": {
       "version": "1.5.1",
       "resolved": "https://registry.npmjs.org/q/-/q-1.5.1.tgz",
@@ -50552,21 +51818,32 @@
       "integrity": "sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA=="
     },
     "react-markdown": {
-      "version": "10.1.0",
-      "resolved": "https://registry.npmjs.org/react-markdown/-/react-markdown-10.1.0.tgz",
-      "integrity": "sha512-qKxVopLT/TyA6BX3Ue5NwabOsAzm0Q7kAPwq6L+wWDwisYs7R8vZ0nRXqq6rkueboxpkjvLGU9fWifiX/ZZFxQ==",
+      "version": "8.0.7",
+      "resolved": "https://registry.npmjs.org/react-markdown/-/react-markdown-8.0.7.tgz",
+      "integrity": "sha512-bvWbzG4MtOU62XqBx3Xx+zB2raaFFsq4mYiAzfjXJMEz2sixgeAfraA3tvzULF02ZdOMUOKTBFFaZJDDrq+BJQ==",
       "requires": {
-        "@types/hast": "^3.0.0",
-        "@types/mdast": "^4.0.0",
-        "devlop": "^1.0.0",
-        "hast-util-to-jsx-runtime": "^2.0.0",
-        "html-url-attributes": "^3.0.0",
-        "mdast-util-to-hast": "^13.0.0",
-        "remark-parse": "^11.0.0",
-        "remark-rehype": "^11.0.0",
-        "unified": "^11.0.0",
-        "unist-util-visit": "^5.0.0",
-        "vfile": "^6.0.0"
+        "@types/hast": "^2.0.0",
+        "@types/prop-types": "^15.0.0",
+        "@types/unist": "^2.0.0",
+        "comma-separated-tokens": "^2.0.0",
+        "hast-util-whitespace": "^2.0.0",
+        "prop-types": "^15.0.0",
+        "property-information": "^6.0.0",
+        "react-is": "^18.0.0",
+        "remark-parse": "^10.0.0",
+        "remark-rehype": "^10.0.0",
+        "space-separated-tokens": "^2.0.0",
+        "style-to-object": "^0.4.0",
+        "unified": "^10.0.0",
+        "unist-util-visit": "^4.0.0",
+        "vfile": "^5.0.0"
+      },
+      "dependencies": {
+        "react-is": {
+          "version": "18.3.1",
+          "resolved": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz",
+          "integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg=="
+        }
       }
     },
     "react-redux": {
@@ -51116,26 +52393,24 @@
       "integrity": "sha512-G08Dxvm4iDN3MLM0EsP62EDV9IuhXPR6blNz6Utcp7zyV3tr4HVNINt6MpaRWbxoOHT3Q7YN2P+jaHX8vUbgog=="
     },
     "remark-parse": {
-      "version": "11.0.0",
-      "resolved": "https://registry.npmjs.org/remark-parse/-/remark-parse-11.0.0.tgz",
-      "integrity": "sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==",
+      "version": "10.0.2",
+      "resolved": "https://registry.npmjs.org/remark-parse/-/remark-parse-10.0.2.tgz",
+      "integrity": "sha512-3ydxgHa/ZQzG8LvC7jTXccARYDcRld3VfcgIIFs7bI6vbRSxJJmzgLEIIoYKyrfhaY+ujuWaf/PJiMZXoiCXgw==",
       "requires": {
-        "@types/mdast": "^4.0.0",
-        "mdast-util-from-markdown": "^2.0.0",
-        "micromark-util-types": "^2.0.0",
-        "unified": "^11.0.0"
+        "@types/mdast": "^3.0.0",
+        "mdast-util-from-markdown": "^1.0.0",
+        "unified": "^10.0.0"
       }
     },
     "remark-rehype": {
-      "version": "11.1.2",
-      "resolved": "https://registry.npmjs.org/remark-rehype/-/remark-rehype-11.1.2.tgz",
-      "integrity": "sha512-Dh7l57ianaEoIpzbp0PC9UKAdCSVklD8E5Rpw7ETfbTl3FqcOOgq5q2LVDhgGCkaBv7p24JXikPdvhhmHvKMsw==",
+      "version": "10.1.0",
+      "resolved": "https://registry.npmjs.org/remark-rehype/-/remark-rehype-10.1.0.tgz",
+      "integrity": "sha512-EFmR5zppdBp0WQeDVZ/b66CWJipB2q2VLNFMabzDSGR66Z2fQii83G5gTBbgGEnEEA0QRussvrFHxk1HWGJskw==",
       "requires": {
-        "@types/hast": "^3.0.0",
-        "@types/mdast": "^4.0.0",
-        "mdast-util-to-hast": "^13.0.0",
-        "unified": "^11.0.0",
-        "vfile": "^6.0.0"
+        "@types/hast": "^2.0.0",
+        "@types/mdast": "^3.0.0",
+        "mdast-util-to-hast": "^12.1.0",
+        "unified": "^10.0.0"
       }
     },
     "remove-trailing-separator": {
@@ -51543,6 +52818,11 @@
         }
       }
     },
+    "rope-sequence": {
+      "version": "1.3.4",
+      "resolved": "https://registry.npmjs.org/rope-sequence/-/rope-sequence-1.3.4.tgz",
+      "integrity": "sha512-UT5EDe2cu2E/6O4igUr5PSFs23nvvukicWHx6GnOPlHAiiYbzNuCRQCuiUdHJQcqKalLKlrYJnjY0ySGsXNQXQ=="
+    },
     "rsvp": {
       "version": "4.8.5",
       "resolved": "https://registry.npmjs.org/rsvp/-/rsvp-4.8.5.tgz",
@@ -51592,6 +52872,14 @@
         }
       }
     },
+    "sade": {
+      "version": "1.8.1",
+      "resolved": "https://registry.npmjs.org/sade/-/sade-1.8.1.tgz",
+      "integrity": "sha512-xal3CZX1Xlo/k4ApwCFrHVACi9fBqJ7V+mwhBsuf/1IOKbBy098Fex+Wa/5QMubw09pSZ/u8EY8PWgevJsXp1A==",
+      "requires": {
+        "mri": "^1.1.0"
+      }
+    },
     "safe-array-concat": {
       "version": "1.1.3",
       "resolved": "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.3.tgz",
@@ -52779,15 +54067,6 @@
         "es-object-atoms": "^1.0.0"
       }
     },
-    "stringify-entities": {
-      "version": "4.0.4",
-      "resolved": "https://registry.npmjs.org/stringify-entities/-/stringify-entities-4.0.4.tgz",
-      "integrity": "sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==",
-      "requires": {
-        "character-entities-html4": "^2.0.0",
-        "character-entities-legacy": "^3.0.0"
-      }
-    },
     "stringify-object": {
       "version": "3.3.0",
       "resolved": "https://registry.npmjs.org/stringify-object/-/stringify-object-3.3.0.tgz",
@@ -52864,20 +54143,12 @@
         }
       }
     },
-    "style-to-js": {
-      "version": "1.1.17",
-      "resolved": "https://registry.npmjs.org/style-to-js/-/style-to-js-1.1.17.tgz",
-      "integrity": "sha512-xQcBGDxJb6jjFCTzvQtfiPn6YvvP2O8U1MDIPNfJQlWMYfktPy+iGsHE7cssjs7y84d9fQaK4UF3RIJaAHSoYA==",
-      "requires": {
-        "style-to-object": "1.0.9"
-      }
-    },
     "style-to-object": {
-      "version": "1.0.9",
-      "resolved": "https://registry.npmjs.org/style-to-object/-/style-to-object-1.0.9.tgz",
-      "integrity": "sha512-G4qppLgKu/k6FwRpHiGiKPaPTFcG3g4wNVX/Qsfu+RqQM30E7Tyu/TEgxcL9PNLF5pdRLwQdE3YKKf+KF2Dzlw==",
+      "version": "0.4.4",
+      "resolved": "https://registry.npmjs.org/style-to-object/-/style-to-object-0.4.4.tgz",
+      "integrity": "sha512-HYNoHZa2GorYNyqiCaBgsxvcJIn7OHq6inEga+E6Ke3m5JkoqpQbnFssk4jwe+K7AhGa2fcha4wSOf1Kn01dMg==",
       "requires": {
-        "inline-style-parser": "0.2.4"
+        "inline-style-parser": "0.1.1"
       }
     },
     "stylehacks": {
@@ -53445,6 +54716,76 @@
       "resolved": "https://registry.npmjs.org/tinyqueue/-/tinyqueue-2.0.3.tgz",
       "integrity": "sha512-ppJZNDuKGgxzkHihX8v9v9G5f+18gzaTfrukGrq6ueg0lmH4nqVnA2IPG0AEH3jKEk2GRJCUhDoqpoiw3PHLBA=="
     },
+    "tippy.js": {
+      "version": "6.3.7",
+      "resolved": "https://registry.npmjs.org/tippy.js/-/tippy.js-6.3.7.tgz",
+      "integrity": "sha512-E1d3oP2emgJ9dRQZdf3Kkn0qJgI6ZLpyS5z6ZkY1DF3kaQaBsGZsndEpHwx+eC+tYM41HaSNvNtLx8tU57FzTQ==",
+      "requires": {
+        "@popperjs/core": "^2.9.0"
+      }
+    },
+    "tiptap-markdown": {
+      "version": "0.8.1",
+      "resolved": "https://registry.npmjs.org/tiptap-markdown/-/tiptap-markdown-0.8.1.tgz",
+      "integrity": "sha512-4bg6UHXF+1cIJHcAkcWd0d8zIweCvdIAY2dJjhm/e5oNWprP3gLUYa4aNAEFOKpD/Wh4VBqA7vi7yMj5vIy6xw==",
+      "requires": {
+        "@types/markdown-it": "^12.2.3",
+        "markdown-it": "^13.0.1",
+        "markdown-it-task-lists": "^2.1.1",
+        "prosemirror-markdown": "^1.11.0"
+      },
+      "dependencies": {
+        "@types/markdown-it": {
+          "version": "12.2.3",
+          "resolved": "https://registry.npmjs.org/@types/markdown-it/-/markdown-it-12.2.3.tgz",
+          "integrity": "sha512-GKMHFfv3458yYy+v/N8gjufHO6MSZKCOXpZc5GXIWWy8uldwfmPn98vp81gZ5f9SVw8YYBctgfJ22a2d7AOMeQ==",
+          "requires": {
+            "@types/linkify-it": "*",
+            "@types/mdurl": "*"
+          }
+        },
+        "argparse": {
+          "version": "2.0.1",
+          "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz",
+          "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="
+        },
+        "entities": {
+          "version": "3.0.1",
+          "resolved": "https://registry.npmjs.org/entities/-/entities-3.0.1.tgz",
+          "integrity": "sha512-WiyBqoomrwMdFG1e0kqvASYfnlb0lp8M5o5Fw2OFq1hNZxxcNk8Ik0Xm7LxzBhuidnZB/UtBqVCgUz3kBOP51Q=="
+        },
+        "linkify-it": {
+          "version": "4.0.1",
+          "resolved": "https://registry.npmjs.org/linkify-it/-/linkify-it-4.0.1.tgz",
+          "integrity": "sha512-C7bfi1UZmoj8+PQx22XyeXCuBlokoyWQL5pWSP+EI6nzRylyThouddufc2c1NDIcP9k5agmN9fLpA7VNJfIiqw==",
+          "requires": {
+            "uc.micro": "^1.0.1"
+          }
+        },
+        "markdown-it": {
+          "version": "13.0.2",
+          "resolved": "https://registry.npmjs.org/markdown-it/-/markdown-it-13.0.2.tgz",
+          "integrity": "sha512-FtwnEuuK+2yVU7goGn/MJ0WBZMM9ZPgU9spqlFs7/A/pDIUNSOQZhUgOqYCficIuR2QaFnrt8LHqBWsbTAoI5w==",
+          "requires": {
+            "argparse": "^2.0.1",
+            "entities": "~3.0.1",
+            "linkify-it": "^4.0.1",
+            "mdurl": "^1.0.1",
+            "uc.micro": "^1.0.5"
+          }
+        },
+        "mdurl": {
+          "version": "1.0.1",
+          "resolved": "https://registry.npmjs.org/mdurl/-/mdurl-1.0.1.tgz",
+          "integrity": "sha512-/sKlQJCBYVY9Ers9hqzKou4H6V5UWc/M59TH2dvkt+84itfnq7uFOMLpOiOS4ujvHP4etln18fmIxA5R5fll0g=="
+        },
+        "uc.micro": {
+          "version": "1.0.6",
+          "resolved": "https://registry.npmjs.org/uc.micro/-/uc.micro-1.0.6.tgz",
+          "integrity": "sha512-8Y75pvTYkLJW2hWQHXxoqRgV7qb9B+9vFEtidML+7koHUFapnVJAZ6cKs+Qjz5Aw3aZWHMC6u0wJE3At+nSGwA=="
+        }
+      }
+    },
     "tmpl": {
       "version": "1.0.5",
       "resolved": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz",
@@ -53836,6 +55177,11 @@
       "resolved": "https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-0.7.40.tgz",
       "integrity": "sha512-us1E3K+3jJppDBa3Tl0L3MOJiGhe1C6P0+nIvQAFYbxlMAx0h81eOwLmU57xgqToduDDPx3y5QsdjPfDu+FgOQ=="
     },
+    "uc.micro": {
+      "version": "2.1.0",
+      "resolved": "https://registry.npmjs.org/uc.micro/-/uc.micro-2.1.0.tgz",
+      "integrity": "sha512-ARDJmphmdvUk6Glw7y9DQ2bFkKBHwQHLi2lsaH6PPmz/Ka9sFOBsBluozhDltWmnv9u/cF6Rt87znRTPV+yp/A=="
+    },
     "uglify-js": {
       "version": "2.8.29",
       "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.8.29.tgz",
@@ -53926,19 +55272,24 @@
       "integrity": "sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w=="
     },
     "unified": {
-      "version": "11.0.5",
-      "resolved": "https://registry.npmjs.org/unified/-/unified-11.0.5.tgz",
-      "integrity": "sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==",
+      "version": "10.1.2",
+      "resolved": "https://registry.npmjs.org/unified/-/unified-10.1.2.tgz",
+      "integrity": "sha512-pUSWAi/RAnVy1Pif2kAoeWNBa3JVrx0MId2LASj8G+7AiHWoKZNTomq6LG326T68U7/e263X6fTdcXIy7XnF7Q==",
       "requires": {
-        "@types/unist": "^3.0.0",
+        "@types/unist": "^2.0.0",
         "bail": "^2.0.0",
-        "devlop": "^1.0.0",
         "extend": "^3.0.0",
+        "is-buffer": "^2.0.0",
         "is-plain-obj": "^4.0.0",
         "trough": "^2.0.0",
-        "vfile": "^6.0.0"
+        "vfile": "^5.0.0"
       },
       "dependencies": {
+        "is-buffer": {
+          "version": "2.0.5",
+          "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-2.0.5.tgz",
+          "integrity": "sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ=="
+        },
         "is-plain-obj": {
           "version": "4.1.0",
           "resolved": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-4.1.0.tgz",
@@ -53998,47 +55349,52 @@
         "crypto-random-string": "^1.0.0"
       }
     },
+    "unist-util-generated": {
+      "version": "2.0.1",
+      "resolved": "https://registry.npmjs.org/unist-util-generated/-/unist-util-generated-2.0.1.tgz",
+      "integrity": "sha512-qF72kLmPxAw0oN2fwpWIqbXAVyEqUzDHMsbtPvOudIlUzXYFIeQIuxXQCRCFh22B7cixvU0MG7m3MW8FTq/S+A=="
+    },
     "unist-util-is": {
-      "version": "6.0.0",
-      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-6.0.0.tgz",
-      "integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==",
+      "version": "5.2.1",
+      "resolved": "https://registry.npmjs.org/unist-util-is/-/unist-util-is-5.2.1.tgz",
+      "integrity": "sha512-u9njyyfEh43npf1M+yGKDGVPbY/JWEemg5nH05ncKPfi+kBbKBJoTdsogMu33uhytuLlv9y0O7GH7fEdwLdLQw==",
       "requires": {
-        "@types/unist": "^3.0.0"
+        "@types/unist": "^2.0.0"
       }
     },
     "unist-util-position": {
-      "version": "5.0.0",
-      "resolved": "https://registry.npmjs.org/unist-util-position/-/unist-util-position-5.0.0.tgz",
-      "integrity": "sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA==",
+      "version": "4.0.4",
+      "resolved": "https://registry.npmjs.org/unist-util-position/-/unist-util-position-4.0.4.tgz",
+      "integrity": "sha512-kUBE91efOWfIVBo8xzh/uZQ7p9ffYRtUbMRZBNFYwf0RK8koUMx6dGUfwylLOKmaT2cs4wSW96QoYUSXAyEtpg==",
       "requires": {
-        "@types/unist": "^3.0.0"
+        "@types/unist": "^2.0.0"
       }
     },
     "unist-util-stringify-position": {
-      "version": "4.0.0",
-      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz",
-      "integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==",
+      "version": "3.0.3",
+      "resolved": "https://registry.npmjs.org/unist-util-stringify-position/-/unist-util-stringify-position-3.0.3.tgz",
+      "integrity": "sha512-k5GzIBZ/QatR8N5X2y+drfpWG8IDBzdnVj6OInRNWm1oXrzydiaAT2OQiA8DPRRZyAKb9b6I2a6PxYklZD0gKg==",
       "requires": {
-        "@types/unist": "^3.0.0"
+        "@types/unist": "^2.0.0"
       }
     },
     "unist-util-visit": {
-      "version": "5.0.0",
-      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-5.0.0.tgz",
-      "integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==",
+      "version": "4.1.2",
+      "resolved": "https://registry.npmjs.org/unist-util-visit/-/unist-util-visit-4.1.2.tgz",
+      "integrity": "sha512-MSd8OUGISqHdVvfY9TPhyK2VdUrPgxkUtWSuMHF6XAAFuL4LokseigBnZtPnJMu+FbynTkFNnFlyjxpVKujMRg==",
       "requires": {
-        "@types/unist": "^3.0.0",
-        "unist-util-is": "^6.0.0",
-        "unist-util-visit-parents": "^6.0.0"
+        "@types/unist": "^2.0.0",
+        "unist-util-is": "^5.0.0",
+        "unist-util-visit-parents": "^5.1.1"
       }
     },
     "unist-util-visit-parents": {
-      "version": "6.0.1",
-      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz",
-      "integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==",
+      "version": "5.1.3",
+      "resolved": "https://registry.npmjs.org/unist-util-visit-parents/-/unist-util-visit-parents-5.1.3.tgz",
+      "integrity": "sha512-x6+y8g7wWMyQhL1iZfhIPhDAs7Xwbn9nRosDXl7qoPTSCy0yNxnKc+hWokFifWQIDGi154rdUqKvbCa4+1kLhg==",
       "requires": {
-        "@types/unist": "^3.0.0",
-        "unist-util-is": "^6.0.0"
+        "@types/unist": "^2.0.0",
+        "unist-util-is": "^5.0.0"
       }
     },
     "universal-cookie": {
@@ -54193,6 +55549,11 @@
       "resolved": "https://registry.npmjs.org/use-media-antd-query/-/use-media-antd-query-1.1.0.tgz",
       "integrity": "sha512-B6kKZwNV4R+l4Rl11sWO7HqOay9alzs1Vp1b4YJqjz33YxbltBCZtt/yxXxkXN9rc1S7OeEL/GbwC30Wmqhw6Q=="
     },
+    "use-sync-external-store": {
+      "version": "1.5.0",
+      "resolved": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz",
+      "integrity": "sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A=="
+    },
     "util": {
       "version": "0.11.1",
       "resolved": "https://registry.npmjs.org/util/-/util-0.11.1.tgz",
@@ -54242,6 +55603,29 @@
       "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz",
       "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="
     },
+    "uvu": {
+      "version": "0.5.6",
+      "resolved": "https://registry.npmjs.org/uvu/-/uvu-0.5.6.tgz",
+      "integrity": "sha512-+g8ENReyr8YsOc6fv/NVJs2vFdHBnBNdfE49rshrTzDWOlUx4Gq7KOS2GD8eqhy2j+Ejq29+SbKH8yjkAqXqoA==",
+      "requires": {
+        "dequal": "^2.0.0",
+        "diff": "^5.0.0",
+        "kleur": "^4.0.3",
+        "sade": "^1.7.3"
+      },
+      "dependencies": {
+        "diff": {
+          "version": "5.2.0",
+          "resolved": "https://registry.npmjs.org/diff/-/diff-5.2.0.tgz",
+          "integrity": "sha512-uIFDxqpRZGZ6ThOk84hEfqWoHx2devRFvpTZcTHur85vImfaxUbTW9Ryh4CpCuDnToOP1CEtXKIgytHBPVff5A=="
+        },
+        "kleur": {
+          "version": "4.1.5",
+          "resolved": "https://registry.npmjs.org/kleur/-/kleur-4.1.5.tgz",
+          "integrity": "sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ=="
+        }
+      }
+    },
     "v8-compile-cache": {
       "version": "2.4.0",
       "resolved": "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.4.0.tgz",
@@ -54299,21 +55683,30 @@
       "integrity": "sha512-/juG65kTL4Cy2su4P8HjtkTxk6VmJDiOPBufWniqQ6wknac6jNiXS9vU+hO3wgusiyqWlzTbVHi0dyJqRONg3w=="
     },
     "vfile": {
-      "version": "6.0.3",
-      "resolved": "https://registry.npmjs.org/vfile/-/vfile-6.0.3.tgz",
-      "integrity": "sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==",
+      "version": "5.3.7",
+      "resolved": "https://registry.npmjs.org/vfile/-/vfile-5.3.7.tgz",
+      "integrity": "sha512-r7qlzkgErKjobAmyNIkkSpizsFPYiUPuJb5pNW1RB4JcYVZhs4lIbVqk8XPk033CV/1z8ss5pkax8SuhGpcG8g==",
       "requires": {
-        "@types/unist": "^3.0.0",
-        "vfile-message": "^4.0.0"
+        "@types/unist": "^2.0.0",
+        "is-buffer": "^2.0.0",
+        "unist-util-stringify-position": "^3.0.0",
+        "vfile-message": "^3.0.0"
+      },
+      "dependencies": {
+        "is-buffer": {
+          "version": "2.0.5",
+          "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-2.0.5.tgz",
+          "integrity": "sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ=="
+        }
       }
     },
     "vfile-message": {
-      "version": "4.0.2",
-      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-4.0.2.tgz",
-      "integrity": "sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==",
+      "version": "3.1.4",
+      "resolved": "https://registry.npmjs.org/vfile-message/-/vfile-message-3.1.4.tgz",
+      "integrity": "sha512-fa0Z6P8HUrQN4BZaX05SIVXic+7kE3b05PWAtPuYP9QLHsLKYR7/AlLW3NtOrpXRLeawpDLMsVkmk5DG0NXgWw==",
       "requires": {
-        "@types/unist": "^3.0.0",
-        "unist-util-stringify-position": "^4.0.0"
+        "@types/unist": "^2.0.0",
+        "unist-util-stringify-position": "^3.0.0"
       }
     },
     "viewport-mercator-project": {
@@ -54348,6 +55741,11 @@
         "browser-process-hrtime": "^1.0.0"
       }
     },
+    "w3c-keyname": {
+      "version": "2.2.8",
+      "resolved": "https://registry.npmjs.org/w3c-keyname/-/w3c-keyname-2.2.8.tgz",
+      "integrity": "sha512-dpojBhNsCNN7T82Tm7k26A6G9ML3NkhDsnw9n/eoxSRlVBB4CEtIQ/KTCLI2Fwf3ataSXRhYFkQi3SlnFwPvPQ=="
+    },
     "w3c-xmlserializer": {
       "version": "2.0.0",
       "resolved": "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-2.0.0.tgz",
@@ -55678,11 +57076,6 @@
       "version": "0.1.0",
       "resolved": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz",
       "integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q=="
-    },
-    "zwitch": {
-      "version": "2.0.4",
-      "resolved": "https://registry.npmjs.org/zwitch/-/zwitch-2.0.4.tgz",
-      "integrity": "sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A=="
     }
   }
 }
Index: craco.config.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>const CracoLessPlugin = require('craco-less');\n\nmodule.exports = {\n  plugins: [\n    {\n      plugin: CracoLessPlugin,\n      options: {\n        lessLoaderOptions: {\n          lessOptions: {\n            modifyVars: {\n              '@primary-color': '#2979FF',\n              '@success-color': '#00C853',\n              '@text-color': '#081833',\n              '@disabled-color': '#515560',\n              '@danger-color': '#FF3D00',\n              '@font-size-base': '14px'\n            },\n            javascriptEnabled: true,\n          },\n        },\n      },\n    },\n  ],\n};
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/craco.config.js b/craco.config.js
--- a/craco.config.js	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/craco.config.js	(date 1751496906045)
@@ -21,4 +21,15 @@
       },
     },
   ],
+  webpack: {
+    configure: (webpackConfig, { env, paths }) => {
+      webpackConfig.module.rules.push({
+        test: /\.mjs$/,
+        include: /node_modules/,
+        type: 'javascript/auto',
+      });
+
+      return webpackConfig;
+    },
+  },
 };
\ No newline at end of file
Index: src/modules/_shared/ai/components/chatbox.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import React, { useEffect, useState, useRef } from 'react'\nimport { useDispatch, useSelector } from 'react-redux'\nimport {\n  Card,\n  Input,\n  Button,\n  Typography,\n  Space,\n  Progress,\n  Avatar,\n  Tooltip,\n  Dropdown,\n  Tag,\n  Spin,\n  Modal\n} from 'antd'\nimport {\n  RobotOutlined,\n  SendOutlined,\n  PaperClipOutlined,\n  MoreOutlined,\n  CloseOutlined,\n  UserOutlined,\n  ThunderboltOutlined,\n  FileTextOutlined,\n  PlusOutlined,\n  HistoryOutlined,\n  DeleteOutlined\n} from '@ant-design/icons'\nimport moment from 'moment'\nimport { AIAssistantState, AIMessage, DEFAULT_PROMPTS, AgentType } from '../types'\nimport {\n  sendMessageRequest,\n  fetchConversationsRequest,\n  createConversationRequest,\n  setCurrentConversation,\n  clearSuggestions,\n  deleteConversationRequest\n} from '../actions'\nimport AppState from '../../../../store/types'\nimport { extractProjectCode } from '../../../../helper/share'\nimport { MessageContent } from './message-content'\nimport './styles.less'\n\nconst { Text, Title } = Typography\nconst { TextArea } = Input\n\ninterface AIAssistantProps {\n  isVisible: boolean\n  onClose: () => void\n  hideHeader?: boolean\n}\n\nexport const AIChatBox: React.FC<AIAssistantProps> = ({ isVisible, onClose, hideHeader = false }) => {\n  const dispatch = useDispatch()\n  const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState\n  const [messageInput, setMessageInput] = useState('')\n  const [selectedPrompt, setSelectedPrompt] = useState<string>('')\n  const [showConversationModal, setShowConversationModal] = useState(false)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n\n  // Load AI settings from localStorage\n  const getAISettings = () => {\n    try {\n      const saved = localStorage.getItem('ai-assistant-settings')\n      return saved ? JSON.parse(saved) : {\n        agentType: 'master_agent' as AgentType,\n        model: 'gpt-4.1' as const,\n        stream: true\n      }\n    } catch {\n      return {\n        agentType: 'master_agent' as AgentType,\n        model: 'gpt-4.1' as const,\n        stream: true\n      }\n    }\n  }\n\n  useEffect(() => {\n    if (isVisible) {\n      dispatch(fetchConversationsRequest())\n    }\n  }, [isVisible, dispatch])\n\n  useEffect(() => {\n    // Create default conversation if none exists and we're visible\n    if (isVisible && aiState.conversations.length === 0 && !aiState.isLoading) {\n      dispatch(createConversationRequest({\n        title: 'New Chat',\n        projectId: extractProjectCode() || 'default-project'\n      }))\n    }\n  }, [isVisible, aiState.conversations.length, aiState.isLoading, dispatch])\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [aiState.currentConversation?.messages])\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  const handleSendMessage = () => {\n    if (messageInput.trim() && aiState.currentConversation?.id) {\n      const content = selectedPrompt\n        ? `${selectedPrompt}\\n\\n${messageInput.trim()}`\n        : messageInput.trim()\n\n      const settings = getAISettings()\n\n      dispatch(sendMessageRequest({\n        content,\n        conversationId: aiState.currentConversation.id,\n        agentType: settings.agentType,\n        model: settings.model,\n        projectId: extractProjectCode() || 'default-project',\n        stream: settings.stream\n      }))\n      setMessageInput('')\n      setSelectedPrompt('')\n      dispatch(clearSuggestions())\n    }\n  }\n\n  const handlePromptSelect = (prompt: string) => {\n    setSelectedPrompt(prompt)\n    setMessageInput('')\n  }\n\n  const handleNewConversation = () => {\n    dispatch(createConversationRequest({\n      title: 'New Chat',\n      projectId: extractProjectCode() || 'default-project'\n    }))\n  }\n\n  const handleSelectConversation = (conversationId: string) => {\n    dispatch(setCurrentConversation(conversationId))\n    setShowConversationModal(false)\n  }\n\n  const handleDeleteConversation = (conversationId: string) => {\n    dispatch(deleteConversationRequest(conversationId))\n  }\n\n  const formatMessageTime = (timestamp: Date) => {\n    const now = moment()\n    const messageTime = moment(timestamp)\n\n    if (now.diff(messageTime, 'minutes') < 1) {\n      return 'now'\n    } else if (now.diff(messageTime, 'hours') < 1) {\n      return `${now.diff(messageTime, 'minutes')}m ago`\n    } else if (now.diff(messageTime, 'days') === 0) {\n      return messageTime.format('HH:mm')\n    } else {\n      return messageTime.format('DD/MM HH:mm')\n    }\n  }\n\n  const renderMessage = (message: AIMessage) => {\n    const isUser = message.type === 'user'\n\n    return (\n      <div\n        key={message.id}\n        className={`ai-message ${isUser ? 'user-message' : 'assistant-message'}`}\n      >\n        <div className=\"message-header\">\n          <Avatar\n            size=\"small\"\n            icon={isUser ? <UserOutlined /> : <RobotOutlined />}\n            className={isUser ? 'user-avatar' : 'ai-avatar'}\n          />\n          <div className=\"message-meta\">\n            {!isUser && message.agentName && (\n              <Text strong className=\"agent-name\">\n                {message.agentName}\n              </Text>\n            )}\n            {!isUser && message.teamName && message.teamName !== message.agentName && (\n              <Text type=\"secondary\" className=\"team-name\">\n                ({message.teamName})\n              </Text>\n            )}\n            <Text type=\"secondary\" className=\"message-time\">\n              {formatMessageTime(message.timestamp)}\n            </Text>\n          </div>\n          {message.tokens && (\n            <Tag color=\"blue\">\n              {message.tokens} tokens\n            </Tag>\n          )}\n        </div>\n        <div className=\"message-content\">\n          <MessageContent content={message.content} />\n        </div>\n      </div>\n    )\n  }\n\n  const tokenUsagePercentage = (aiState.totalTokensUsed / aiState.availableTokens) * 100\n\n  if (!isVisible) return null\n\n  return (\n    <div className=\"ai-assistant-container\">\n      <Card className=\"ai-assistant-card\" bodyStyle={{ padding: 0 }}>\n        {/* Header - only show when not in tabbed interface */}\n        {!hideHeader && (\n          <div className=\"ai-assistant-header\">\n            <div className=\"header-left\">\n              <RobotOutlined className=\"ai-icon\" />\n              <Title level={5} style={{ margin: 0, color: 'white' }}>\n                BA Vista\n              </Title>\n            </div>\n            <div className=\"header-right\">\n              <Tooltip title=\"Settings\">\n                <Button\n                  type=\"text\"\n                  icon={<MoreOutlined />}\n                  style={{ color: 'white' }}\n                />\n              </Tooltip>\n              <Tooltip title=\"Close\">\n                <Button\n                  type=\"text\"\n                  icon={<CloseOutlined />}\n                  onClick={onClose}\n                  style={{ color: 'white' }}\n                />\n              </Tooltip>\n            </div>\n          </div>\n        )}\n\n        {/* Current Task */}\n        <div className=\"current-task\">\n          <Title level={5}>{aiState.currentConversation?.title ?? \"New Chat\"}</Title>\n          <div className=\"task-actions\">\n            <Tooltip title=\"New Conversation\" placement=\"topRight\">\n              <Button\n                type=\"text\"\n                icon={<PlusOutlined />}\n                size=\"small\"\n                onClick={handleNewConversation}\n              />\n            </Tooltip>\n            <Tooltip title=\"Conversation History\" placement=\"topRight\">\n              <Button\n                type=\"text\"\n                icon={<HistoryOutlined />}\n                size=\"small\"\n                onClick={() => setShowConversationModal(true)}\n              />\n            </Tooltip>\n          </div>\n        </div>\n\n\n\n        {/* Token Usage */}\n        <div className=\"token-usage\">\n          <div className=\"token-info\">\n            <Text strong>Token:</Text>\n            <Space>\n              <ThunderboltOutlined style={{ color: '#52c41a' }} />\n              <Text>{aiState.totalTokensUsed}</Text>\n              <ThunderboltOutlined style={{ color: '#ff4d4f' }} />\n              <Text>{aiState.availableTokens - aiState.totalTokensUsed}</Text>\n            </Space>\n            <Text strong>{aiState.availableTokens - aiState.totalTokensUsed} USD</Text>\n          </div>\n          <Progress\n            percent={tokenUsagePercentage}\n            size=\"small\"\n            strokeColor={tokenUsagePercentage > 80 ? '#ff4d4f' : '#1890ff'}\n            showInfo={false}\n          />\n        </div>\n\n\n\n        {/* Messages */}\n        <div className=\"ai-messages\">\n          {aiState.currentConversation?.messages.length === 0 && !aiState.isTyping ? (\n            <div className=\"empty-conversation\">\n              <div className=\"empty-conversation-content\">\n                <RobotOutlined className=\"empty-icon\" />\n                <Title level={4}>Welcome to BA Vista AI Assistant</Title>\n                <Text type=\"secondary\">\n                  I'm here to help you analyze requirements, generate documentation, and improve your business analysis workflow.\n                </Text>\n                <div className=\"suggested-prompts\">\n                  <Text strong>Try asking me to:</Text>\n                  <ul>\n                    <li>Analyze this requirement for completeness</li>\n                    <li>Generate use case scenarios</li>\n                    <li>Review this document for clarity</li>\n                    <li>Create test cases for this feature</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          ) : (\n            <>\n              {aiState.currentConversation?.messages.map(renderMessage)}\n              {aiState.isTyping && (\n                <div className=\"ai-message assistant-message\">\n                  <div className=\"message-header\">\n                    <Avatar size=\"small\" icon={<RobotOutlined />} className=\"ai-avatar\" />\n                    <Text type=\"secondary\">AI is thinking...</Text>\n                  </div>\n                  <div className=\"message-content\">\n                    <Spin size=\"small\" />\n                  </div>\n                </div>\n              )}\n            </>\n          )}\n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Quick Actions */}\n        {aiState.suggestions.length > 0 && (\n          <div className=\"quick-actions\">\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>Suggestions:</Text>\n            <Space wrap>\n              {aiState.suggestions.map((suggestion, index) => (\n                <Button\n                  key={index}\n                  size=\"small\"\n                  type=\"dashed\"\n                  onClick={() => setMessageInput(suggestion)}\n                >\n                  {suggestion}\n                </Button>\n              ))}\n            </Space>\n          </div>\n        )}\n\n        {/* Input Area */}\n        <div className=\"ai-input-area\">\n          {selectedPrompt && (\n            <div className=\"selected-prompt\">\n              <Tag color=\"blue\" closable onClose={() => setSelectedPrompt('')}>\n                <FileTextOutlined /> {DEFAULT_PROMPTS.find(p => p.prompt === selectedPrompt)?.title || 'Custom Prompt'}\n              </Tag>\n            </div>\n          )}\n\n          <div className=\"input-container\">\n            <TextArea\n              value={messageInput}\n              onChange={(e) => setMessageInput(e.target.value)}\n              placeholder={selectedPrompt ? \"Add your specific requirements...\" : \"Input a prompt, or type @ to call reference doc/ artefacts\"}\n              autoSize={{ minRows: 1, maxRows: 4 }}\n              onPressEnter={(e) => {\n                if (!e.shiftKey) {\n                  e.preventDefault()\n                  handleSendMessage()\n                }\n              }}\n              className=\"message-input\"\n            />\n            <div className=\"input-actions\">\n              <Dropdown\n                menu={{\n                  items: DEFAULT_PROMPTS.map(prompt => ({\n                    key: prompt.prompt,\n                    label: (\n                      <div>\n                        <Text strong>{prompt.title}</Text>\n                        <br />\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          {prompt.description} ({prompt.tokens} tokens)\n                        </Text>\n                      </div>\n                    )\n                  })),\n                  onClick: ({ key }) => handlePromptSelect(key as string)\n                }}\n                trigger={['click']}\n              >\n                <Button\n                  type=\"text\"\n                  icon={<FileTextOutlined />}\n                  size=\"small\"\n                />\n              </Dropdown>\n              <Button\n                type=\"text\"\n                icon={<PaperClipOutlined />}\n                size=\"small\"\n              />\n              <Button\n                type=\"primary\"\n                icon={<SendOutlined />}\n                onClick={handleSendMessage}\n                disabled={!messageInput.trim() && !selectedPrompt}\n                size=\"small\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Conversation History Modal */}\n        <Modal\n          title=\"Conversation History\"\n          open={showConversationModal}\n          onCancel={() => setShowConversationModal(false)}\n          footer={null}\n          width={600}\n          className=\"conversation-history-modal\"\n        >\n          <div className=\"conversation-modal-content\">\n            {aiState.conversations.length === 0 ? (\n              <div className=\"empty-conversations\">\n                <Text type=\"secondary\">No conversations found</Text>\n              </div>\n            ) : (\n              <div className=\"conversation-items\">\n                {aiState.conversations.map((conversation) => (\n                  <div\n                    key={conversation.id}\n                    className={`conversation-item ${conversation.id === aiState.currentConversation?.id ? 'active' : ''}`}\n                    onClick={() => handleSelectConversation(conversation.id)}\n                  >\n                    <div className=\"conversation-info\">\n                      <Text className=\"conversation-title\">{conversation.title}</Text>\n                      <Text type=\"secondary\" className=\"conversation-time\">\n                        {moment(conversation.updatedAt).format('DD/MM HH:mm')}\n                      </Text>\n                    </div>\n                    <Button\n                      type=\"text\"\n                      icon={<DeleteOutlined />}\n                      size=\"small\"\n                      onClick={(e) => {\n                        e.stopPropagation()\n                        handleDeleteConversation(conversation.id)\n                      }}\n                      className=\"delete-conversation-btn\"\n                      danger\n                    />\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </Modal>\n      </Card>\n    </div>\n  )\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/components/chatbox.tsx b/src/modules/_shared/ai/components/chatbox.tsx
--- a/src/modules/_shared/ai/components/chatbox.tsx	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/src/modules/_shared/ai/components/chatbox.tsx	(date 1751496906059)
@@ -9,7 +9,6 @@
   Progress,
   Avatar,
   Tooltip,
-  Dropdown,
   Tag,
   Spin,
   Modal
@@ -21,11 +20,13 @@
   MoreOutlined,
   CloseOutlined,
   UserOutlined,
-  ThunderboltOutlined,
   FileTextOutlined,
   PlusOutlined,
   HistoryOutlined,
-  DeleteOutlined
+  DeleteOutlined,
+  ArrowUpOutlined,
+  ArrowDownOutlined,
+  CopyOutlined
 } from '@ant-design/icons'
 import moment from 'moment'
 import { AIAssistantState, AIMessage, DEFAULT_PROMPTS, AgentType } from '../types'
@@ -41,6 +42,10 @@
 import { extractProjectCode } from '../../../../helper/share'
 import { MessageContent } from './message-content'
 import './styles.less'
+import intl from '../../../../config/locale.config'
+import CustomSvgIcons from '../../../../helper/component/custom-icons'
+import { CanvasDialog } from './canvas-dialog'
+
 
 const { Text, Title } = Typography
 const { TextArea } = Input
@@ -55,9 +60,9 @@
   const dispatch = useDispatch()
   const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState
   const [messageInput, setMessageInput] = useState('')
-  const [selectedPrompt, setSelectedPrompt] = useState<string>('')
   const [showConversationModal, setShowConversationModal] = useState(false)
   const messagesEndRef = useRef<HTMLDivElement>(null)
+  const canvasRef = useRef<{ open: (initialContent: string) => void }>(null)
 
   // Load AI settings from localStorage
   const getAISettings = () => {
@@ -103,9 +108,7 @@
 
   const handleSendMessage = () => {
     if (messageInput.trim() && aiState.currentConversation?.id) {
-      const content = selectedPrompt
-        ? `${selectedPrompt}\n\n${messageInput.trim()}`
-        : messageInput.trim()
+      const content = messageInput.trim()
 
       const settings = getAISettings()
 
@@ -118,16 +121,10 @@
         stream: settings.stream
       }))
       setMessageInput('')
-      setSelectedPrompt('')
       dispatch(clearSuggestions())
     }
   }
 
-  const handlePromptSelect = (prompt: string) => {
-    setSelectedPrompt(prompt)
-    setMessageInput('')
-  }
-
   const handleNewConversation = () => {
     dispatch(createConversationRequest({
       title: 'New Chat',
@@ -159,8 +156,11 @@
     }
   }
 
-  const renderMessage = (message: AIMessage) => {
+  const isLoading = aiState.isTyping || aiState.currentConversation?.messages.some(m => m.isLoading)
+
+  const renderMessage = (message: AIMessage, openCanvas: any) => {
     const isUser = message.type === 'user'
+    if (!message.content) return
 
     return (
       <div
@@ -174,16 +174,19 @@
             className={isUser ? 'user-avatar' : 'ai-avatar'}
           />
           <div className="message-meta">
-            {!isUser && message.agentName && (
-              <Text strong className="agent-name">
-                {message.agentName}
-              </Text>
-            )}
-            {!isUser && message.teamName && message.teamName !== message.agentName && (
-              <Text type="secondary" className="team-name">
-                ({message.teamName})
-              </Text>
+            {!isUser && (
+              <>
+                <Text strong className="agent-name">
+                  {intl.formatMessage({ id: 'ai.agent.name' })}
+                </Text>
+                {message.teamName && (
+                  <Text type="secondary" className="team-name">
+                    ({message.teamName})
+                  </Text>
+                )}
+              </>
             )}
+
             <Text type="secondary" className="message-time">
               {formatMessageTime(message.timestamp)}
             </Text>
@@ -195,48 +198,34 @@
           )}
         </div>
         <div className="message-content">
-          <MessageContent content={message.content} />
+          <MessageContent content={message.content} isLoading={message.isLoading} />
         </div>
-      </div>
-    )
-  }
-
-  const tokenUsagePercentage = (aiState.totalTokensUsed / aiState.availableTokens) * 100
-
-  if (!isVisible) return null
-
-  return (
-    <div className="ai-assistant-container">
-      <Card className="ai-assistant-card" bodyStyle={{ padding: 0 }}>
-        {/* Header - only show when not in tabbed interface */}
-        {!hideHeader && (
-          <div className="ai-assistant-header">
-            <div className="header-left">
-              <RobotOutlined className="ai-icon" />
-              <Title level={5} style={{ margin: 0, color: 'white' }}>
-                BA Vista
-              </Title>
-            </div>
-            <div className="header-right">
-              <Tooltip title="Settings">
-                <Button
-                  type="text"
-                  icon={<MoreOutlined />}
-                  style={{ color: 'white' }}
-                />
-              </Tooltip>
-              <Tooltip title="Close">
-                <Button
-                  type="text"
-                  icon={<CloseOutlined />}
-                  onClick={onClose}
-                  style={{ color: 'white' }}
-                />
-              </Tooltip>
-            </div>
-          </div>
-        )}
+        <div className="message-tools">
+          <Tooltip title="Copy" placement='bottom'>
+            <Button type='text' icon={<CopyOutlined />} />
+          </Tooltip>
+          {!isUser && (
+            // Open canvas for AI messages
+            <Tooltip title="Edit in Canvas" placement='bottom'>
+              <Button
+                type="text"
+                icon={<CustomSvgIcons name="CanvasButtonIcon" fontSize={12} />}
+                onClick={openCanvas}
+                disabled={message.isLoading}
+              />
+            </Tooltip>
+          )}
+        </div>
+      </div>
+    )
+  }
 
+  if (!isVisible) return null
+
+  return (
+    <div className="ai-assistant-container">
+      <CanvasDialog ref={canvasRef} />
+      <Card className="ai-assistant-card" bodyStyle={{ padding: 0 }}>
         {/* Current Task */}
         <div className="current-task">
           <Title level={5}>{aiState.currentConversation?.title ?? "New Chat"}</Title>
@@ -267,19 +256,17 @@
           <div className="token-info">
             <Text strong>Token:</Text>
             <Space>
-              <ThunderboltOutlined style={{ color: '#52c41a' }} />
-              <Text>{aiState.totalTokensUsed}</Text>
-              <ThunderboltOutlined style={{ color: '#ff4d4f' }} />
-              <Text>{aiState.availableTokens - aiState.totalTokensUsed}</Text>
+              <Tooltip title={intl.formatMessage({ id: 'ai.send-token.description' })}>
+                <ArrowUpOutlined />
+                <Text>{aiState.currentConversation?.sentToken}</Text>
+              </Tooltip>
+              <Tooltip title={intl.formatMessage({ id: 'ai.receive-token.description' })}>
+                <ArrowDownOutlined />
+                <Text>{aiState.currentConversation?.receiveToken}</Text>
+              </Tooltip>
             </Space>
-            <Text strong>{aiState.availableTokens - aiState.totalTokensUsed} USD</Text>
+            <Text strong>{aiState.currentConversation?.totalCost} USD</Text>
           </div>
-          <Progress
-            percent={tokenUsagePercentage}
-            size="small"
-            strokeColor={tokenUsagePercentage > 80 ? '#ff4d4f' : '#1890ff'}
-            showInfo={false}
-          />
         </div>
 
 
@@ -290,32 +277,20 @@
             <div className="empty-conversation">
               <div className="empty-conversation-content">
                 <RobotOutlined className="empty-icon" />
-                <Title level={4}>Welcome to BA Vista AI Assistant</Title>
+                <Title level={4}>{intl.formatMessage({ id: 'ai.welcome-message.title' })}</Title>
                 <Text type="secondary">
-                  I'm here to help you analyze requirements, generate documentation, and improve your business analysis workflow.
+                  {intl.formatMessage({ id: 'ai.welcome-message.description' })}
                 </Text>
-                <div className="suggested-prompts">
-                  <Text strong>Try asking me to:</Text>
-                  <ul>
-                    <li>Analyze this requirement for completeness</li>
-                    <li>Generate use case scenarios</li>
-                    <li>Review this document for clarity</li>
-                    <li>Create test cases for this feature</li>
-                  </ul>
-                </div>
               </div>
             </div>
           ) : (
             <>
-              {aiState.currentConversation?.messages.map(renderMessage)}
+              {aiState.currentConversation?.messages.map((message) => renderMessage(message, () => canvasRef.current?.open(message.content)))}
               {aiState.isTyping && (
                 <div className="ai-message assistant-message">
                   <div className="message-header">
                     <Avatar size="small" icon={<RobotOutlined />} className="ai-avatar" />
-                    <Text type="secondary">AI is thinking...</Text>
-                  </div>
-                  <div className="message-content">
-                    <Spin size="small" />
+                    <Text type="secondary">AI is thinking...</Text> <Spin size="small" />
                   </div>
                 </div>
               )}
@@ -343,21 +318,13 @@
           </div>
         )}
 
-        {/* Input Area */}
         <div className="ai-input-area">
-          {selectedPrompt && (
-            <div className="selected-prompt">
-              <Tag color="blue" closable onClose={() => setSelectedPrompt('')}>
-                <FileTextOutlined /> {DEFAULT_PROMPTS.find(p => p.prompt === selectedPrompt)?.title || 'Custom Prompt'}
-              </Tag>
-            </div>
-          )}
 
           <div className="input-container">
             <TextArea
               value={messageInput}
               onChange={(e) => setMessageInput(e.target.value)}
-              placeholder={selectedPrompt ? "Add your specific requirements..." : "Input a prompt, or type @ to call reference doc/ artefacts"}
+              placeholder={"Input a prompt, or type @ to call reference doc/ artefacts"}
               autoSize={{ minRows: 1, maxRows: 4 }}
               onPressEnter={(e) => {
                 if (!e.shiftKey) {
@@ -368,30 +335,6 @@
               className="message-input"
             />
             <div className="input-actions">
-              <Dropdown
-                menu={{
-                  items: DEFAULT_PROMPTS.map(prompt => ({
-                    key: prompt.prompt,
-                    label: (
-                      <div>
-                        <Text strong>{prompt.title}</Text>
-                        <br />
-                        <Text type="secondary" style={{ fontSize: '12px' }}>
-                          {prompt.description} ({prompt.tokens} tokens)
-                        </Text>
-                      </div>
-                    )
-                  })),
-                  onClick: ({ key }) => handlePromptSelect(key as string)
-                }}
-                trigger={['click']}
-              >
-                <Button
-                  type="text"
-                  icon={<FileTextOutlined />}
-                  size="small"
-                />
-              </Dropdown>
               <Button
                 type="text"
                 icon={<PaperClipOutlined />}
@@ -401,7 +344,7 @@
                 type="primary"
                 icon={<SendOutlined />}
                 onClick={handleSendMessage}
-                disabled={!messageInput.trim() && !selectedPrompt}
+                disabled={!messageInput.trim() || isLoading}
                 size="small"
               />
             </div>
Index: package.json
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>{\n  \"name\": \"reqtool-fe\",\n  \"version\": \"0.1.0\",\n  \"private\": true,\n  \"dependencies\": {\n    \"@ant-design/charts\": \"^1.3.5\",\n    \"@ant-design/icons\": \"^4.7.0\",\n    \"@ant-design/plots\": \"^2.5.0\",\n    \"@ant-design/pro-table\": \"^2.56.7\",\n    \"@azure/msal-browser\": \"^2.18.0\",\n    \"@azure/msal-react\": \"^1.1.0\",\n    \"@ckeditor/ckeditor5-react\": \"^3.0.3\",\n    \"@craco/craco\": \"^6.4.2\",\n    \"@redux-saga/core\": \"^1.3.0\",\n    \"@reduxjs/toolkit\": \"^1.6.2\",\n    \"@svgr/webpack\": \"^5.5.0\",\n    \"@testing-library/jest-dom\": \"^5.14.1\",\n    \"@testing-library/react\": \"^11.2.7\",\n    \"@testing-library/user-event\": \"^12.8.3\",\n    \"@tiptap/extension-character-count\": \"^2.23.0\",\n    \"@tiptap/extension-code-block-lowlight\": \"^2.23.0\",\n    \"@tiptap/extension-color\": \"^2.23.0\",\n    \"@tiptap/extension-highlight\": \"^2.23.0\",\n    \"@tiptap/extension-horizontal-rule\": \"^2.23.0\",\n    \"@tiptap/extension-image\": \"^2.23.0\",\n    \"@tiptap/extension-link\": \"^2.23.0\",\n    \"@tiptap/extension-mention\": \"^2.23.0\",\n    \"@tiptap/extension-placeholder\": \"^2.23.0\",\n    \"@tiptap/extension-strike\": \"^2.23.0\",\n    \"@tiptap/extension-subscript\": \"^2.23.0\",\n    \"@tiptap/extension-superscript\": \"^2.23.0\",\n    \"@tiptap/extension-table\": \"^2.23.0\",\n    \"@tiptap/extension-table-cell\": \"^2.23.0\",\n    \"@tiptap/extension-table-header\": \"^2.23.0\",\n    \"@tiptap/extension-table-of-contents\": \"^2.23.0\",\n    \"@tiptap/extension-table-row\": \"^2.23.0\",\n    \"@tiptap/extension-task-item\": \"^2.23.0\",\n    \"@tiptap/extension-task-list\": \"^2.23.0\",\n    \"@tiptap/extension-text-align\": \"^2.23.0\",\n    \"@tiptap/extension-typography\": \"^2.23.0\",\n    \"@tiptap/extension-underline\": \"^2.23.0\",\n    \"@tiptap/react\": \"^2.23.0\",\n    \"@tiptap/starter-kit\": \"^2.23.0\",\n    \"@types/jest\": \"^26.0.24\",\n    \"@types/node\": \"^12.20.33\",\n    \"@types/react\": \"^17.0.30\",\n    \"@types/react-dom\": \"^17.0.9\",\n    \"antd\": \"^4.16.13\",\n    \"axios\": \"^0.23.0\",\n    \"ckeditor5-custom-build\": \"./lib/ckeditor5\",\n    \"compare-versions\": \"^5.0.1\",\n    \"craco-less\": \"^1.20.0\",\n    \"env-cmd\": \"^10.1.0\",\n    \"file\": \"^0.2.2\",\n    \"file-saver\": \"^2.0.5\",\n    \"lodash.debounce\": \"^4.0.8\",\n    \"moment\": \"^2.30.1\",\n    \"react\": \"^17.0.2\",\n    \"react-cookie\": \"^4.1.1\",\n    \"react-custom-scrollbars\": \"^4.2.1\",\n    \"react-dom\": \"^17.0.2\",\n    \"react-drag-listview\": \"^0.1.8\",\n    \"react-draggable\": \"^4.4.5\",\n    \"react-highlight-words\": \"^0.17.0\",\n    \"react-hotkeys\": \"^2.0.0\",\n    \"react-intl\": \"^5.21.0\",\n    \"react-markdown\": \"^10.1.0\",\n    \"react-redux\": \"^7.2.5\",\n    \"react-router\": \"^5.3.4\",\n    \"react-router-dom\": \"^5.3.0\",\n    \"react-scripts\": \"4.0.3\",\n    \"react-select\": \"^5.3.2\",\n    \"redux\": \"^4.2.1\",\n    \"redux-injectors\": \"^1.3.0\",\n    \"redux-saga\": \"^1.1.3\",\n    \"typescript\": \"^4.4.4\",\n    \"web-vitals\": \"^1.1.2\",\n    \"yarn\": \"^1.22.18\"\n  },\n  \"scripts\": {\n    \"start\": \"env-cmd -f .env craco start\",\n    \"start-dev\": \"set PORT=3006 && env-cmd -f .env.development craco start\",\n    \"build\": \"env-cmd -f .env craco build\",\n    \"build-dev\": \"env-cmd -f .env.development craco build\",\n    \"build-ba\": \"env-cmd -f .env.ba craco build\",\n    \"build-prod\": \"env-cmd -f .env.prod craco build\",\n    \"test\": \"env-cmd -f .env craco test\",\n    \"eject\": \"env-cmd -f .env react-scripts eject\",\n    \"format:src\": \"yarn prettier --write src/\"\n  },\n  \"eslintConfig\": {\n    \"extends\": [\n      \"react-app\",\n      \"react-app/jest\"\n    ]\n  },\n  \"browserslist\": {\n    \"production\": [\n      \">0.2%\",\n      \"not dead\",\n      \"not op_mini all\"\n    ],\n    \"development\": [\n      \"last 1 chrome version\",\n      \"last 1 firefox version\",\n      \"last 1 safari version\"\n    ]\n  },\n  \"devDependencies\": {\n    \"@types/react-router-dom\": \"^5.3.1\",\n    \"prettier\": \"2.5.1\"\n  }\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/package.json b/package.json
--- a/package.json	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/package.json	(date 1751496906055)
@@ -11,6 +11,7 @@
     "@azure/msal-react": "^1.1.0",
     "@ckeditor/ckeditor5-react": "^3.0.3",
     "@craco/craco": "^6.4.2",
+    "@microsoft/fetch-event-source": "^2.0.1",
     "@redux-saga/core": "^1.3.0",
     "@reduxjs/toolkit": "^1.6.2",
     "@svgr/webpack": "^5.5.0",
@@ -64,7 +65,7 @@
     "react-highlight-words": "^0.17.0",
     "react-hotkeys": "^2.0.0",
     "react-intl": "^5.21.0",
-    "react-markdown": "^10.1.0",
+    "react-markdown": "^8.0.6",
     "react-redux": "^7.2.5",
     "react-router": "^5.3.4",
     "react-router-dom": "^5.3.0",
@@ -73,6 +74,7 @@
     "redux": "^4.2.1",
     "redux-injectors": "^1.3.0",
     "redux-saga": "^1.1.3",
+    "tiptap-markdown": "^0.8.1",
     "typescript": "^4.4.4",
     "web-vitals": "^1.1.2",
     "yarn": "^1.22.18"
Index: src/modules/_shared/ai/components/ai-settings.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import React, { useState } from 'react'\nimport { useDispatch, useSelector } from 'react-redux'\nimport {\n  Card,\n  Select,\n  Form,\n  Typography,\n  Space,\n  Divider,\n  Tag,\n  Tooltip,\n  Switch,\n  InputNumber,\n  Button\n} from 'antd'\nimport {\n  RobotOutlined,\n  SettingOutlined,\n  ThunderboltOutlined,\n  SaveOutlined\n} from '@ant-design/icons'\nimport { AgentType, AvaiModel, AIAssistantState } from '../types'\nimport AppState from '../../../../store/types'\nimport './ai-settings.less'\n\nconst { Title, Text } = Typography\nconst { Option } = Select\n\ninterface AISettingsProps {\n  onSettingsChange?: (settings: AISettings) => void\n}\n\nexport interface AISettings {\n  agentType: AgentType\n  model: AvaiModel\n  stream: boolean\n  maxTokens: number\n  temperature: number\n}\n\nconst AGENT_TYPES: { value: AgentType; label: string; description: string; color: string }[] = [\n  {\n    value: 'master_agent',\n    label: 'Master Agent',\n    description: 'Strategic analysis and high-level decision making',\n    color: 'blue'\n  },\n  {\n    value: 'ur_agent',\n    label: 'User Requirement Agent',\n    description: 'Specialized in user requirements analysis',\n    color: 'green'\n  },\n  {\n    value: 'hlr_agent',\n    label: 'High-Level Requirement Agent',\n    description: 'Focus on high-level requirement specification',\n    color: 'purple'\n  }\n]\n\nconst AI_MODELS: { value: AvaiModel; label: string; description: string; performance: string }[] = [\n  {\n    value: 'gpt-4.1-mini',\n    label: 'GPT-4.1 Mini',\n    description: 'Fast and efficient for simple tasks',\n    performance: 'Fast'\n  },\n  {\n    value: 'gpt-4.1',\n    label: 'GPT-4.1',\n    description: 'Balanced performance and capability',\n    performance: 'Balanced'\n  },\n  {\n    value: 'gpt-o4',\n    label: 'GPT-O4',\n    description: 'Most advanced model for complex analysis',\n    performance: 'Advanced'\n  }\n]\n\nexport const AISettings: React.FC<AISettingsProps> = ({ onSettingsChange }) => {\n  const dispatch = useDispatch()\n  const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState\n\n  const [settings, setSettings] = useState<AISettings>({\n    agentType: 'leader_agent',\n    model: 'gpt-4.1',\n    stream: true,\n    maxTokens: 2000,\n    temperature: 0.7\n  })\n\n  const handleSettingChange = (key: keyof AISettings, value: any) => {\n    const newSettings = { ...settings, [key]: value }\n    setSettings(newSettings)\n    onSettingsChange?.(newSettings)\n  }\n\n  const handleSaveSettings = () => {\n    // Save settings to localStorage or dispatch to Redux\n    localStorage.setItem('ai-assistant-settings', JSON.stringify(settings))\n    // You could also dispatch an action to save to Redux store\n  }\n\n  const renderAgentTypeOption = (agent: typeof AGENT_TYPES[0]) => (\n    <Option key={agent.value} value={agent.value}>\n      <div className=\"agent-option\">\n        <div className=\"agent-header\">\n          <Tag color={agent.color}>{agent.label}</Tag>\n        </div>\n        <Text type=\"secondary\" className=\"agent-description\">\n          {agent.description}\n        </Text>\n      </div>\n    </Option>\n  )\n\n  const renderModelOption = (model: typeof AI_MODELS[0]) => (\n    <Option key={model.value} value={model.value}>\n      <div className=\"model-option\">\n        <div className=\"model-header\">\n          <span className=\"model-name\">{model.label}</span>\n          <Tag color={model.performance === 'Fast' ? 'green' : model.performance === 'Balanced' ? 'blue' : 'gold'}>\n            {model.performance}\n          </Tag>\n        </div>\n        <Text type=\"secondary\" className=\"model-description\">\n          {model.description}\n        </Text>\n      </div>\n    </Option>\n  )\n\n  return (\n    <div className=\"ai-settings\">\n      <div className=\"settings-header\">\n        <Title level={4}>\n          <SettingOutlined /> AI Assistant Settings\n        </Title>\n      </div>\n\n      <Form layout=\"vertical\" className=\"settings-form\">\n        <Form.Item label=\"Agent Type\" className=\"agent-selection\">\n          <Select\n            value={settings.agentType}\n            onChange={(value) => handleSettingChange('agentType', value)}\n            placeholder=\"Select an agent type\"\n            optionLabelProp=\"label\"\n          >\n            {AGENT_TYPES.map(renderAgentTypeOption)}\n          </Select>\n          <Text type=\"secondary\" className=\"help-text\">\n            Choose the type of AI agent based on your analysis needs\n          </Text>\n        </Form.Item>\n\n        <Form.Item label=\"AI Model\" className=\"model-selection\">\n          <Select\n            value={settings.model}\n            onChange={(value) => handleSettingChange('model', value)}\n            placeholder=\"Select an AI model\"\n            optionLabelProp=\"label\"\n          >\n            {AI_MODELS.map(renderModelOption)}\n          </Select>\n          <Text type=\"secondary\" className=\"help-text\">\n            Select the AI model that best fits your task complexity\n          </Text>\n        </Form.Item>\n\n        <Divider />\n\n        <Form.Item label=\"Advanced Settings\">\n          <Space direction=\"vertical\" style={{ width: '100%' }}>\n            <div className=\"setting-row\">\n              <div className=\"setting-label\">\n                <Text>Streaming Response</Text>\n                <Tooltip title=\"Enable real-time streaming of AI responses\">\n                  <ThunderboltOutlined style={{ marginLeft: 4, color: '#1890ff' }} />\n                </Tooltip>\n              </div>\n              <Switch\n                checked={settings.stream}\n                onChange={(checked) => handleSettingChange('stream', checked)}\n              />\n            </div>\n\n            <div className=\"setting-row\">\n              <div className=\"setting-label\">\n                <Text>Max Tokens</Text>\n                <Tooltip title=\"Maximum number of tokens in the response\">\n                  <ThunderboltOutlined style={{ marginLeft: 4, color: '#1890ff' }} />\n                </Tooltip>\n              </div>\n              <InputNumber\n                min={100}\n                max={4000}\n                value={settings.maxTokens}\n                onChange={(value) => handleSettingChange('maxTokens', value || 2000)}\n                style={{ width: 120 }}\n              />\n            </div>\n\n            <div className=\"setting-row\">\n              <div className=\"setting-label\">\n                <Text>Temperature</Text>\n                <Tooltip title=\"Controls randomness in responses (0.0 = deterministic, 1.0 = creative)\">\n                  <ThunderboltOutlined style={{ marginLeft: 4, color: '#1890ff' }} />\n                </Tooltip>\n              </div>\n              <InputNumber\n                min={0}\n                max={1}\n                step={0.1}\n                value={settings.temperature}\n                onChange={(value) => handleSettingChange('temperature', value || 0.7)}\n                style={{ width: 120 }}\n              />\n            </div>\n          </Space>\n        </Form.Item>\n\n        <Form.Item>\n          <Button\n            type=\"primary\"\n            icon={<SaveOutlined />}\n            onClick={handleSaveSettings}\n            block\n          >\n            Save Settings\n          </Button>\n        </Form.Item>\n      </Form>\n\n      <div className=\"settings-info\">\n        <Card size=\"small\" className=\"info-card\">\n          <Title level={5}>Current Configuration</Title>\n          <Space direction=\"vertical\" size=\"small\">\n            <div>\n              <Text strong>Agent: </Text>\n              <Tag color={AGENT_TYPES.find(a => a.value === settings.agentType)?.color}>\n                {AGENT_TYPES.find(a => a.value === settings.agentType)?.label}\n              </Tag>\n            </div>\n            <div>\n              <Text strong>Model: </Text>\n              <Tag color=\"blue\">{settings.model}</Tag>\n            </div>\n            <div>\n              <Text strong>Streaming: </Text>\n              <Tag color={settings.stream ? 'green' : 'red'}>\n                {settings.stream ? 'Enabled' : 'Disabled'}\n              </Tag>\n            </div>\n          </Space>\n        </Card>\n      </div>\n    </div>\n  )\n}\n\nexport default AISettings\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/components/ai-settings.tsx b/src/modules/_shared/ai/components/ai-settings.tsx
--- a/src/modules/_shared/ai/components/ai-settings.tsx	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/src/modules/_shared/ai/components/ai-settings.tsx	(date 1751496906059)
@@ -85,7 +85,7 @@
   const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState
 
   const [settings, setSettings] = useState<AISettings>({
-    agentType: 'leader_agent',
+    agentType: 'master_agent',
     model: 'gpt-4.1',
     stream: true,
     maxTokens: 2000,
Index: src/helper/component/custom-icons/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import React, { FC } from 'react'\nimport Icon from '@ant-design/icons'\n\nimport './styles.less'\n\n// table icons declaration\nimport { ReactComponent as EditCustomIcon } from '../../../assets/icons/edit.svg'\nimport { ReactComponent as DeleteCustomIcon } from '../../../assets/icons/delete.svg'\nimport { ReactComponent as CheckmarkCustomIcon } from '../../../assets/icons/check_mark.svg'\n\n// sider icons declaration\nimport { ReactComponent as ObjectSiderCustomIcon } from '../../../assets/icons/object_sider.svg'\nimport { ReactComponent as ActorSiderCustomIcon } from '../../../assets/icons/actor_sider.svg'\nimport { ReactComponent as UsecaseSiderCustomIcon } from '../../../assets/icons/usecase_sider.svg'\nimport { ReactComponent as PermissionMatrixCustomSiderIcon } from '../../../assets/icons/permission_sider.svg'\nimport { ReactComponent as ScreenSiderCustomIcon } from '../../../assets/icons/screen_sider.svg'\nimport { ReactComponent as WorkflowSiderCustomIcon } from '../../../assets/icons/workflow_sider.svg'\nimport { ReactComponent as StateSiderCustomIcon } from '../../../assets/icons/state_sider.svg'\nimport { ReactComponent as NFRCustomIcon } from '../../../assets/icons/nfr_sider.svg'\nimport { ReactComponent as DataMigrationCustomIcon } from '../../../assets/icons/data_migration_sider.svg'\nimport { ReactComponent as MeetingCustomIcon } from '../../../assets/icons/meeting.svg'\n\nconst iconsList = {\n  ObjectSiderCustomIcon,\n  ActorSiderCustomIcon,\n  UsecaseSiderCustomIcon,\n  PermissionMatrixCustomSiderIcon,\n  ScreenSiderCustomIcon,\n  WorkflowSiderCustomIcon,\n  StateSiderCustomIcon,\n  NFRCustomIcon,\n  DataMigrationCustomIcon,\n  EditCustomIcon,\n  DeleteCustomIcon,\n  CheckmarkCustomIcon,\n  MeetingCustomIcon,\n}\n\nconst CustomSvgIcons = (props) => {\n  return <Icon component={iconsList[props.name]} {...props} />\n}\n\nexport default CustomSvgIcons\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/helper/component/custom-icons/index.tsx b/src/helper/component/custom-icons/index.tsx
--- a/src/helper/component/custom-icons/index.tsx	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/src/helper/component/custom-icons/index.tsx	(date 1751496906056)
@@ -19,6 +19,8 @@
 import { ReactComponent as NFRCustomIcon } from '../../../assets/icons/nfr_sider.svg'
 import { ReactComponent as DataMigrationCustomIcon } from '../../../assets/icons/data_migration_sider.svg'
 import { ReactComponent as MeetingCustomIcon } from '../../../assets/icons/meeting.svg'
+import { ReactComponent as CanvasButtonIcon } from '../../../assets/icons/canvas_button.svg'
+
 
 const iconsList = {
   ObjectSiderCustomIcon,
@@ -34,6 +36,7 @@
   DeleteCustomIcon,
   CheckmarkCustomIcon,
   MeetingCustomIcon,
+  CanvasButtonIcon
 }
 
 const CustomSvgIcons = (props) => {
Index: .env.ba
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>REACT_APP_API_LOGIN = \"https://prod-rtmvp.azurewebsites.net\"\nREACT_APP_API_BACKEND = \"https://prod-rtmvp-api.azurewebsites.net/api/\"\nREACT_APP_CLIENT_ID = '837ed5be-bfcd-4c57-a121-b45c442fc65d'\nREACT_APP_AUTHORITY = 'https://login.microsoftonline.com/f01e930a-b52e-42b1-b70f-a8882b5d043b'\nREACT_APP_SCOPES = 'api://ca13b70b-acd7-4f6b-b407-1f132a99c3be/access_as_user'
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.env.ba b/.env.ba
--- a/.env.ba	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/.env.ba	(date 1751496906044)
@@ -1,5 +1,9 @@
-REACT_APP_API_LOGIN = "https://prod-rtmvp.azurewebsites.net"
-REACT_APP_API_BACKEND = "https://prod-rtmvp-api.azurewebsites.net/api/"
-REACT_APP_CLIENT_ID = '837ed5be-bfcd-4c57-a121-b45c442fc65d'
-REACT_APP_AUTHORITY = 'https://login.microsoftonline.com/f01e930a-b52e-42b1-b70f-a8882b5d043b'
-REACT_APP_SCOPES = 'api://ca13b70b-acd7-4f6b-b407-1f132a99c3be/access_as_user'
\ No newline at end of file
+REACT_APP_API_LOGIN = "https://ba.ops-ai.dev"
+REACT_APP_API_BACKEND = "https://ba-vista-backend.ops-ai.dev/api/"
+REACT_APP_API_AI = "https://ba-vista-ai.ops-ai.dev"
+REACT_APP_API_UPLOAD_IMG ="https://ba-vista-backend.ops-ai.dev/api/projects"
+REACT_APP_API_TEMPLATE_USECASE = "https://ba-vista-backend.ops-ai.dev/api/importtemplates/function"
+REACT_APP_API_TEMPLATE_USERREQUIREMENT = "https://ba-vista-backend.ops-ai.dev/api/importtemplates/userrequirement"
+REACT_APP_CLIENT_ID = '4cc7fef8-50e9-47dd-8418-47dd7d837f97'
+REACT_APP_AUTHORITY = 'https://login.microsoftonline.com/1c3cfd54-42ff-43ab-9d54-20d83dea2411'
+REACT_APP_SCOPES = 'api://74098fea-63bb-444b-81cc-a4bf714cb4ca/access_as_user'
Index: src/modules/_shared/ai/reducer.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { createReducer } from '@reduxjs/toolkit'\nimport { AIAssistantState } from './types'\nimport {\n  setCurrentConversation,\n  fetchConversationsRequest,\n  fetchConversationsSuccess,\n  fetchConversationsFailure,\n  createConversationRequest,\n  createConversationSuccess,\n  createConversationFailure,\n  deleteConversationRequest,\n  deleteConversationSuccess,\n  deleteConversationFailure,\n  sendMessageRequest,\n  sendMessageSuccess,\n  sendMessageFailure,\n  receiveAIResponse,\n  startStreamingResponse,\n  receiveStreamingChunk,\n  endStreamingResponse,\n  streamingError,\n  updateTokenUsage,\n  fetchTokenUsageRequest,\n  fetchTokenUsageSuccess,\n  fetchTokenUsageFailure,\n  setAITyping,\n  setSuggestions,\n  clearSuggestions,\n  clearAIError,\n  toggleAIChatPanel,\n  toggleAISetingsPanel,\n  setActiveTab,\n  closeAllPanels\n} from './actions'\n\nconst initialState: AIAssistantState = {\n  isOpen: false,\n  isLoading: false,\n  currentConversation: undefined,\n  conversations: [],\n  totalTokensUsed: 0,\n  availableTokens: 1000, // Default token limit\n  error: undefined,\n  isTyping: false,\n  suggestions: [],\n  // UI Panel States\n  isAiPanelOpen: false,\n  activeTab: null\n}\n\nconst handleTogglePanel = (tab: AIAssistantState[\"activeTab\"]) => (state: AIAssistantState) => {\n  const wasOpen = state.isAiPanelOpen\n  state.isAiPanelOpen = !state.isAiPanelOpen\n  state.activeTab = tab\n}\n\nexport const aiAssistantReducer = createReducer(initialState, (builder) => {\n  builder\n    // UI Actions\n    .addCase(setCurrentConversation, (state, action) => {\n      const conversation = state.conversations.find(c => c.id === action.payload)\n      if (conversation) {\n        state.currentConversation = conversation\n      }\n    })\n\n    // Conversation Actions\n    .addCase(fetchConversationsRequest, (state) => {\n      state.isLoading = true\n      state.error = undefined\n    })\n    .addCase(fetchConversationsSuccess, (state, action) => {\n      state.isLoading = false\n      state.conversations = action.payload\n    })\n    .addCase(fetchConversationsFailure, (state, action) => {\n      state.isLoading = false\n      state.error = action.payload\n    })\n\n    .addCase(createConversationRequest, (state) => {\n      state.isLoading = true\n      state.error = undefined\n    })\n    .addCase(createConversationSuccess, (state, action) => {\n      state.isLoading = false\n      state.conversations.unshift(action.payload)\n      state.currentConversation = action.payload\n    })\n    .addCase(createConversationFailure, (state, action) => {\n      state.isLoading = false\n      state.error = action.payload\n    })\n\n    .addCase(deleteConversationSuccess, (state, action) => {\n      state.conversations = state.conversations.filter(c => c.id !== action.payload)\n      if (state.currentConversation?.id === action.payload) {\n        state.currentConversation = state.conversations[0] || undefined\n      }\n    })\n\n    // Message Actions\n    .addCase(sendMessageRequest, (state) => {\n      state.isLoading = true\n      state.error = undefined\n    })\n    .addCase(sendMessageSuccess, (state, action) => {\n      state.isLoading = false\n      if (state.currentConversation) {\n        state.currentConversation.messages.push(action.payload)\n        state.currentConversation.updatedAt = action.payload.timestamp\n\n        // Update conversation in the list\n        const conversationIndex = state.conversations.findIndex(\n          c => c.id === state.currentConversation?.id\n        )\n        if (conversationIndex !== -1) {\n          state.conversations[conversationIndex] = state.currentConversation\n        }\n      }\n    })\n    .addCase(sendMessageFailure, (state, action) => {\n      state.isLoading = false\n      state.error = action.payload\n    })\n\n    .addCase(receiveAIResponse, (state, action) => {\n      state.isTyping = false\n      if (state.currentConversation) {\n        const aiMessage = {\n          id: Date.now().toString(),\n          content: action.payload.content,\n          type: 'assistant' as const,\n          timestamp: new Date(),\n          tokens: action.payload.tokens\n        }\n\n        state.currentConversation.messages.push(aiMessage)\n        state.currentConversation.totalTokens += action.payload.tokens\n        state.currentConversation.updatedAt = aiMessage.timestamp\n        state.totalTokensUsed += action.payload.tokens\n\n        // Update suggestions if provided\n        if (action.payload.suggestions) {\n          state.suggestions = action.payload.suggestions\n        }\n\n        // Update conversation in the list\n        const conversationIndex = state.conversations.findIndex(\n          c => c.id === state.currentConversation?.id\n        )\n        if (conversationIndex !== -1) {\n          state.conversations[conversationIndex] = state.currentConversation\n        }\n      }\n    })\n\n    // Streaming Actions\n    .addCase(startStreamingResponse, (state, action) => {\n      state.isTyping = true\n      if (state.currentConversation) {\n        const streamingMessage = {\n          id: action.payload,\n          content: '',\n          type: 'assistant' as const,\n          timestamp: new Date(),\n          isLoading: true\n        }\n        state.currentConversation.messages.push(streamingMessage)\n      }\n    })\n    .addCase(receiveStreamingChunk, (state, action) => {\n      if (state.currentConversation) {\n        const messageIndex = state.currentConversation.messages.findIndex(\n          m => m.id === action.payload.messageId\n        )\n        if (messageIndex !== -1) {\n          const message = state.currentConversation.messages[messageIndex]\n          message.content += action.payload.chunk\n\n          // Update agent information if provided\n          if (action.payload.agentName) {\n            message.agentName = action.payload.agentName\n          }\n          if (action.payload.runId) {\n            message.runId = action.payload.runId\n          }\n          if (action.payload.teamId) {\n            message.teamId = action.payload.teamId\n          }\n          if (action.payload.teamName) {\n            message.teamName = action.payload.teamName\n          }\n        }\n      }\n    })\n    .addCase(endStreamingResponse, (state, action) => {\n      state.isTyping = false\n      if (state.currentConversation) {\n        const messageIndex = state.currentConversation.messages.findIndex(\n          m => m.id === action.payload.messageId\n        )\n        if (messageIndex !== -1) {\n          state.currentConversation.messages[messageIndex].isLoading = false\n          state.currentConversation.messages[messageIndex].tokens = action.payload.totalTokens\n          state.currentConversation.totalTokens += action.payload.totalTokens\n          state.totalTokensUsed += action.payload.totalTokens\n          state.currentConversation.updatedAt = new Date()\n        }\n      }\n    })\n    .addCase(streamingError, (state, action) => {\n      state.isTyping = false\n      state.error = action.payload.error\n      if (state.currentConversation) {\n        const messageIndex = state.currentConversation.messages.findIndex(\n          m => m.id === action.payload.messageId\n        )\n        if (messageIndex !== -1) {\n          state.currentConversation.messages[messageIndex].isLoading = false\n          state.currentConversation.messages[messageIndex].content = 'Error: Failed to receive response'\n        }\n      }\n    })\n\n    // Token Actions\n    .addCase(updateTokenUsage, (state, action) => {\n      state.totalTokensUsed = action.payload.used\n      state.availableTokens = action.payload.available\n    })\n    .addCase(fetchTokenUsageSuccess, (state, action) => {\n      state.totalTokensUsed = action.payload.used\n      state.availableTokens = action.payload.available\n    })\n\n    // Typing Actions\n    .addCase(setAITyping, (state, action) => {\n      state.isTyping = action.payload\n    })\n\n    // Suggestions Actions\n    .addCase(setSuggestions, (state, action) => {\n      state.suggestions = action.payload\n    })\n    .addCase(clearSuggestions, (state) => {\n      state.suggestions = []\n    })\n\n    // Error Actions\n    .addCase(clearAIError, (state) => {\n      state.error = undefined\n    })\n    .addCase(toggleAIChatPanel, handleTogglePanel('ai-chat'))\n    .addCase(toggleAISetingsPanel, handleTogglePanel('settings'))\n    .addCase(setActiveTab, (state, action) => {\n      state.activeTab = action.payload\n    })\n    .addCase(closeAllPanels, (state) => {\n      state.isAiPanelOpen = false\n      state.activeTab = null\n    })\n})\n\nexport { initialState as AIState }\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/reducer.ts b/src/modules/_shared/ai/reducer.ts
--- a/src/modules/_shared/ai/reducer.ts	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/src/modules/_shared/ai/reducer.ts	(date 1751496906062)
@@ -1,5 +1,5 @@
 import { createReducer } from '@reduxjs/toolkit'
-import { AIAssistantState } from './types'
+import { AIAssistantState, AIErrorState } from './types'
 import {
   setCurrentConversation,
   fetchConversationsRequest,
@@ -38,9 +38,15 @@
   isLoading: false,
   currentConversation: undefined,
   conversations: [],
-  totalTokensUsed: 0,
-  availableTokens: 1000, // Default token limit
+  sentToken: 0,
+  receiveToken: 0,
+  totalCost: 0,
   error: undefined,
+  errorState: {
+    current: undefined,
+    history: [],
+    isRetrying: false
+  },
   isTyping: false,
   suggestions: [],
   // UI Panel States
@@ -92,12 +98,20 @@
       state.error = action.payload
     })
 
+    .addCase(deleteConversationRequest, (state) => {
+      state.isLoading = true
+      state.error = undefined
+    })
     .addCase(deleteConversationSuccess, (state, action) => {
       state.conversations = state.conversations.filter(c => c.id !== action.payload)
       if (state.currentConversation?.id === action.payload) {
         state.currentConversation = state.conversations[0] || undefined
       }
     })
+    .addCase(deleteConversationFailure, (state, action) => {
+      state.isLoading = false
+      state.error = action.payload
+    })
 
     // Message Actions
     .addCase(sendMessageRequest, (state) => {
@@ -135,10 +149,9 @@
           tokens: action.payload.tokens
         }
 
+        // TODO: Add token usage change
         state.currentConversation.messages.push(aiMessage)
-        state.currentConversation.totalTokens += action.payload.tokens
         state.currentConversation.updatedAt = aiMessage.timestamp
-        state.totalTokensUsed += action.payload.tokens
 
         // Update suggestions if provided
         if (action.payload.suggestions) {
@@ -157,7 +170,6 @@
 
     // Streaming Actions
     .addCase(startStreamingResponse, (state, action) => {
-      state.isTyping = true
       if (state.currentConversation) {
         const streamingMessage = {
           id: action.payload,
@@ -171,6 +183,7 @@
     })
     .addCase(receiveStreamingChunk, (state, action) => {
       if (state.currentConversation) {
+
         const messageIndex = state.currentConversation.messages.findIndex(
           m => m.id === action.payload.messageId
         )
@@ -191,6 +204,7 @@
           if (action.payload.teamName) {
             message.teamName = action.payload.teamName
           }
+          state.isTyping = !Boolean(message.content)
         }
       }
     })
@@ -202,10 +216,8 @@
         )
         if (messageIndex !== -1) {
           state.currentConversation.messages[messageIndex].isLoading = false
-          state.currentConversation.messages[messageIndex].tokens = action.payload.totalTokens
-          state.currentConversation.totalTokens += action.payload.totalTokens
-          state.totalTokensUsed += action.payload.totalTokens
           state.currentConversation.updatedAt = new Date()
+          // TODO: Add token usage change
         }
       }
     })
@@ -225,12 +237,10 @@
 
     // Token Actions
     .addCase(updateTokenUsage, (state, action) => {
-      state.totalTokensUsed = action.payload.used
-      state.availableTokens = action.payload.available
+      // TODO: Add token usage
     })
     .addCase(fetchTokenUsageSuccess, (state, action) => {
-      state.totalTokensUsed = action.payload.used
-      state.availableTokens = action.payload.available
+      // TODO: Add token usage
     })
 
     // Typing Actions
Index: src/modules/_shared/ai/types.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>// API Schema Types based on OpenAPI specification\nexport type AgentType = 'master_agent' | 'ur_agent' | 'hlr_agent'\nexport type AvaiModel = 'gpt-4.1-mini' | 'gpt-4.1' | 'gpt-o4'\n\nexport interface MessageRequest extends Record<string, any> {\n  message: string\n  stream?: boolean\n  model?: AvaiModel\n  agent_type: AgentType\n  project_id: string\n}\n\nexport interface ValidationError {\n  loc: (string | number)[]\n  msg: string\n  type: string\n}\n\nexport interface HTTPValidationError {\n  detail: ValidationError[]\n}\n\nexport interface AIMessage {\n  id: string\n  content: string\n  type: 'user' | 'assistant' | 'system'\n  timestamp: Date\n  isLoading?: boolean\n  tokens?: number\n  attachments?: AIAttachment[]\n  agentName?: string\n  runId?: string\n  teamId?: string\n  teamName?: string\n}\n\nexport interface AIAttachment {\n  id: string\n  name: string\n  type: 'document' | 'image' | 'requirement' | 'artefact'\n  url?: string\n  content?: string\n  size?: number\n}\n\nexport interface AIConversation {\n  id: string\n  title: string\n  messages: AIMessage[]\n  totalTokens: number\n  createdAt: Date\n  updatedAt: Date\n  projectId?: string\n}\n\nexport interface AIAssistantState {\n  isOpen: boolean\n  isLoading: boolean\n  currentConversation?: AIConversation\n  conversations: AIConversation[]\n  totalTokensUsed: number\n  availableTokens: number\n  error?: string\n  isTyping: boolean\n  suggestions: string[]\n  // UI Panel States\n  isAiPanelOpen: boolean\n  activeTab: 'ai-chat' | 'settings' | null\n}\n\nexport interface SendMessageRequest {\n  content: string\n  conversationId: string\n  attachments?: File[]\n  referenceArtefacts?: string[]\n  agentType: AgentType\n  model?: AvaiModel\n  projectId: string\n  stream?: boolean\n}\n\nexport interface CreateConversationRequest {\n  title?: string\n  initialMessage?: string\n  projectId: string\n}\n\nexport interface AIResponse {\n  content: string\n  tokens: number\n  suggestions?: string[]\n  references?: AIReference[]\n}\n\nexport interface AIReference {\n  id: string\n  type: 'requirement' | 'document' | 'artefact'\n  title: string\n  url: string\n  excerpt?: string\n}\n\nexport interface TokenUsage {\n  used: number\n  available: number\n  percentage: number\n}\n\n// Event Stream Types for TeamRunResponseContent\nexport interface TeamRunResponseContentEvent {\n  type: 'agent'\n  event: 'TeamRunResponseContent'\n  agent_id: string | null\n  agent_name: string\n  run_id: string\n  conversation_id: string\n  content: string\n  content_type: 'str'\n  thinking: string\n  team_id: string\n  team_name: string\n}\n\nexport interface StreamingChunkData {\n  messageId: string\n  chunk: string\n  agentName?: string\n  runId?: string\n  teamId?: string\n  teamName?: string\n}\n\nexport enum AIActionTypes {\n  // UI Actions\n  SET_CURRENT_CONVERSATION = 'SET_CURRENT_CONVERSATION',\n\n  // Conversation Actions\n  FETCH_CONVERSATIONS_REQUEST = 'FETCH_CONVERSATIONS_REQUEST',\n  FETCH_CONVERSATIONS_SUCCESS = 'FETCH_CONVERSATIONS_SUCCESS',\n  FETCH_CONVERSATIONS_FAILURE = 'FETCH_CONVERSATIONS_FAILURE',\n\n  CREATE_CONVERSATION_REQUEST = 'CREATE_CONVERSATION_REQUEST',\n  CREATE_CONVERSATION_SUCCESS = 'CREATE_CONVERSATION_SUCCESS',\n  CREATE_CONVERSATION_FAILURE = 'CREATE_CONVERSATION_FAILURE',\n\n  DELETE_CONVERSATION_REQUEST = 'DELETE_CONVERSATION_REQUEST',\n  DELETE_CONVERSATION_SUCCESS = 'DELETE_CONVERSATION_SUCCESS',\n  DELETE_CONVERSATION_FAILURE = 'DELETE_CONVERSATION_FAILURE',\n\n  // Message Actions\n  SEND_MESSAGE_REQUEST = 'SEND_MESSAGE_REQUEST',\n  SEND_MESSAGE_SUCCESS = 'SEND_MESSAGE_SUCCESS',\n  SEND_MESSAGE_FAILURE = 'SEND_MESSAGE_FAILURE',\n\n  RECEIVE_AI_RESPONSE = 'RECEIVE_AI_RESPONSE',\n\n  // Streaming Actions\n  START_STREAMING_RESPONSE = 'START_STREAMING_RESPONSE',\n  RECEIVE_STREAMING_CHUNK = 'RECEIVE_STREAMING_CHUNK',\n  END_STREAMING_RESPONSE = 'END_STREAMING_RESPONSE',\n  STREAMING_ERROR = 'STREAMING_ERROR',\n\n  // Token Actions\n  UPDATE_TOKEN_USAGE = 'UPDATE_TOKEN_USAGE',\n  FETCH_TOKEN_USAGE_REQUEST = 'FETCH_TOKEN_USAGE_REQUEST',\n  FETCH_TOKEN_USAGE_SUCCESS = 'FETCH_TOKEN_USAGE_SUCCESS',\n  FETCH_TOKEN_USAGE_FAILURE = 'FETCH_TOKEN_USAGE_FAILURE',\n\n  // Typing Actions\n  SET_AI_TYPING = 'SET_AI_TYPING',\n\n  // Suggestions Actions\n  SET_SUGGESTIONS = 'SET_SUGGESTIONS',\n  CLEAR_SUGGESTIONS = 'CLEAR_SUGGESTIONS',\n\n  // Error Actions\n  CLEAR_AI_ERROR = 'CLEAR_AI_ERROR',\n\n  // Panel Management Actions\n  TOGGLE_AI_CHAT_PANEL = 'TOGGLE_AI_CHAT_PANEL',\n  TOGGLE_SETTINGS_PANEL = 'TOGGLE_SETTINGS_PANEL',\n  SET_ACTIVE_TAB = 'SET_ACTIVE_TAB',\n  CLOSE_ALL_PANELS = 'CLOSE_ALL_PANELS'\n}\n\nexport interface AIPromptTemplate {\n  id: string\n  title: string\n  description: string\n  prompt: string\n  category: 'summarize' | 'high-level-requirement' | 'details-requirement' | 'review'\n  tokens: number\n}\n\nexport const DEFAULT_PROMPTS: AIPromptTemplate[] = [\n  {\n    id: 'summarize-requirement',\n    title: 'Summarize Requirement',\n    description: 'Summarize this requirement for completeness and clarity',\n    prompt: 'Please summarize this requirement for completeness, clarity, and potential issues. Provide suggestions for improvement.',\n    category: 'summarize',\n    tokens: 50\n  },\n  {\n    id: 'generate-high-level-requirement',\n    title: 'Generate High-Level Requirement',\n    description: 'Generate high-level requirement for this requirement',\n    prompt: 'Based on this requirement, please generate high-level requirement including positive, negative, and edge cases.',\n    category: 'high-level-requirement',\n    tokens: 75\n  },\n  {\n    id: 'use-case-diagram',\n    title: 'Generate Use Case Diagram',\n    description: 'Generate use case diagram for this requirement',\n    prompt: 'Based on this requirement, please generate use case diagram including positive, negative, and edge cases.',\n    category: 'details-requirement',\n    tokens: 60\n  },\n  {\n    id: 'review-requirement',\n    title: 'Review Requirement',\n    description: 'Review this requirement for completeness and clarity',\n    prompt: 'Please review this requirement for completeness, clarity, and adherence to best practices. Suggest improvements.',\n    category: 'review',\n    tokens: 40\n  },\n  {\n    id: 'cr-impact-analysis',\n    title: 'Change Request Impact Analysis',\n    description: 'Analyze the impact of proposed changes on existing requirements and system components',\n    prompt: 'Please analyze the impact of this change request on existing requirements, system architecture, and related components. Identify potential risks, dependencies, and areas that may be affected by implementing this change.',\n    category: 'review',\n    tokens: 40\n  }\n]\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/types.ts b/src/modules/_shared/ai/types.ts
--- a/src/modules/_shared/ai/types.ts	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/src/modules/_shared/ai/types.ts	(date 1751496906063)
@@ -20,6 +20,52 @@
   detail: ValidationError[]
 }
 
+// Enhanced Error Types
+export enum AIErrorType {
+  NETWORK_ERROR = 'NETWORK_ERROR',
+  API_ERROR = 'API_ERROR',
+  VALIDATION_ERROR = 'VALIDATION_ERROR',
+  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
+  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
+  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
+  STREAMING_ERROR = 'STREAMING_ERROR',
+  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
+  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
+}
+
+export enum AIErrorSeverity {
+  LOW = 'LOW',
+  MEDIUM = 'MEDIUM',
+  HIGH = 'HIGH',
+  CRITICAL = 'CRITICAL'
+}
+
+export interface AIError {
+  id: string
+  type: AIErrorType
+  severity: AIErrorSeverity
+  message: string
+  details?: string
+  timestamp: Date
+  context?: {
+    action?: string
+    conversationId?: string
+    messageId?: string
+    userId?: string
+    projectId?: string
+  }
+  originalError?: any
+  retryable: boolean
+  retryCount?: number
+  maxRetries?: number
+}
+
+export interface AIErrorState {
+  current?: AIError
+  history: AIError[]
+  isRetrying: boolean
+}
+
 export interface AIMessage {
   id: string
   content: string
@@ -47,7 +93,9 @@
   id: string
   title: string
   messages: AIMessage[]
-  totalTokens: number
+  sentToken: number
+  receiveToken: number
+  totalCost: number
   createdAt: Date
   updatedAt: Date
   projectId?: string
@@ -58,13 +106,16 @@
   isLoading: boolean
   currentConversation?: AIConversation
   conversations: AIConversation[]
-  totalTokensUsed: number
-  availableTokens: number
-  error?: string
+  error?: string // Keep for backward compatibility
+  errorState: AIErrorState
+  sentToken: number
+  receiveToken: number
+  totalCost: number
   isTyping: boolean
   suggestions: string[]
   // UI Panel States
   isAiPanelOpen: boolean
+  isCanvasOpen?: boolean
   activeTab: 'ai-chat' | 'settings' | null
 }
 
@@ -119,6 +170,7 @@
   thinking: string
   team_id: string
   team_name: string
+  tool_call_id?: string // Optional field for tool calls
 }
 
 export interface StreamingChunkData {
@@ -175,6 +227,10 @@
 
   // Error Actions
   CLEAR_AI_ERROR = 'CLEAR_AI_ERROR',
+  SET_AI_ERROR = 'SET_AI_ERROR',
+  ADD_ERROR_TO_HISTORY = 'ADD_ERROR_TO_HISTORY',
+  CLEAR_ERROR_HISTORY = 'CLEAR_ERROR_HISTORY',
+  SET_RETRY_STATE = 'SET_RETRY_STATE',
 
   // Panel Management Actions
   TOGGLE_AI_CHAT_PANEL = 'TOGGLE_AI_CHAT_PANEL',
Index: src/modules/_shared/ai/components/styles.less
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>@import '../../../../commons.less';\n@import './right-menu.less';\n@import './settings-panel.less';\n@import './ai-assistant.less';\n\n.ai-assistant-container {\n  position: fixed;\n  top: @navbar-height;\n  right: 0;\n  bottom: 0;\n  width: 380px;\n  z-index: 1001;\n  background: white;\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);\n  border-left: 1px solid #e8e8e8;\n\n  // When chat is also open, position AI assistant to the left of chat\n  .chat-open & {\n    right: 400px;\n\n    @media (max-width: 1200px) {\n      right: 350px;\n    }\n\n    @media (max-width: 768px) {\n      right: 0;\n      z-index: 1002; // Higher than chat on mobile\n    }\n  }\n\n  @media (max-width: 768px) {\n    width: 100%;\n    left: 0;\n  }\n\n  .ai-assistant-card {\n    height: 100%;\n    border: none;\n    border-radius: 0;\n    display: flex;\n    flex-direction: column;\n\n    .ant-card-body {\n      padding: 0;\n      height: 100%;\n      display: flex;\n      flex-direction: column;\n    }\n  }\n\n  .ai-assistant-header {\n    background: linear-gradient(135deg, #1890ff, #722ed1);\n    padding: 12px 16px;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    border-bottom: 1px solid #e8e8e8;\n\n    .header-left {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      .ai-icon {\n        color: white;\n        font-size: 18px;\n      }\n\n      .ant-typography {\n        color: white !important;\n        margin: 0;\n      }\n    }\n\n    .header-right {\n      display: flex;\n      gap: 4px;\n    }\n  }\n\n  .token-usage {\n    padding: 12px 16px;\n    background: #f8f9fa;\n    border-bottom: 1px solid #e8e8e8;\n\n    .token-info {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 8px;\n\n      .ant-space {\n        .anticon {\n          font-size: 12px;\n        }\n      }\n    }\n\n    .ant-progress {\n      margin: 0;\n    }\n  }\n\n  .current-task {\n    padding: 16px;\n    background: white;\n    border-bottom: 1px solid #e8e8e8;\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n\n    .ant-typography {\n      margin: 0;\n      flex: 1;\n      font-size: 16px;\n      font-weight: 600;\n      color: #262626;\n    }\n\n    .task-actions {\n      display: flex;\n      gap: 8px;\n    }\n  }\n\n  .conversation-list {\n    border-bottom: 1px solid #e8e8e8;\n    max-height: 200px;\n    overflow-y: auto;\n    background: white;\n\n    .conversation-list-header {\n      padding: 12px 16px 8px;\n      background: #fafafa;\n      border-bottom: 1px solid #f0f0f0;\n    }\n\n    .conversation-items {\n      .conversation-item {\n        padding: 12px 16px;\n        border-bottom: 1px solid #f5f5f5;\n        cursor: pointer;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        transition: background-color 0.2s;\n\n        &:hover {\n          background-color: #f5f5f5;\n        }\n\n        &.active {\n          background-color: #e6f7ff;\n          border-left: 3px solid #1890ff;\n        }\n\n        .conversation-info {\n          flex: 1;\n          min-width: 0;\n\n          .conversation-title {\n            display: block;\n            font-weight: 500;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n          }\n\n          .conversation-time {\n            font-size: 12px;\n          }\n        }\n\n        .delete-conversation-btn {\n          opacity: 0;\n          transition: opacity 0.2s;\n        }\n\n        &:hover .delete-conversation-btn {\n          opacity: 1;\n        }\n      }\n    }\n  }\n\n  .ai-messages {\n    flex: 1;\n    padding: 16px;\n    overflow-y: auto;\n    background: #fafafa;\n    max-height: calc(100vh - 300px);\n\n    .empty-conversation {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n      min-height: 300px;\n\n      .empty-conversation-content {\n        text-align: center;\n        max-width: 300px;\n\n        .empty-icon {\n          font-size: 48px;\n          color: #1890ff;\n          margin-bottom: 16px;\n        }\n\n        h4 {\n          color: #262626;\n          margin-bottom: 8px;\n        }\n\n        .suggested-prompts {\n          margin-top: 24px;\n          text-align: left;\n\n          ul {\n            margin-top: 8px;\n            padding-left: 16px;\n\n            li {\n              margin-bottom: 4px;\n              color: #595959;\n            }\n          }\n        }\n      }\n    }\n\n    .ai-message {\n      margin-bottom: 16px;\n\n      .message-header {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        margin-bottom: 6px;\n\n        .user-avatar {\n          background: #1890ff;\n        }\n\n        .ai-avatar {\n          background: #722ed1;\n        }\n\n        .message-meta {\n          display: flex;\n          flex-direction: column;\n          gap: 2px;\n          flex: 1;\n\n          .agent-name {\n            font-size: 12px;\n            color: #722ed1;\n            margin: 0;\n          }\n\n          .team-name {\n            font-size: 10px;\n            margin: 0;\n          }\n\n          .message-time {\n            font-size: 11px;\n            margin: 0;\n          }\n        }\n\n        .ant-tag {\n          margin: 0;\n          font-size: 10px;\n          padding: 0 4px;\n          height: 18px;\n          line-height: 16px;\n        }\n      }\n\n      .message-content {\n        margin-left: 32px;\n        padding: 8px 12px;\n        border-radius: 8px;\n        word-wrap: break-word;\n\n        .ant-typography {\n          margin: 0;\n          line-height: 1.5;\n        }\n      }\n\n      &.user-message {\n        .message-content {\n          background: #e6f7ff;\n          border: 1px solid #91d5ff;\n        }\n      }\n\n      &.assistant-message {\n        .message-content {\n          background: #f6ffed;\n          border: 1px solid #b7eb8f;\n        }\n      }\n    }\n  }\n\n  .quick-actions {\n    padding: 8px 16px;\n    background: white;\n    border-bottom: 1px solid #e8e8e8;\n\n    .ant-space {\n      width: 100%;\n    }\n\n    .ant-btn {\n      font-size: 12px;\n      height: 24px;\n      padding: 0 8px;\n    }\n  }\n\n  .ai-input-area {\n    background: white;\n    border-top: 1px solid #e8e8e8;\n    padding: 12px 16px;\n\n    .selected-prompt {\n      margin-bottom: 8px;\n\n      .ant-tag {\n        margin: 0;\n        padding: 4px 8px;\n        border-radius: 4px;\n        font-size: 12px;\n\n        .anticon {\n          margin-right: 4px;\n        }\n      }\n    }\n\n    .input-container {\n      display: flex;\n      align-items: flex-end;\n      gap: 8px;\n\n      .message-input {\n        flex: 1;\n        border-radius: 6px;\n        border: 1px solid #d9d9d9;\n        resize: none;\n\n        &:focus {\n          border-color: #1890ff;\n          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n        }\n\n        &::placeholder {\n          color: #bfbfbf;\n          font-size: 13px;\n        }\n      }\n\n      .input-actions {\n        display: flex;\n        align-items: center;\n        gap: 4px;\n\n        .ant-btn {\n          border: none;\n          box-shadow: none;\n          padding: 4px;\n          height: 28px;\n          width: 28px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n\n          &.ant-btn-primary {\n            background: #1890ff;\n\n            &:hover {\n              background: #40a9ff;\n            }\n\n            &:disabled {\n              background: #f5f5f5;\n              color: #bfbfbf;\n            }\n          }\n\n          .anticon {\n            font-size: 14px;\n          }\n        }\n      }\n    }\n  }\n\n  // Conversation History Modal\n  .conversation-history-modal {\n    .ant-modal-header {\n      background: linear-gradient(135deg, #1890ff, #722ed1);\n      border-bottom: 1px solid #e8e8e8;\n\n      .ant-modal-title {\n        color: white;\n        font-weight: 600;\n      }\n    }\n\n    .conversation-modal-content {\n      max-height: 400px;\n      overflow-y: auto;\n\n      .empty-conversations {\n        text-align: center;\n        padding: 40px 20px;\n      }\n\n      .conversation-items {\n        .conversation-item {\n          padding: 12px 16px;\n          border-bottom: 1px solid #f5f5f5;\n          cursor: pointer;\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          transition: background-color 0.2s;\n          border-radius: 6px;\n          margin-bottom: 4px;\n\n          &:hover {\n            background-color: #f5f5f5;\n          }\n\n          &.active {\n            background-color: #e6f7ff;\n            border: 1px solid #91d5ff;\n          }\n\n          .conversation-info {\n            flex: 1;\n            min-width: 0;\n\n            .conversation-title {\n              display: block;\n              font-weight: 500;\n              white-space: nowrap;\n              overflow: hidden;\n              text-overflow: ellipsis;\n              margin-bottom: 4px;\n            }\n\n            .conversation-time {\n              font-size: 12px;\n            }\n          }\n\n          .delete-conversation-btn {\n            opacity: 0;\n            transition: opacity 0.2s;\n          }\n\n          &:hover .delete-conversation-btn {\n            opacity: 1;\n          }\n        }\n      }\n    }\n  }\n}\n\n// AI Assistant Toggle Button\n.ai-assistant-toggle-button {\n  position: fixed;\n  bottom: 90px; // Position above chat button\n  right: 24px;\n  width: 56px;\n  height: 56px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #1890ff, #722ed1);\n  color: white;\n  border: none;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  z-index: 999;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background: linear-gradient(135deg, #40a9ff, #9254de);\n    transform: scale(1.1);\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n\n  .anticon {\n    color: white;\n  }\n}\n\n// Responsive adjustments\n@media (max-width: 1200px) {\n  .ai-assistant-container {\n    width: 350px;\n  }\n}\n\n@media (max-width: 768px) {\n  .ai-assistant-container {\n    width: 100%;\n    left: 0;\n  }\n\n  .ai-assistant-toggle-button {\n    bottom: 80px;\n    right: 16px;\n    width: 48px;\n    height: 48px;\n    font-size: 20px;\n  }\n}\n\n// Animation for AI assistant panel\n.ai-assistant-slide-enter {\n  transform: translateX(100%);\n}\n\n.ai-assistant-slide-enter-active {\n  transform: translateX(0);\n  transition: transform 300ms ease-in-out;\n}\n\n.ai-assistant-slide-exit {\n  transform: translateX(0);\n}\n\n.ai-assistant-slide-exit-active {\n  transform: translateX(100%);\n  transition: transform 300ms ease-in-out;\n}\n\n// Scrollbar styling for messages\n.ai-messages::-webkit-scrollbar {\n  width: 6px;\n}\n\n.ai-messages::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.ai-messages::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n\n  &:hover {\n    background: #a8a8a8;\n  }\n}\n\n// Dropdown menu styling\n.ant-dropdown-menu {\n  .ant-dropdown-menu-item {\n    padding: 8px 12px;\n\n    .ant-typography {\n      margin: 0;\n    }\n  }\n}\n\n// Progress bar customization\n.token-usage .ant-progress-line {\n  .ant-progress-bg {\n    transition: all 0.3s ease;\n  }\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/components/styles.less b/src/modules/_shared/ai/components/styles.less
--- a/src/modules/_shared/ai/components/styles.less	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/src/modules/_shared/ai/components/styles.less	(date 1751496906061)
@@ -290,6 +290,10 @@
         }
       }
 
+      .message-tools {
+        margin-left: 32px;
+      }
+
       &.user-message {
         .message-content {
           background: #e6f7ff;
Index: src/modules/_shared/ai/saga.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { Action } from '@reduxjs/toolkit'\nimport { all, call, fork, put, takeLatest, delay, take } from 'redux-saga/effects'\nimport { eventChannel, EventChannel } from 'redux-saga'\nimport { ShowAppMessage } from '../../../helper/share'\nimport { MESSAGE_TYPE } from '../../../constants'\nimport { extractProjectCode } from '../../../helper/share'\nimport aiAssistantService from '../../../services/ai-assistant.service'\nimport {\n  fetchConversationsRequest,\n  fetchConversationsSuccess,\n  fetchConversationsFailure,\n  createConversationRequest,\n  createConversationSuccess,\n  createConversationFailure,\n  deleteConversationRequest,\n  deleteConversationSuccess,\n  deleteConversationFailure,\n  sendMessageRequest,\n  sendMessageSuccess,\n  sendMessageFailure,\n  receiveAIResponse,\n  startStreamingResponse,\n  receiveStreamingChunk,\n  endStreamingResponse,\n  streamingError,\n  fetchTokenUsageRequest,\n  fetchTokenUsageSuccess,\n  fetchTokenUsageFailure,\n  setAITyping,\n  setSuggestions\n} from './actions'\nimport {\n  AIConversation,\n  AIMessage,\n  SendMessageRequest,\n  CreateConversationRequest,\n  AIResponse,\n  TokenUsage,\n  AgentType,\n  TeamRunResponseContentEvent\n} from './types'\n\n// Default agent type and project configuration\nconst getDefaultAgentType = (): AgentType => 'master_agent'\nconst getProjectId = (): string => extractProjectCode() || 'default-project'\n\n// Create event channel for streaming from POST /v1/conversations/:id/messages\nfunction createStreamingChannel(apiRequest: SendMessageRequest): EventChannel<any> {\n  return eventChannel(emitter => {\n    // Start the streaming request\n    aiAssistantService.sendStreamingMessage(\n      apiRequest,\n      // onChunk callback\n      (event: TeamRunResponseContentEvent) => {\n        emitter({\n          type: 'CHUNK',\n          payload: event\n        })\n      },\n      // onComplete callback\n      () => {\n        emitter({\n          type: 'COMPLETE'\n        })\n        emitter('END')\n      },\n      // onError callback\n      (error: Error) => {\n        emitter({\n          type: 'ERROR',\n          payload: error\n        })\n        emitter('END')\n      }\n    )\n\n    // Return unsubscribe function\n    return () => {\n      aiAssistantService.stopStreaming()\n    }\n  })\n}\n\n// Streaming response handler saga\nfunction* handleStreamingResponse(apiRequest: SendMessageRequest, streamingMessageId: string) {\n  try {\n    const streamingChannel = yield call(createStreamingChannel, apiRequest)\n\n    while (true) {\n      const action = yield take(streamingChannel)\n\n      if (action.type === 'CHUNK') {\n        const event = action.payload as TeamRunResponseContentEvent\n        yield put(receiveStreamingChunk({\n          messageId: streamingMessageId,\n          chunk: event.content,\n          agentName: event.agent_name,\n          runId: event.run_id,\n          teamId: event.team_id,\n          teamName: event.team_name\n        }))\n      } else if (action.type === 'COMPLETE') {\n        yield put(endStreamingResponse({\n          messageId: streamingMessageId,\n          totalTokens: 0 // Will be updated when we get token info\n        }))\n        break\n      } else if (action.type === 'ERROR') {\n        yield put(streamingError({\n          messageId: streamingMessageId,\n          error: action.payload.message\n        }))\n        break\n      }\n    }\n\n    streamingChannel.close()\n\n  } catch (error: any) {\n    yield put(streamingError({\n      messageId: streamingMessageId,\n      error: error.message\n    }))\n  }\n}\n\n// Mock data generators for development\nconst generateMockConversations = (): AIConversation[] => [\n  {\n    id: '1',\n    title: 'Analyst this requirement for me',\n    messages: [\n      {\n        id: '1',\n        content: 'I need to summary requirement',\n        type: 'user',\n        timestamp: new Date(Date.now() - 3600000)\n      },\n      {\n        id: '2',\n        content: 'Please provide me a requirement file (doc, image, pdf...) of give me some some information about ABC project.',\n        type: 'assistant',\n        timestamp: new Date(Date.now() - 3500000),\n        tokens: 25\n      }\n    ],\n    totalTokens: 25,\n    createdAt: new Date(Date.now() - 3600000),\n    updatedAt: new Date(Date.now() - 3500000),\n    projectId: 'current-project'\n  }\n]\n\nconst generateAIResponse = (userMessage: string): AIResponse => {\n  // Mock AI responses based on user input\n  const responses = [\n    {\n      content: \"I'd be happy to help you analyze this requirement. To provide the most accurate analysis, could you please share the specific requirement document or details you'd like me to review?\",\n      tokens: 35,\n      suggestions: [\n        \"Upload requirement document\",\n        \"Describe the requirement\",\n        \"Check for completeness\"\n      ]\n    },\n    {\n      content: \"Based on your request, I can help summarize requirements. Please provide the requirement documents or specific details you'd like me to analyze and summarize.\",\n      tokens: 28,\n      suggestions: [\n        \"Upload documents\",\n        \"List key requirements\",\n        \"Generate summary\"\n      ]\n    },\n    {\n      content: \"I can assist with requirement analysis including completeness checks, clarity assessment, and improvement suggestions. What specific aspect would you like me to focus on?\",\n      tokens: 32,\n      suggestions: [\n        \"Check completeness\",\n        \"Improve clarity\",\n        \"Generate test cases\"\n      ]\n    }\n  ]\n\n  return responses[Math.floor(Math.random() * responses.length)]\n}\n\nfunction* handleFetchConversations(action: Action) {\n  if (fetchConversationsRequest.match(action)) {\n    try {\n      const response = yield call(aiAssistantService.listConversations, 50, 0)\n\n      if (response.status === 200) {\n        // The API returns a different structure, so we need to handle it properly\n        let conversations: AIConversation[] = []\n\n        if (response.data && Array.isArray(response.data)) {\n          // If data is directly an array of conversations\n          conversations = response.data.map((conv: any) => aiAssistantService.transformConversation(conv))\n        } else if (response.data && response.data.conversations) {\n          // If data has a conversations property\n          conversations = response.data.conversations.map((conv: any) => aiAssistantService.transformConversation(conv))\n        }\n\n        yield put(fetchConversationsSuccess(conversations))\n      } else {\n        // Fallback to mock data if API fails\n        const conversations = generateMockConversations()\n        yield put(fetchConversationsSuccess(conversations))\n      }\n    } catch (error: any) {\n      // Fallback to mock data on error\n      const conversations = generateMockConversations()\n      yield put(fetchConversationsSuccess(conversations))\n      console.warn('AI API not available, using mock data:', error.message)\n    }\n  }\n}\n\nfunction* handleCreateConversation(action: Action) {\n  if (createConversationRequest.match(action)) {\n    try {\n      const request = action.payload as CreateConversationRequest\n      const response = yield call(aiAssistantService.createConversation, request)\n\n      if (response.status === 200) {\n        const newConversation = aiAssistantService.transformConversation(response.data)\n        // Set a default title if none provided\n        if (request.title) {\n          newConversation.title = request.title\n        }\n        yield put(createConversationSuccess(newConversation))\n        ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'New conversation created')\n      } else {\n        throw new Error('Failed to create conversation')\n      }\n    } catch (error: any) {\n      yield put(createConversationFailure(error.message))\n      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to create conversation')\n    }\n  }\n}\n\nfunction* handleSendMessage(action: Action) {\n  if (sendMessageRequest.match(action)) {\n    try {\n      const request = action.payload as SendMessageRequest\n\n      // Create user message\n      const userMessage: AIMessage = {\n        id: Date.now().toString(),\n        content: request.content,\n        type: 'user',\n        timestamp: new Date()\n      }\n\n      yield put(sendMessageSuccess(userMessage))\n\n      // Show AI typing indicator\n      yield put(setAITyping(true))\n\n      try {\n        // Send message to AI API\n        const apiRequest: SendMessageRequest = {\n          ...request,\n          agentType: request.agentType || getDefaultAgentType(),\n          projectId: request.projectId || getProjectId()\n        }\n\n        if (request.stream) {\n          // Handle real streaming response with SSE\n          const streamingMessageId = `streaming_${Date.now()}`\n          yield put(startStreamingResponse(streamingMessageId))\n\n          try {\n            // Start the streaming saga\n            yield fork(handleStreamingResponse, apiRequest, streamingMessageId)\n          } catch (streamError: any) {\n            yield put(streamingError({ messageId: streamingMessageId, error: streamError.message }))\n          }\n        } else {\n          // Handle regular response\n          const response = yield call(aiAssistantService.sendMessage, apiRequest)\n\n          if (response.status === 200) {\n            let content = ''\n            let tokensUsed = 0\n\n            if (response.data && response.data.message) {\n              content = response.data.message.content || response.data.message\n              tokensUsed = response.data.tokens_used || response.data.message.tokens || 0\n            } else if (typeof response.data === 'string') {\n              content = response.data\n            }\n\n            const aiResponse: AIResponse = {\n              content,\n              tokens: tokensUsed,\n              suggestions: [] // Add suggestions logic if needed\n            }\n            yield put(receiveAIResponse(aiResponse))\n          } else {\n            throw new Error('Failed to get AI response')\n          }\n        }\n      } catch (apiError: any) {\n        // Fallback to mock response if API fails\n        console.warn('AI API failed, using mock response:', apiError.message)\n        const aiResponse = generateAIResponse(request.content)\n        yield put(receiveAIResponse(aiResponse))\n      }\n\n    } catch (error: any) {\n      yield put(sendMessageFailure(error.message))\n      yield put(setAITyping(false))\n      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to send message')\n    }\n  }\n}\n\nfunction* handleDeleteConversation(action: Action) {\n  if (deleteConversationRequest.match(action)) {\n    try {\n      const conversationId = action.payload as string\n      const response = yield call(aiAssistantService.deleteConversation, conversationId)\n\n      if (response.status === 200) {\n        yield put(deleteConversationSuccess(conversationId))\n        ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'Conversation deleted')\n      } else {\n        throw new Error('Failed to delete conversation')\n      }\n    } catch (error: any) {\n      yield put(deleteConversationFailure(error.message))\n      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to delete conversation')\n    }\n  }\n}\n\nfunction* handleFetchTokenUsage(action: Action) {\n  if (fetchTokenUsageRequest.match(action)) {\n    try {\n      // const response = yield call(apiCall, 'GET', AI_API_URLS.TOKEN_USAGE)\n      yield delay(300)\n\n      const tokenUsage: TokenUsage = {\n        used: 150,\n        available: 1000,\n        percentage: 15\n      }\n\n      yield put(fetchTokenUsageSuccess(tokenUsage))\n    } catch (error) {\n      yield put(fetchTokenUsageFailure('Failed to fetch token usage'))\n    }\n  }\n}\n\nfunction* watchAIAssistantRequests() {\n  yield takeLatest(fetchConversationsRequest.type, handleFetchConversations)\n  yield takeLatest(createConversationRequest.type, handleCreateConversation)\n  yield takeLatest(sendMessageRequest.type, handleSendMessage)\n  yield takeLatest(deleteConversationRequest.type, handleDeleteConversation)\n  yield takeLatest(fetchTokenUsageRequest.type, handleFetchTokenUsage)\n}\n\nexport default function* aiAssistantSaga() {\n  yield all([fork(watchAIAssistantRequests)])\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/saga.ts b/src/modules/_shared/ai/saga.ts
--- a/src/modules/_shared/ai/saga.ts	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/src/modules/_shared/ai/saga.ts	(date 1751496906062)
@@ -27,7 +27,7 @@
   fetchTokenUsageSuccess,
   fetchTokenUsageFailure,
   setAITyping,
-  setSuggestions
+  setSuggestions,
 } from './actions'
 import {
   AIConversation,
@@ -91,18 +91,20 @@
 
       if (action.type === 'CHUNK') {
         const event = action.payload as TeamRunResponseContentEvent
-        yield put(receiveStreamingChunk({
-          messageId: streamingMessageId,
-          chunk: event.content,
-          agentName: event.agent_name,
-          runId: event.run_id,
-          teamId: event.team_id,
-          teamName: event.team_name
-        }))
+        if (event.event === 'TeamRunResponseContent') {
+          yield put(receiveStreamingChunk({
+            messageId: streamingMessageId,
+            chunk: event.content,
+            agentName: event.agent_name,
+            runId: event.run_id,
+            teamId: event.team_id,
+            teamName: event.team_name
+          }))
+        }
       } else if (action.type === 'COMPLETE') {
         yield put(endStreamingResponse({
           messageId: streamingMessageId,
-          totalTokens: 0 // Will be updated when we get token info
+          totalTokens: 0 // TODO: Will be updated when we get token info
         }))
         break
       } else if (action.type === 'ERROR') {
@@ -124,96 +126,27 @@
   }
 }
 
-// Mock data generators for development
-const generateMockConversations = (): AIConversation[] => [
-  {
-    id: '1',
-    title: 'Analyst this requirement for me',
-    messages: [
-      {
-        id: '1',
-        content: 'I need to summary requirement',
-        type: 'user',
-        timestamp: new Date(Date.now() - 3600000)
-      },
-      {
-        id: '2',
-        content: 'Please provide me a requirement file (doc, image, pdf...) of give me some some information about ABC project.',
-        type: 'assistant',
-        timestamp: new Date(Date.now() - 3500000),
-        tokens: 25
-      }
-    ],
-    totalTokens: 25,
-    createdAt: new Date(Date.now() - 3600000),
-    updatedAt: new Date(Date.now() - 3500000),
-    projectId: 'current-project'
-  }
-]
-
-const generateAIResponse = (userMessage: string): AIResponse => {
-  // Mock AI responses based on user input
-  const responses = [
-    {
-      content: "I'd be happy to help you analyze this requirement. To provide the most accurate analysis, could you please share the specific requirement document or details you'd like me to review?",
-      tokens: 35,
-      suggestions: [
-        "Upload requirement document",
-        "Describe the requirement",
-        "Check for completeness"
-      ]
-    },
-    {
-      content: "Based on your request, I can help summarize requirements. Please provide the requirement documents or specific details you'd like me to analyze and summarize.",
-      tokens: 28,
-      suggestions: [
-        "Upload documents",
-        "List key requirements",
-        "Generate summary"
-      ]
-    },
-    {
-      content: "I can assist with requirement analysis including completeness checks, clarity assessment, and improvement suggestions. What specific aspect would you like me to focus on?",
-      tokens: 32,
-      suggestions: [
-        "Check completeness",
-        "Improve clarity",
-        "Generate test cases"
-      ]
-    }
-  ]
-
-  return responses[Math.floor(Math.random() * responses.length)]
-}
-
 function* handleFetchConversations(action: Action) {
   if (fetchConversationsRequest.match(action)) {
     try {
       const response = yield call(aiAssistantService.listConversations, 50, 0)
 
-      if (response.status === 200) {
-        // The API returns a different structure, so we need to handle it properly
-        let conversations: AIConversation[] = []
+      // The API returns a different structure, so we need to handle it properly
+      let conversations: AIConversation[] = []
 
-        if (response.data && Array.isArray(response.data)) {
-          // If data is directly an array of conversations
-          conversations = response.data.map((conv: any) => aiAssistantService.transformConversation(conv))
-        } else if (response.data && response.data.conversations) {
-          // If data has a conversations property
-          conversations = response.data.conversations.map((conv: any) => aiAssistantService.transformConversation(conv))
-        }
+      if (response.data && Array.isArray(response.data)) {
+        // If data is directly an array of conversations
+        conversations = response.data.map((conv: any) => aiAssistantService.transformConversation(conv))
+      } else if (response.data && response.data.conversations) {
+        // If data has a conversations property
+        conversations = response.data.conversations.map((conv: any) => aiAssistantService.transformConversation(conv))
+      }
 
-        yield put(fetchConversationsSuccess(conversations))
-      } else {
-        // Fallback to mock data if API fails
-        const conversations = generateMockConversations()
-        yield put(fetchConversationsSuccess(conversations))
-      }
+      yield put(fetchConversationsSuccess(conversations))
     } catch (error: any) {
       // Fallback to mock data on error
-      const conversations = generateMockConversations()
-      yield put(fetchConversationsSuccess(conversations))
-      console.warn('AI API not available, using mock data:', error.message)
+      yield put(fetchConversationsFailure(error.message))
+      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to fetch conversations')
     }
   }
 }
@@ -224,17 +157,16 @@
       const request = action.payload as CreateConversationRequest
       const response = yield call(aiAssistantService.createConversation, request)
 
-      if (response.status === 200) {
-        const newConversation = aiAssistantService.transformConversation(response.data)
-        // Set a default title if none provided
-        if (request.title) {
-          newConversation.title = request.title
-        }
-        yield put(createConversationSuccess(newConversation))
-        ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'New conversation created')
-      } else {
-        throw new Error('Failed to create conversation')
-      }
+      const newConversation = aiAssistantService.transformConversation(response.data)
+      // Set a default title if none provided
+      if (request.title) {
+        newConversation.title = request.title
+      }
+      newConversation.sentToken = 0
+      newConversation.receiveToken = 0
+      newConversation.totalCost = 0
+      yield put(createConversationSuccess(newConversation))
+      // ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'New conversation created')
     } catch (error: any) {
       yield put(createConversationFailure(error.message))
       ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to create conversation')
@@ -244,9 +176,10 @@
 
 function* handleSendMessage(action: Action) {
   if (sendMessageRequest.match(action)) {
-    try {
-      const request = action.payload as SendMessageRequest
+    const request = action.payload as SendMessageRequest
 
+    try {
+
       // Create user message
       const userMessage: AIMessage = {
         id: Date.now().toString(),
@@ -260,61 +193,51 @@
       // Show AI typing indicator
       yield put(setAITyping(true))
 
-      try {
-        // Send message to AI API
-        const apiRequest: SendMessageRequest = {
-          ...request,
-          agentType: request.agentType || getDefaultAgentType(),
-          projectId: request.projectId || getProjectId()
-        }
+      // Send message to AI API
+      const apiRequest: SendMessageRequest = {
+        ...request,
+        agentType: request.agentType || getDefaultAgentType(),
+        projectId: request.projectId || getProjectId()
+      }
 
-        if (request.stream) {
-          // Handle real streaming response with SSE
-          const streamingMessageId = `streaming_${Date.now()}`
-          yield put(startStreamingResponse(streamingMessageId))
+      if (request.stream) {
+        // Handle real streaming response with SSE
+        const streamingMessageId = `streaming_${Date.now()}`
+        yield put(startStreamingResponse(streamingMessageId))
 
-          try {
-            // Start the streaming saga
-            yield fork(handleStreamingResponse, apiRequest, streamingMessageId)
-          } catch (streamError: any) {
-            yield put(streamingError({ messageId: streamingMessageId, error: streamError.message }))
-          }
-        } else {
-          // Handle regular response
-          const response = yield call(aiAssistantService.sendMessage, apiRequest)
+        try {
+          // Start the streaming saga
+          yield fork(handleStreamingResponse, apiRequest, streamingMessageId)
+        } catch (streamError: any) {
+          yield put(streamingError({ messageId: streamingMessageId, error: streamError.message }))
+        }
+      } else {
+        // Handle regular response
+        const response = yield call(aiAssistantService.sendMessage, apiRequest)
 
-          if (response.status === 200) {
-            let content = ''
-            let tokensUsed = 0
+        if (response.status === 200) {
+          let content = ''
+          let tokensUsed = 0
 
-            if (response.data && response.data.message) {
-              content = response.data.message.content || response.data.message
-              tokensUsed = response.data.tokens_used || response.data.message.tokens || 0
-            } else if (typeof response.data === 'string') {
-              content = response.data
-            }
+          if (response.data && response.data.message) {
+            content = response.data.message.content || response.data.message
+            tokensUsed = response.data.tokens_used || response.data.message.tokens || 0
+          } else if (typeof response.data === 'string') {
+            content = response.data
+          }
 
-            const aiResponse: AIResponse = {
-              content,
-              tokens: tokensUsed,
-              suggestions: [] // Add suggestions logic if needed
-            }
-            yield put(receiveAIResponse(aiResponse))
-          } else {
-            throw new Error('Failed to get AI response')
-          }
+          const aiResponse: AIResponse = {
+            content,
+            tokens: tokensUsed,
+            suggestions: [] // Add suggestions logic if needed
+          }
+          yield put(receiveAIResponse(aiResponse))
+        } else {
+          throw new Error('Failed to get AI response')
         }
-      } catch (apiError: any) {
-        // Fallback to mock response if API fails
-        console.warn('AI API failed, using mock response:', apiError.message)
-        const aiResponse = generateAIResponse(request.content)
-        yield put(receiveAIResponse(aiResponse))
       }
-
     } catch (error: any) {
       yield put(sendMessageFailure(error.message))
-      yield put(setAITyping(false))
-      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to send message')
     }
   }
 }
@@ -323,17 +246,11 @@
   if (deleteConversationRequest.match(action)) {
     try {
       const conversationId = action.payload as string
-      const response = yield call(aiAssistantService.deleteConversation, conversationId)
+      yield call(aiAssistantService.deleteConversation, conversationId)
 
-      if (response.status === 200) {
-        yield put(deleteConversationSuccess(conversationId))
-        ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'Conversation deleted')
-      } else {
-        throw new Error('Failed to delete conversation')
-      }
+      yield put(deleteConversationSuccess(conversationId))
     } catch (error: any) {
       yield put(deleteConversationFailure(error.message))
-      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to delete conversation')
     }
   }
 }
@@ -341,6 +258,7 @@
 function* handleFetchTokenUsage(action: Action) {
   if (fetchTokenUsageRequest.match(action)) {
     try {
+      // TODO: Implement real apis
       // const response = yield call(apiCall, 'GET', AI_API_URLS.TOKEN_USAGE)
       yield delay(300)
 
@@ -351,8 +269,8 @@
       }
 
       yield put(fetchTokenUsageSuccess(tokenUsage))
-    } catch (error) {
-      yield put(fetchTokenUsageFailure('Failed to fetch token usage'))
+    } catch (error: any) {
+      yield put(fetchTokenUsageFailure(error.message))
     }
   }
 }
Index: src/modules/_shared/ai/actions.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { createAction } from '@reduxjs/toolkit'\nimport {\n  AIActionTypes,\n  AIConversation,\n  AIMessage,\n  SendMessageRequest,\n  CreateConversationRequest,\n  AIResponse,\n  TokenUsage,\n  StreamingChunkData\n} from './types'\n\n// UI Actions\nexport const setCurrentConversation = createAction<string>(AIActionTypes.SET_CURRENT_CONVERSATION)\n\n// Conversation Actions\nexport const fetchConversationsRequest = createAction(AIActionTypes.FETCH_CONVERSATIONS_REQUEST)\nexport const fetchConversationsSuccess = createAction<AIConversation[]>(AIActionTypes.FETCH_CONVERSATIONS_SUCCESS)\nexport const fetchConversationsFailure = createAction<string>(AIActionTypes.FETCH_CONVERSATIONS_FAILURE)\n\nexport const createConversationRequest = createAction<CreateConversationRequest>(AIActionTypes.CREATE_CONVERSATION_REQUEST)\nexport const createConversationSuccess = createAction<AIConversation>(AIActionTypes.CREATE_CONVERSATION_SUCCESS)\nexport const createConversationFailure = createAction<string>(AIActionTypes.CREATE_CONVERSATION_FAILURE)\n\nexport const deleteConversationRequest = createAction<string>(AIActionTypes.DELETE_CONVERSATION_REQUEST)\nexport const deleteConversationSuccess = createAction<string>(AIActionTypes.DELETE_CONVERSATION_SUCCESS)\nexport const deleteConversationFailure = createAction<string>(AIActionTypes.DELETE_CONVERSATION_FAILURE)\n\n// Message Actions\nexport const sendMessageRequest = createAction<SendMessageRequest>(AIActionTypes.SEND_MESSAGE_REQUEST)\nexport const sendMessageSuccess = createAction<AIMessage>(AIActionTypes.SEND_MESSAGE_SUCCESS)\nexport const sendMessageFailure = createAction<string>(AIActionTypes.SEND_MESSAGE_FAILURE)\n\nexport const receiveAIResponse = createAction<AIResponse>(AIActionTypes.RECEIVE_AI_RESPONSE)\n\n// Streaming Actions\nexport const startStreamingResponse = createAction<string>(AIActionTypes.START_STREAMING_RESPONSE) // messageId\nexport const receiveStreamingChunk = createAction<StreamingChunkData>(AIActionTypes.RECEIVE_STREAMING_CHUNK)\nexport const endStreamingResponse = createAction<{ messageId: string; totalTokens: number }>(AIActionTypes.END_STREAMING_RESPONSE)\nexport const streamingError = createAction<{ messageId: string; error: string }>(AIActionTypes.STREAMING_ERROR)\n\n// Token Actions\nexport const updateTokenUsage = createAction<TokenUsage>(AIActionTypes.UPDATE_TOKEN_USAGE)\nexport const fetchTokenUsageRequest = createAction(AIActionTypes.FETCH_TOKEN_USAGE_REQUEST)\nexport const fetchTokenUsageSuccess = createAction<TokenUsage>(AIActionTypes.FETCH_TOKEN_USAGE_SUCCESS)\nexport const fetchTokenUsageFailure = createAction<string>(AIActionTypes.FETCH_TOKEN_USAGE_FAILURE)\n\n// Typing Actions\nexport const setAITyping = createAction<boolean>(AIActionTypes.SET_AI_TYPING)\n\n// Suggestions Actions\nexport const setSuggestions = createAction<string[]>(AIActionTypes.SET_SUGGESTIONS)\nexport const clearSuggestions = createAction(AIActionTypes.CLEAR_SUGGESTIONS)\n\n// Error Actions\nexport const clearAIError = createAction(AIActionTypes.CLEAR_AI_ERROR)\n\n// Panel Management Actions\nexport const toggleAIChatPanel = createAction(AIActionTypes.TOGGLE_AI_CHAT_PANEL)\nexport const toggleAISetingsPanel = createAction(AIActionTypes.TOGGLE_SETTINGS_PANEL)\nexport const setActiveTab = createAction<'ai-chat' | 'settings' | null>(AIActionTypes.SET_ACTIVE_TAB)\nexport const closeAllPanels = createAction(AIActionTypes.CLOSE_ALL_PANELS)\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/actions.ts b/src/modules/_shared/ai/actions.ts
--- a/src/modules/_shared/ai/actions.ts	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/src/modules/_shared/ai/actions.ts	(date 1751496906058)
@@ -7,7 +7,8 @@
   CreateConversationRequest,
   AIResponse,
   TokenUsage,
-  StreamingChunkData
+  StreamingChunkData,
+  AIError
 } from './types'
 
 // UI Actions
Index: .env
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>REACT_APP_API_LOGIN = \"http://localhost:3000\"\nREACT_APP_API_BACKEND = \"https://ba-vista-backend.ops-ai.dev/api/\"\nREACT_APP_API_AI = \"http://localhost:8090\"\nREACT_APP_API_UPLOAD_IMG =\"https://ba-vista-backend.ops-ai.dev/api/projects\"\nREACT_APP_API_TEMPLATE_USECASE = \"https://ba-vista-backend.ops-ai.dev/api/importtemplates/function\"\nREACT_APP_API_TEMPLATE_USERREQUIREMENT = \"https://ba-vista-backend.ops-ai.dev/api/importtemplates/userrequirement\"\nREACT_APP_CLIENT_ID = '4cc7fef8-50e9-47dd-8418-47dd7d837f97'\nREACT_APP_AUTHORITY = 'https://login.microsoftonline.com/1c3cfd54-42ff-43ab-9d54-20d83dea2411'\nREACT_APP_SCOPES = 'api://74098fea-63bb-444b-81cc-a4bf714cb4ca/access_as_user'\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.env b/.env
--- a/.env	(revision 604a2fb7715dc8dbdb7ace5c97a95b74792f7107)
+++ b/.env	(date 1751496906044)
@@ -1,6 +1,6 @@
 REACT_APP_API_LOGIN = "http://localhost:3000"
 REACT_APP_API_BACKEND = "https://ba-vista-backend.ops-ai.dev/api/"
-REACT_APP_API_AI = "http://localhost:8090"
+REACT_APP_API_AI = "https://ba-vista-ai.ops-ai.dev"
 REACT_APP_API_UPLOAD_IMG ="https://ba-vista-backend.ops-ai.dev/api/projects"
 REACT_APP_API_TEMPLATE_USECASE = "https://ba-vista-backend.ops-ai.dev/api/importtemplates/function"
 REACT_APP_API_TEMPLATE_USERREQUIREMENT = "https://ba-vista-backend.ops-ai.dev/api/importtemplates/userrequirement"

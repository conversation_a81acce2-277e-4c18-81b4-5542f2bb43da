Index: src/modules/_shared/ai/components/ai-assistant.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import React from 'react'\nimport { useSelector, useDispatch } from 'react-redux'\nimport { Button, Tabs } from 'antd'\nimport { RobotOutlined, SettingOutlined, CloseOutlined, ExpandOutlined } from '@ant-design/icons'\nimport { AIAssistantState } from '../types'\nimport { toggleAIChatPanel, toggleAISettingsPanel, setActiveTab, closeAllPanels } from '../actions'\nimport { AIChatBox } from './chatbox'\nimport AISettings from './ai-settings'\nimport { RightMenu } from './right-menu'\nimport AppState from '../../../../store/types'\nimport './ai-assistant.less'\n\nconst { TabPane } = Tabs\n\ntype AIAssistantProps = {\n  collapsed: boolean\n}\n\nexport const AIAssistant: React.FC<AIAssistantProps> = ({ collapsed }) => {\n  const dispatch = useDispatch()\n  const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState\n\n  const handleToggleAIChat = () => {\n    dispatch(toggleAIChatPanel())\n  }\n\n  const handleToggleSettings = () => {\n    dispatch(toggleAISettingsPanel())\n  }\n\n  const handleTabChange = (activeKey: string) => {\n    dispatch(setActiveTab(activeKey as 'ai-chat' | 'settings'))\n  }\n\n  const handleCloseAll = () => {\n    dispatch(closeAllPanels())\n  }\n\n  return (\n    <>\n      {/* Right Side Menu - Always visible */}\n      <RightMenu\n        onToggleAIChat={handleToggleAIChat}\n        onToggleSettings={handleToggleSettings}\n        isAIChatOpen={aiState.isAiPanelOpen && aiState.activeTab === 'ai-chat'}\n        isSettingsOpen={aiState.isAiPanelOpen && aiState.activeTab === 'settings'}\n      />\n      {/* <CanvasEditor onClose={() => { }} onSave={async () => { }} collapsed={collapsed} /> */}\n      {/* Tabbed Interface - Shows when any panel is open */}\n      {aiState.isAiPanelOpen && (\n        <div className=\"ai-assistant-tabbed-container\">\n          <div className=\"ai-assistant-tabs-header\">\n            <Tabs\n              activeKey={aiState.activeTab || undefined}\n              onChange={handleTabChange}\n              type=\"card\"\n              size=\"small\"\n              tabBarExtraContent={\n                <div className=\"tab-extra-actions\">\n                  <Button about='Toggle Full screen' type=\"text\" onClick={() => { }} icon={<ExpandOutlined />} />\n                  <Button about='Close Chat Panel' type=\"text\" onClick={handleCloseAll} icon={<CloseOutlined />} />\n                </div>\n              }\n            >\n              <TabPane\n                tab={\n                  <span>\n                    <RobotOutlined />\n                    BA Vista\n                  </span>\n                }\n                key=\"ai-chat\"\n              />\n              <TabPane\n                tab={\n                  <span>\n                    <SettingOutlined />\n                    Settings\n                  </span>\n                }\n                key=\"settings\"\n              />\n            </Tabs>\n          </div>\n\n          <div className=\"ai-assistant-tab-content\">\n            {aiState.activeTab === 'ai-chat' && (\n              <AIChatBox\n                isVisible={true}\n                onClose={handleToggleAIChat}\n                hideHeader={true}\n              />\n            )}\n            {aiState.activeTab === 'settings' && (\n              <AISettings />\n            )}\n          </div>\n        </div>\n      )}\n    </>\n  )\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/components/ai-assistant.tsx b/src/modules/_shared/ai/components/ai-assistant.tsx
--- a/src/modules/_shared/ai/components/ai-assistant.tsx	(revision f38906c8539b9e76738b3328caadbab18a30ce17)
+++ b/src/modules/_shared/ai/components/ai-assistant.tsx	(date 1751964160117)
@@ -9,6 +9,7 @@
 import { RightMenu } from './right-menu'
 import AppState from '../../../../store/types'
 import './ai-assistant.less'
+import intl from '../../../../config/locale.config'
 
 const { TabPane } = Tabs
 
@@ -66,7 +67,7 @@
                 tab={
                   <span>
                     <RobotOutlined />
-                    BA Vista
+                    {intl.formatMessage({ id: 'ai.agent.name' }) }
                   </span>
                 }
                 key="ai-chat"
Index: src/locales/en-US/ai.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>export default {\n  'ai.agent.name': 'OrcaAI',\n  'ai.send-token.description': 'Token that uses the AI agent to generate responses.',\n  'ai.receive-token.description': 'Token that receives the AI agent\\'s response.',\n  'ai.welcome-message.title': 'Welcome to Orca AI Assistant',\n  'ai.welcome-message.description': 'I\\'m here to help you analyze requirements, generate documentation, and improve your business analysis workflow.',\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/locales/en-US/ai.ts b/src/locales/en-US/ai.ts
--- a/src/locales/en-US/ai.ts	(revision f38906c8539b9e76738b3328caadbab18a30ce17)
+++ b/src/locales/en-US/ai.ts	(date 1751964300224)
@@ -1,5 +1,5 @@
 export default {
-  'ai.agent.name': 'OrcaAI',
+  'ai.agent.name': 'Orca AI',
   'ai.send-token.description': 'Token that uses the AI agent to generate responses.',
   'ai.receive-token.description': 'Token that receives the AI agent\'s response.',
   'ai.welcome-message.title': 'Welcome to Orca AI Assistant',

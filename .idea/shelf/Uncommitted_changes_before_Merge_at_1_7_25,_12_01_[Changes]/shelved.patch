Index: src/modules/_shared/ai/components/ai-settings.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import React, { useState } from 'react'\nimport { useDispatch, useSelector } from 'react-redux'\nimport {\n  Card,\n  Select,\n  Form,\n  Typography,\n  Space,\n  Divider,\n  Tag,\n  Tooltip,\n  Switch,\n  InputNumber,\n  Button\n} from 'antd'\nimport {\n  RobotOutlined,\n  SettingOutlined,\n  ThunderboltOutlined,\n  SaveOutlined\n} from '@ant-design/icons'\nimport { AgentType, AvaiModel, AIAssistantState } from '../types'\nimport AppState from '../../../../store/types'\nimport './ai-settings.less'\n\nconst { Title, Text } = Typography\nconst { Option } = Select\n\ninterface AISettingsProps {\n  onSettingsChange?: (settings: AISettings) => void\n}\n\nexport interface AISettings {\n  agentType: AgentType\n  model: AvaiModel\n  stream: boolean\n  maxTokens: number\n  temperature: number\n}\n\nconst AGENT_TYPES: { value: AgentType; label: string; description: string; color: string }[] = [\n  {\n    value: 'master_agent',\n    label: 'Master Agent',\n    description: 'Strategic analysis and high-level decision making',\n    color: 'blue'\n  },\n  {\n    value: 'ur_agent',\n    label: 'User Requirement Agent',\n    description: 'Specialized in user requirements analysis',\n    color: 'green'\n  },\n  {\n    value: 'hlr_agent',\n    label: 'High-Level Requirement Agent',\n    description: 'Focus on high-level requirement specification',\n    color: 'purple'\n  }\n]\n\nconst AI_MODELS: { value: AvaiModel; label: string; description: string; performance: string }[] = [\n  {\n    value: 'gpt-4.1-mini',\n    label: 'GPT-4.1 Mini',\n    description: 'Fast and efficient for simple tasks',\n    performance: 'Fast'\n  },\n  {\n    value: 'gpt-4.1',\n    label: 'GPT-4.1',\n    description: 'Balanced performance and capability',\n    performance: 'Balanced'\n  },\n  {\n    value: 'gpt-o4',\n    label: 'GPT-O4',\n    description: 'Most advanced model for complex analysis',\n    performance: 'Advanced'\n  }\n]\n\nexport const AISettings: React.FC<AISettingsProps> = ({ onSettingsChange }) => {\n  const dispatch = useDispatch()\n  const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState\n\n  const [settings, setSettings] = useState<AISettings>({\n    agentType: 'leader_agent',\n    model: 'gpt-4.1',\n    stream: true,\n    maxTokens: 2000,\n    temperature: 0.7\n  })\n\n  const handleSettingChange = (key: keyof AISettings, value: any) => {\n    const newSettings = { ...settings, [key]: value }\n    setSettings(newSettings)\n    onSettingsChange?.(newSettings)\n  }\n\n  const handleSaveSettings = () => {\n    // Save settings to localStorage or dispatch to Redux\n    localStorage.setItem('ai-assistant-settings', JSON.stringify(settings))\n    // You could also dispatch an action to save to Redux store\n  }\n\n  const renderAgentTypeOption = (agent: typeof AGENT_TYPES[0]) => (\n    <Option key={agent.value} value={agent.value}>\n      <div className=\"agent-option\">\n        <div className=\"agent-header\">\n          <Tag color={agent.color}>{agent.label}</Tag>\n        </div>\n        <Text type=\"secondary\" className=\"agent-description\">\n          {agent.description}\n        </Text>\n      </div>\n    </Option>\n  )\n\n  const renderModelOption = (model: typeof AI_MODELS[0]) => (\n    <Option key={model.value} value={model.value}>\n      <div className=\"model-option\">\n        <div className=\"model-header\">\n          <span className=\"model-name\">{model.label}</span>\n          <Tag color={model.performance === 'Fast' ? 'green' : model.performance === 'Balanced' ? 'blue' : 'gold'}>\n            {model.performance}\n          </Tag>\n        </div>\n        <Text type=\"secondary\" className=\"model-description\">\n          {model.description}\n        </Text>\n      </div>\n    </Option>\n  )\n\n  return (\n    <div className=\"ai-settings\">\n      <div className=\"settings-header\">\n        <Title level={4}>\n          <SettingOutlined /> AI Assistant Settings\n        </Title>\n      </div>\n\n      <Form layout=\"vertical\" className=\"settings-form\">\n        <Form.Item label=\"Agent Type\" className=\"agent-selection\">\n          <Select\n            value={settings.agentType}\n            onChange={(value) => handleSettingChange('agentType', value)}\n            placeholder=\"Select an agent type\"\n            optionLabelProp=\"label\"\n          >\n            {AGENT_TYPES.map(renderAgentTypeOption)}\n          </Select>\n          <Text type=\"secondary\" className=\"help-text\">\n            Choose the type of AI agent based on your analysis needs\n          </Text>\n        </Form.Item>\n\n        <Form.Item label=\"AI Model\" className=\"model-selection\">\n          <Select\n            value={settings.model}\n            onChange={(value) => handleSettingChange('model', value)}\n            placeholder=\"Select an AI model\"\n            optionLabelProp=\"label\"\n          >\n            {AI_MODELS.map(renderModelOption)}\n          </Select>\n          <Text type=\"secondary\" className=\"help-text\">\n            Select the AI model that best fits your task complexity\n          </Text>\n        </Form.Item>\n\n        <Divider />\n\n        <Form.Item label=\"Advanced Settings\">\n          <Space direction=\"vertical\" style={{ width: '100%' }}>\n            <div className=\"setting-row\">\n              <div className=\"setting-label\">\n                <Text>Streaming Response</Text>\n                <Tooltip title=\"Enable real-time streaming of AI responses\">\n                  <ThunderboltOutlined style={{ marginLeft: 4, color: '#1890ff' }} />\n                </Tooltip>\n              </div>\n              <Switch\n                checked={settings.stream}\n                onChange={(checked) => handleSettingChange('stream', checked)}\n              />\n            </div>\n\n            <div className=\"setting-row\">\n              <div className=\"setting-label\">\n                <Text>Max Tokens</Text>\n                <Tooltip title=\"Maximum number of tokens in the response\">\n                  <ThunderboltOutlined style={{ marginLeft: 4, color: '#1890ff' }} />\n                </Tooltip>\n              </div>\n              <InputNumber\n                min={100}\n                max={4000}\n                value={settings.maxTokens}\n                onChange={(value) => handleSettingChange('maxTokens', value || 2000)}\n                style={{ width: 120 }}\n              />\n            </div>\n\n            <div className=\"setting-row\">\n              <div className=\"setting-label\">\n                <Text>Temperature</Text>\n                <Tooltip title=\"Controls randomness in responses (0.0 = deterministic, 1.0 = creative)\">\n                  <ThunderboltOutlined style={{ marginLeft: 4, color: '#1890ff' }} />\n                </Tooltip>\n              </div>\n              <InputNumber\n                min={0}\n                max={1}\n                step={0.1}\n                value={settings.temperature}\n                onChange={(value) => handleSettingChange('temperature', value || 0.7)}\n                style={{ width: 120 }}\n              />\n            </div>\n          </Space>\n        </Form.Item>\n\n        <Form.Item>\n          <Button\n            type=\"primary\"\n            icon={<SaveOutlined />}\n            onClick={handleSaveSettings}\n            block\n          >\n            Save Settings\n          </Button>\n        </Form.Item>\n      </Form>\n\n      <div className=\"settings-info\">\n        <Card size=\"small\" className=\"info-card\">\n          <Title level={5}>Current Configuration</Title>\n          <Space direction=\"vertical\" size=\"small\">\n            <div>\n              <Text strong>Agent: </Text>\n              <Tag color={AGENT_TYPES.find(a => a.value === settings.agentType)?.color}>\n                {AGENT_TYPES.find(a => a.value === settings.agentType)?.label}\n              </Tag>\n            </div>\n            <div>\n              <Text strong>Model: </Text>\n              <Tag color=\"blue\">{settings.model}</Tag>\n            </div>\n            <div>\n              <Text strong>Streaming: </Text>\n              <Tag color={settings.stream ? 'green' : 'red'}>\n                {settings.stream ? 'Enabled' : 'Disabled'}\n              </Tag>\n            </div>\n          </Space>\n        </Card>\n      </div>\n    </div>\n  )\n}\n\nexport default AISettings\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/components/ai-settings.tsx b/src/modules/_shared/ai/components/ai-settings.tsx
--- a/src/modules/_shared/ai/components/ai-settings.tsx	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/modules/_shared/ai/components/ai-settings.tsx	(date 1751346090160)
@@ -85,7 +85,7 @@
   const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState
 
   const [settings, setSettings] = useState<AISettings>({
-    agentType: 'leader_agent',
+    agentType: 'master_agent',
     model: 'gpt-4.1',
     stream: true,
     maxTokens: 2000,
Index: src/modules/_shared/ai/types.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>// API Schema Types based on OpenAPI specification\nexport type AgentType = 'master_agent' | 'ur_agent' | 'hlr_agent'\nexport type AvaiModel = 'gpt-4.1-mini' | 'gpt-4.1' | 'gpt-o4'\n\nexport interface MessageRequest extends Record<string, any> {\n  message: string\n  stream?: boolean\n  model?: AvaiModel\n  agent_type: AgentType\n  project_id: string\n}\n\nexport interface ValidationError {\n  loc: (string | number)[]\n  msg: string\n  type: string\n}\n\nexport interface HTTPValidationError {\n  detail: ValidationError[]\n}\n\nexport interface AIMessage {\n  id: string\n  content: string\n  type: 'user' | 'assistant' | 'system'\n  timestamp: Date\n  isLoading?: boolean\n  tokens?: number\n  attachments?: AIAttachment[]\n  agentName?: string\n  runId?: string\n  teamId?: string\n  teamName?: string\n}\n\nexport interface AIAttachment {\n  id: string\n  name: string\n  type: 'document' | 'image' | 'requirement' | 'artefact'\n  url?: string\n  content?: string\n  size?: number\n}\n\nexport interface AIConversation {\n  id: string\n  title: string\n  messages: AIMessage[]\n  totalTokens: number\n  createdAt: Date\n  updatedAt: Date\n  projectId?: string\n}\n\nexport interface AIAssistantState {\n  isOpen: boolean\n  isLoading: boolean\n  currentConversation?: AIConversation\n  conversations: AIConversation[]\n  totalTokensUsed: number\n  availableTokens: number\n  error?: string\n  isTyping: boolean\n  suggestions: string[]\n  // UI Panel States\n  isAiPanelOpen: boolean\n  activeTab: 'ai-chat' | 'settings' | null\n}\n\nexport interface SendMessageRequest {\n  content: string\n  conversationId: string\n  attachments?: File[]\n  referenceArtefacts?: string[]\n  agentType: AgentType\n  model?: AvaiModel\n  projectId: string\n  stream?: boolean\n}\n\nexport interface CreateConversationRequest {\n  title?: string\n  initialMessage?: string\n  projectId: string\n}\n\nexport interface AIResponse {\n  content: string\n  tokens: number\n  suggestions?: string[]\n  references?: AIReference[]\n}\n\nexport interface AIReference {\n  id: string\n  type: 'requirement' | 'document' | 'artefact'\n  title: string\n  url: string\n  excerpt?: string\n}\n\nexport interface TokenUsage {\n  used: number\n  available: number\n  percentage: number\n}\n\n// Event Stream Types for TeamRunResponseContent\nexport interface TeamRunResponseContentEvent {\n  type: 'agent'\n  event: 'TeamRunResponseContent'\n  agent_id: string | null\n  agent_name: string\n  run_id: string\n  conversation_id: string\n  content: string\n  content_type: 'str'\n  thinking: string\n  team_id: string\n  team_name: string\n}\n\nexport interface StreamingChunkData {\n  messageId: string\n  chunk: string\n  agentName?: string\n  runId?: string\n  teamId?: string\n  teamName?: string\n}\n\nexport enum AIActionTypes {\n  // UI Actions\n  SET_CURRENT_CONVERSATION = 'SET_CURRENT_CONVERSATION',\n\n  // Conversation Actions\n  FETCH_CONVERSATIONS_REQUEST = 'FETCH_CONVERSATIONS_REQUEST',\n  FETCH_CONVERSATIONS_SUCCESS = 'FETCH_CONVERSATIONS_SUCCESS',\n  FETCH_CONVERSATIONS_FAILURE = 'FETCH_CONVERSATIONS_FAILURE',\n\n  CREATE_CONVERSATION_REQUEST = 'CREATE_CONVERSATION_REQUEST',\n  CREATE_CONVERSATION_SUCCESS = 'CREATE_CONVERSATION_SUCCESS',\n  CREATE_CONVERSATION_FAILURE = 'CREATE_CONVERSATION_FAILURE',\n\n  DELETE_CONVERSATION_REQUEST = 'DELETE_CONVERSATION_REQUEST',\n  DELETE_CONVERSATION_SUCCESS = 'DELETE_CONVERSATION_SUCCESS',\n  DELETE_CONVERSATION_FAILURE = 'DELETE_CONVERSATION_FAILURE',\n\n  // Message Actions\n  SEND_MESSAGE_REQUEST = 'SEND_MESSAGE_REQUEST',\n  SEND_MESSAGE_SUCCESS = 'SEND_MESSAGE_SUCCESS',\n  SEND_MESSAGE_FAILURE = 'SEND_MESSAGE_FAILURE',\n\n  RECEIVE_AI_RESPONSE = 'RECEIVE_AI_RESPONSE',\n\n  // Streaming Actions\n  START_STREAMING_RESPONSE = 'START_STREAMING_RESPONSE',\n  RECEIVE_STREAMING_CHUNK = 'RECEIVE_STREAMING_CHUNK',\n  END_STREAMING_RESPONSE = 'END_STREAMING_RESPONSE',\n  STREAMING_ERROR = 'STREAMING_ERROR',\n\n  // Token Actions\n  UPDATE_TOKEN_USAGE = 'UPDATE_TOKEN_USAGE',\n  FETCH_TOKEN_USAGE_REQUEST = 'FETCH_TOKEN_USAGE_REQUEST',\n  FETCH_TOKEN_USAGE_SUCCESS = 'FETCH_TOKEN_USAGE_SUCCESS',\n  FETCH_TOKEN_USAGE_FAILURE = 'FETCH_TOKEN_USAGE_FAILURE',\n\n  // Typing Actions\n  SET_AI_TYPING = 'SET_AI_TYPING',\n\n  // Suggestions Actions\n  SET_SUGGESTIONS = 'SET_SUGGESTIONS',\n  CLEAR_SUGGESTIONS = 'CLEAR_SUGGESTIONS',\n\n  // Error Actions\n  CLEAR_AI_ERROR = 'CLEAR_AI_ERROR',\n\n  // Panel Management Actions\n  TOGGLE_AI_CHAT_PANEL = 'TOGGLE_AI_CHAT_PANEL',\n  TOGGLE_SETTINGS_PANEL = 'TOGGLE_SETTINGS_PANEL',\n  SET_ACTIVE_TAB = 'SET_ACTIVE_TAB',\n  CLOSE_ALL_PANELS = 'CLOSE_ALL_PANELS'\n}\n\nexport interface AIPromptTemplate {\n  id: string\n  title: string\n  description: string\n  prompt: string\n  category: 'summarize' | 'high-level-requirement' | 'details-requirement' | 'review'\n  tokens: number\n}\n\nexport const DEFAULT_PROMPTS: AIPromptTemplate[] = [\n  {\n    id: 'summarize-requirement',\n    title: 'Summarize Requirement',\n    description: 'Summarize this requirement for completeness and clarity',\n    prompt: 'Please summarize this requirement for completeness, clarity, and potential issues. Provide suggestions for improvement.',\n    category: 'summarize',\n    tokens: 50\n  },\n  {\n    id: 'generate-high-level-requirement',\n    title: 'Generate High-Level Requirement',\n    description: 'Generate high-level requirement for this requirement',\n    prompt: 'Based on this requirement, please generate high-level requirement including positive, negative, and edge cases.',\n    category: 'high-level-requirement',\n    tokens: 75\n  },\n  {\n    id: 'use-case-diagram',\n    title: 'Generate Use Case Diagram',\n    description: 'Generate use case diagram for this requirement',\n    prompt: 'Based on this requirement, please generate use case diagram including positive, negative, and edge cases.',\n    category: 'details-requirement',\n    tokens: 60\n  },\n  {\n    id: 'review-requirement',\n    title: 'Review Requirement',\n    description: 'Review this requirement for completeness and clarity',\n    prompt: 'Please review this requirement for completeness, clarity, and adherence to best practices. Suggest improvements.',\n    category: 'review',\n    tokens: 40\n  },\n  {\n    id: 'cr-impact-analysis',\n    title: 'Change Request Impact Analysis',\n    description: 'Analyze the impact of proposed changes on existing requirements and system components',\n    prompt: 'Please analyze the impact of this change request on existing requirements, system architecture, and related components. Identify potential risks, dependencies, and areas that may be affected by implementing this change.',\n    category: 'review',\n    tokens: 40\n  }\n]\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/types.ts b/src/modules/_shared/ai/types.ts
--- a/src/modules/_shared/ai/types.ts	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/modules/_shared/ai/types.ts	(date 1751346090161)
@@ -20,6 +20,52 @@
   detail: ValidationError[]
 }
 
+// Enhanced Error Types
+export enum AIErrorType {
+  NETWORK_ERROR = 'NETWORK_ERROR',
+  API_ERROR = 'API_ERROR',
+  VALIDATION_ERROR = 'VALIDATION_ERROR',
+  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
+  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
+  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
+  STREAMING_ERROR = 'STREAMING_ERROR',
+  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
+  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
+}
+
+export enum AIErrorSeverity {
+  LOW = 'LOW',
+  MEDIUM = 'MEDIUM',
+  HIGH = 'HIGH',
+  CRITICAL = 'CRITICAL'
+}
+
+export interface AIError {
+  id: string
+  type: AIErrorType
+  severity: AIErrorSeverity
+  message: string
+  details?: string
+  timestamp: Date
+  context?: {
+    action?: string
+    conversationId?: string
+    messageId?: string
+    userId?: string
+    projectId?: string
+  }
+  originalError?: any
+  retryable: boolean
+  retryCount?: number
+  maxRetries?: number
+}
+
+export interface AIErrorState {
+  current?: AIError
+  history: AIError[]
+  isRetrying: boolean
+}
+
 export interface AIMessage {
   id: string
   content: string
@@ -47,7 +93,9 @@
   id: string
   title: string
   messages: AIMessage[]
-  totalTokens: number
+  sentToken: number
+  receiveToken: number
+  totalCost: number
   createdAt: Date
   updatedAt: Date
   projectId?: string
@@ -58,9 +106,11 @@
   isLoading: boolean
   currentConversation?: AIConversation
   conversations: AIConversation[]
-  totalTokensUsed: number
-  availableTokens: number
-  error?: string
+  error?: string // Keep for backward compatibility
+  errorState: AIErrorState
+  sentToken: number
+  receiveToken: number
+  totalCost: number
   isTyping: boolean
   suggestions: string[]
   // UI Panel States
@@ -119,6 +169,7 @@
   thinking: string
   team_id: string
   team_name: string
+  tool_call_id?: string // Optional field for tool calls
 }
 
 export interface StreamingChunkData {
@@ -175,6 +226,10 @@
 
   // Error Actions
   CLEAR_AI_ERROR = 'CLEAR_AI_ERROR',
+  SET_AI_ERROR = 'SET_AI_ERROR',
+  ADD_ERROR_TO_HISTORY = 'ADD_ERROR_TO_HISTORY',
+  CLEAR_ERROR_HISTORY = 'CLEAR_ERROR_HISTORY',
+  SET_RETRY_STATE = 'SET_RETRY_STATE',
 
   // Panel Management Actions
   TOGGLE_AI_CHAT_PANEL = 'TOGGLE_AI_CHAT_PANEL',
Index: src/modules/_shared/ai/README.md
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/README.md b/src/modules/_shared/ai/README.md
deleted file mode 100644
--- a/src/modules/_shared/ai/README.md	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ /dev/null	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
@@ -1,214 +0,0 @@
-# AI Chat Assistant Integration
-
-This module provides a complete integration with the AI Chat Assistant backend API based on the OpenAPI specification.
-
-## Features
-
-- ✅ **Real API Integration**: Connects to the actual AI backend API
-- ✅ **Multiple Agent Types**: Support for leader_agent, ur_agent, and hlr_agent
-- ✅ **Multiple AI Models**: Support for gpt-4.1-mini, gpt-4.1, and gpt-o4
-- ✅ **Streaming Support**: Real-time streaming responses from the AI
-- ✅ **Settings Panel**: User-configurable agent types and models
-- ✅ **Error Handling**: Graceful fallback to mock responses when API is unavailable
-- ✅ **Type Safety**: Full TypeScript support with OpenAPI-based types
-- ✅ **Comprehensive Testing**: Unit tests, integration tests, and manual test runner
-
-## Architecture
-
-### Service Layer (`ai-assistant.service.ts`)
-- Handles all API communication
-- Transforms API responses to internal format
-- Provides streaming support
-- Includes error handling and fallbacks
-
-### Redux Integration
-- **Actions**: All AI-related actions including streaming support
-- **Reducer**: State management for conversations, messages, and UI state
-- **Saga**: Handles async operations and API calls
-
-### UI Components
-- **AIChatBox**: Main chat interface
-- **AISettings**: Configuration panel for agent types and models
-- **AIAssistant**: Tabbed interface combining chat and settings
-
-## API Endpoints
-
-Based on the OpenAPI specification:
-
-- `GET /v1/health` - Health check
-- `GET /v1/agents` - List available agents
-- `GET /v1/conversations` - List conversations
-- `POST /v1/conversations/{conversation_id}/messages` - Send message
-- `DELETE /v1/conversations/{conversation_id}` - Delete conversation
-
-## Configuration
-
-### Environment Variables
-
-Add to your `.env` file:
-```
-REACT_APP_API_AI_ASSISTANT=https://ai-assistant-api.ops-ai.dev
-```
-
-### Agent Types
-
-- **leader_agent**: Strategic analysis and high-level decision making
-- **ur_agent**: Specialized in user requirements analysis  
-- **hlr_agent**: Focus on high-level requirement specification
-
-### AI Models
-
-- **gpt-4.1-mini**: Fast and efficient for simple tasks
-- **gpt-4.1**: Balanced performance and capability
-- **gpt-o4**: Most advanced model for complex analysis
-
-## Usage
-
-### Basic Chat Integration
-
-```tsx
-import { AIChatBox } from './modules/_shared/ai/components/chatbox'
-
-function MyComponent() {
-  return (
-    <AIChatBox 
-      isVisible={true} 
-      onClose={() => {}} 
-    />
-  )
-}
-```
-
-### Settings Panel
-
-```tsx
-import AISettings from './modules/_shared/ai/components/ai-settings'
-
-function SettingsPage() {
-  return (
-    <AISettings 
-      onSettingsChange={(settings) => console.log(settings)} 
-    />
-  )
-}
-```
-
-### Redux Integration
-
-```tsx
-import { useDispatch } from 'react-redux'
-import { sendMessageRequest } from './modules/_shared/ai/actions'
-
-function sendMessage() {
-  dispatch(sendMessageRequest({
-    content: 'Hello AI',
-    conversationId: 'conv-123',
-    agentType: 'leader_agent',
-    projectId: 'my-project'
-  }))
-}
-```
-
-## Testing
-
-### Running Tests
-
-```bash
-# Run all AI tests
-npm test -- --testPathPattern=ai
-
-# Run specific test files
-npm test ai-assistant.service.test.ts
-npm test reducer.test.ts
-npm test integration.test.ts
-```
-
-### Manual Testing
-
-Use the built-in test runner:
-
-```typescript
-import { runAIIntegrationTests } from './modules/_shared/ai/test-runner'
-
-// Run all integration tests
-await runAIIntegrationTests()
-```
-
-Or in browser console:
-```javascript
-window.runAITests()
-```
-
-## Error Handling
-
-The integration includes multiple layers of error handling:
-
-1. **API Level**: Service layer catches and transforms API errors
-2. **Saga Level**: Fallback to mock responses when API is unavailable
-3. **UI Level**: User-friendly error messages and loading states
-
-## Streaming Support
-
-The integration supports real-time streaming responses:
-
-```typescript
-// Enable streaming in settings
-const settings = {
-  agentType: 'leader_agent',
-  model: 'gpt-4.1',
-  stream: true
-}
-
-// Messages will be streamed in real-time to the UI
-```
-
-## Development
-
-### Adding New Agent Types
-
-1. Update the `AgentType` enum in `types.ts`
-2. Add the new agent to the `AGENT_TYPES` array in `ai-settings.tsx`
-3. Update tests to include the new agent type
-
-### Adding New Models
-
-1. Update the `AvaiModel` enum in `types.ts`
-2. Add the new model to the `AI_MODELS` array in `ai-settings.tsx`
-3. Update tests to include the new model
-
-### Extending API Functionality
-
-1. Add new methods to `ai-assistant.service.ts`
-2. Create corresponding actions in `actions.ts`
-3. Update the reducer to handle new actions
-4. Add saga handlers for async operations
-5. Write tests for new functionality
-
-## Troubleshooting
-
-### API Connection Issues
-
-1. Check environment variables are set correctly
-2. Verify API endpoint is accessible
-3. Check network connectivity
-4. Review browser console for CORS issues
-
-### Authentication Issues
-
-1. Ensure access tokens are valid
-2. Check project code is set correctly
-3. Verify API permissions
-
-### Performance Issues
-
-1. Consider disabling streaming for slower connections
-2. Reduce message history length
-3. Optimize component re-renders
-
-## Contributing
-
-1. Follow the existing code structure
-2. Add tests for new functionality
-3. Update documentation
-4. Ensure TypeScript types are correct
-5. Test with both real API and fallback modes
Index: src/locales/en-US.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import request from './en-US/request'\nimport viewobject from './en-US/viewObject'\nimport actor from './en-US/actor'\nimport common from './en-US/commonlocale'\nimport msg from './en-US/message'\nimport viewobjectSpecification from './en-US/viewObjectSpecification'\nimport createObject from './en-US/createObject'\nimport updateObject from './en-US/updateObject'\nimport viewScreenList from './en-US/viewScreenList'\nimport viewUseCaseDetail from './en-US/viewUseCaseDetail'\nimport viewScreenDetails from './en-US/viewScreenDetails'\nimport viewFunction from './en-US/view-function'\nimport createFunction from './en-US/create-function'\nimport createScreen from './en-US/createScreen'\nimport workFlow from './en-US/viewWorkFlow'\nimport viewStateTransition from './en-US/view-state-transition'\nimport createStateTransition from './en-US/create-state-transition'\nimport mess from './en-US/mess'\nimport mail from './en-US/email'\nimport viewDataMigration from './en-US/view-data-migration'\nimport viewNonFunctional from './en-US/view-non-functional'\nimport createNonFunctional from './en-US/create-non-functional'\nimport userRequirement from './en-US/user-requirement'\nimport viewObjectRelationship from './en-US/view-object-relationship'\nimport meeting from './en-US/meeting'\nimport viewBusinessRule from './en-US/view-business-rule'\nimport viewUsecaseDiagram from './en-US/view-usecase-diagram'\nimport reference_document from './en-US/reference_document'\nimport viewOtherRequirement from './en-US/view-other-requirement'\nimport generateSrs from './en-US/generate-srs'\nimport dashboard from './en-US/dashboard'\nimport projectManagement from './en-US/project-management'\nimport common_committee from './en-US/common_committee'\nimport common_usercase from './en-US/common_usercase'\nimport commonComponent from './en-US/common-component'\nimport commonObject from './en-US/common-object'\nimport commonScreen from './en-US/common-screen'\nimport menu from './en-US/menu'\nimport myAssignedTask from './en-US/my_assigned_task'\nimport validate_srs from './en-US/validate-srs'\nimport select_common_component from './en-US/select-common-component'\nimport myPendingReviewTask from './en-US/my-pending-review-task'\nimport recommend_common_component from './en-US/recommend-common-component'\nimport related_links from './en-US/related-links'\nimport qualityReport from './en-US/quality-report'\nimport common_nonFunctionalRequirement from './en-US/common_non-functional-requirement'\nimport commonmessage from './en-US/common-message'\nimport effortEstimation from './en-US/effort_estimation'\nimport epicManagement from './en-US/epic-management'\nimport sprint_management from './en-US/sprint_management'\nimport user_story from './en-US/user-story'\nimport recommended from './en-US/recommenedcommonrequirement'\nimport glossary from './en-US/glossary'\nimport viewVersionHistory from './en-US/viewVersionHistory'\nexport default {\n  ...viewVersionHistory,\n  ...glossary,\n  ...recommended,\n  ...commonmessage,\n  ...projectManagement,\n  ...request,\n  ...viewobject,\n  ...actor,\n  ...msg,\n  ...common,\n  ...viewobjectSpecification,\n  ...createObject,\n  ...updateObject,\n  ...viewScreenList,\n  ...viewUseCaseDetail,\n  ...viewFunction,\n  ...createFunction,\n  ...createScreen,\n  ...viewScreenDetails,\n  ...workFlow,\n  ...viewStateTransition,\n  ...createStateTransition,\n  ...mess,\n  ...mail,\n  ...viewDataMigration,\n  ...viewNonFunctional,\n  ...createNonFunctional,\n  ...userRequirement,\n  ...viewObjectRelationship,\n  ...meeting,\n  ...viewBusinessRule,\n  ...viewUsecaseDiagram,\n  ...reference_document,\n  ...viewOtherRequirement,\n  ...generateSrs,\n  ...dashboard,\n  ...commonObject,\n  ...common_committee,\n  ...commonComponent,\n  ...commonScreen,\n  ...common_usercase,\n  ...common_nonFunctionalRequirement,\n  ...menu,\n  ...myAssignedTask,\n  ...myPendingReviewTask,\n  ...validate_srs,\n  ...select_common_component,\n  ...recommend_common_component,\n  ...related_links,\n  ...qualityReport,\n  ...effortEstimation,\n  ...epicManagement,\n  ...sprint_management,\n  ...user_story\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/locales/en-US.ts b/src/locales/en-US.ts
--- a/src/locales/en-US.ts	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/locales/en-US.ts	(date 1751346090160)
@@ -52,6 +52,7 @@
 import recommended from './en-US/recommenedcommonrequirement'
 import glossary from './en-US/glossary'
 import viewVersionHistory from './en-US/viewVersionHistory'
+import ai from './en-US/ai'
 export default {
   ...viewVersionHistory,
   ...glossary,
@@ -106,5 +107,6 @@
   ...effortEstimation,
   ...epicManagement,
   ...sprint_management,
-  ...user_story
+  ...user_story,
+  ...ai
 }
Index: src/services/ai-assistant.service.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { apiCall } from '../helper/api/aloApi'\nimport { API_URLS } from '../constants'\nimport { extractProjectCode } from '../helper/share'\nimport {\n  MessageRequest,\n  AgentType,\n  AvaiModel,\n  AIConversation,\n  AIMessage,\n  SendMessageRequest,\n  CreateConversationRequest,\n  TeamRunResponseContentEvent\n} from '../modules/_shared/ai/types'\n// Note: EventStreamService not needed since we're using fetch directly\n\nexport interface AIApiResponse<T = any> {\n  data: T\n  status: number\n  message?: string\n}\n\nexport interface ConversationResponse {\n  status_code: number\n  message: string\n  conversation_id: string\n  title: string\n  created_at: string\n}\n\nexport interface MessageResponse {\n  id: string\n  content: string\n  role: 'user' | 'assistant' | 'system'\n  timestamp: string\n  tokens?: number\n}\n\nexport interface ListConversationsResponse {\n  conversations: ConversationResponse[]\n  total: number\n  limit: number\n  offset: number\n}\n\nexport interface ListMessagesResponse {\n  messages: MessageResponse[]\n  total: number\n  limit: number\n  offset: number\n}\n\nexport interface SendMessageResponse {\n  message: MessageResponse\n  conversation_id: string\n  tokens_used: number\n}\n\nclass AIAssistantService {\n  private getProjectId(): string {\n    return extractProjectCode() || 'default-project'\n  }\n\n  /**\n   * Check AI API health\n   */\n  async checkHealth(): Promise<AIApiResponse> {\n    try {\n      const response = await apiCall('GET', API_URLS.AI_HEALTH)\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Health check failed: ${error.message}`)\n    }\n  }\n\n  /**\n   * Get available agents\n   */\n  async getAgents(): Promise<AIApiResponse<string[]>> {\n    try {\n      const response = await apiCall('GET', API_URLS.AI_AGENTS)\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to fetch agents: ${error.message}`)\n    }\n  }\n\n  /**\n   * List all conversations\n   */\n  async listConversations(limit: number = 50, offset: number = 0): Promise<AIApiResponse<ListConversationsResponse>> {\n    try {\n      const params = { limit, offset }\n      const response = await apiCall('GET', API_URLS.AI_CONVERSATIONS, params)\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to fetch conversations: ${error.message}`)\n    }\n  }\n\n  /**\n   * Create a new conversation\n   */\n  async createConversation(request: CreateConversationRequest): Promise<AIApiResponse<ConversationResponse>> {\n    try {\n      const requestBody = {\n        project_id: request.projectId\n      }\n\n      const response = await apiCall('POST', API_URLS.AI_CONVERSATIONS, requestBody)\n\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to create conversation: ${error.message}`)\n    }\n  }\n\n  /**\n   * Delete a conversation\n   */\n  async deleteConversation(conversationId: string): Promise<AIApiResponse> {\n    try {\n      const response = await apiCall('DELETE', API_URLS.AI_DELETE_CONVERSATION(conversationId))\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to delete conversation: ${error.message}`)\n    }\n  }\n\n  /**\n   * List messages in a conversation\n   */\n  async listMessages(\n    conversationId: string,\n    limit: number = 100,\n    offset: number = 0\n  ): Promise<AIApiResponse<ListMessagesResponse>> {\n    try {\n      const params = { limit, offset }\n      const response = await apiCall('GET', API_URLS.AI_CONVERSATION_MESSAGES(conversationId), params)\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to fetch messages: ${error.message}`)\n    }\n  }\n\n  /**\n   * Send a message to the AI assistant\n   */\n  async sendMessage(request: SendMessageRequest): Promise<AIApiResponse<any>> {\n    try {\n      const messageRequest: MessageRequest = {\n        message: request.content,\n        stream: request.stream ?? true,\n        model: request.model ?? 'gpt-4.1',\n        agent_type: request.agentType,\n        project_id: request.projectId\n      }\n\n      const response = await apiCall(\n        'POST',\n        API_URLS.AI_CONVERSATION_MESSAGES(request.conversationId),\n        messageRequest\n      )\n\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to send message: ${error.message}`)\n    }\n  }\n\n  /**\n   * Send a streaming message to the AI assistant using Server-Sent Events\n   * The POST /v1/conversations/:id/messages endpoint returns an event stream directly\n   */\n  async sendStreamingMessage(\n    request: SendMessageRequest,\n    onChunk: (event: TeamRunResponseContentEvent) => void,\n    onComplete: () => void,\n    onError: (error: Error) => void\n  ): Promise<void> {\n    try {\n      const messageRequest: MessageRequest = {\n        message: request.content,\n        stream: true,\n        model: request.model ?? 'gpt-4.1',\n        agent_type: request.agentType,\n        project_id: request.projectId\n      }\n\n      // Create EventSource directly to the messages endpoint\n      const url = API_URLS.AI_CONVERSATION_MESSAGES(request.conversationId)\n\n      // We need to make a streaming request to the same endpoint\n      this.createStreamingRequest(url, messageRequest, onChunk, onComplete, onError)\n\n    } catch (error: any) {\n      onError(new Error(`Streaming failed: ${error.message}`))\n    }\n  }\n\n  /**\n   * Create a streaming request using fetch with ReadableStream\n   */\n  private async createStreamingRequest(\n    url: string,\n    messageRequest: MessageRequest,\n    onChunk: (event: TeamRunResponseContentEvent) => void,\n    onComplete: () => void,\n    onError: (error: Error) => void\n  ): Promise<void> {\n    try {\n      // Get auth token and project code for headers\n      const accessToken = localStorage.getItem('accessToken')\n      const projectCode = extractProjectCode()\n\n      const response = await fetch(url, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'ProjectCode': projectCode || '',\n          'Authentication': `Bearer ${accessToken}`,\n          'Authorization': `Bearer ${accessToken}`,\n          'Accept': 'text/event-stream',\n          'Cache-Control': 'no-cache'\n        },\n        body: JSON.stringify(messageRequest)\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`)\n      }\n\n      if (!response.body) {\n        throw new Error('No response body for streaming')\n      }\n\n      const reader = response.body.getReader()\n      const decoder = new TextDecoder()\n\n      try {\n        while (true) {\n          const { done, value } = await reader.read()\n\n          if (done) {\n            onComplete()\n            break\n          }\n\n          // Decode the chunk\n          const chunk = decoder.decode(value, { stream: true })\n\n          // Parse event stream data\n          this.parseEventStreamChunk(chunk, onChunk)\n        }\n      } finally {\n        reader.releaseLock()\n      }\n\n    } catch (error: any) {\n      onError(new Error(`Streaming request failed: ${error.message}`))\n    }\n  }\n\n  /**\n   * Parse event stream chunk and extract TeamRunResponseContent events\n   */\n  private parseEventStreamChunk(\n    chunk: string,\n    onChunk: (event: TeamRunResponseContentEvent) => void\n  ): void {\n    const lines = chunk.split('\\n')\n\n    for (const line of lines) {\n      if (line.startsWith('data: ')) {\n        try {\n          const jsonData = line.substring(6) // Remove 'data: ' prefix\n          const eventData = JSON.parse(jsonData) as TeamRunResponseContentEvent\n\n          // Only handle TeamRunResponseContent events\n          if (eventData.type === 'agent' && eventData.event === 'TeamRunResponseContent') {\n            onChunk(eventData)\n          }\n        } catch (parseError) {\n          console.warn('Failed to parse event line:', line, parseError)\n        }\n      }\n    }\n  }\n\n  /**\n   * Stop any active streaming connection\n   * Note: With fetch streams, the connection is managed by the reader\n   */\n  stopStreaming(): void {\n    // The streaming connection is managed by the fetch reader\n    // and will be automatically closed when the component unmounts\n    // or when the reader is released\n    console.log('Streaming stopped')\n  }\n\n  /**\n   * Legacy streaming method for backward compatibility\n   */\n  async sendStreamingMessageLegacy(\n    request: SendMessageRequest,\n    onChunk: (chunk: string) => void,\n    onComplete: (response: SendMessageResponse) => void,\n    onError: (error: Error) => void\n  ): Promise<void> {\n    try {\n      // For now, simulate streaming with a regular API call\n      // In a real implementation, this would use Server-Sent Events or WebSocket\n      const response = await this.sendMessage(request)\n\n      // Simulate streaming by sending chunks\n      const content = response.data.message.content\n      const chunks = content.split(' ')\n\n      for (let i = 0; i < chunks.length; i++) {\n        setTimeout(() => {\n          onChunk(chunks[i] + ' ')\n          if (i === chunks.length - 1) {\n            onComplete(response.data)\n          }\n        }, i * 100) // 100ms delay between chunks\n      }\n    } catch (error: any) {\n      onError(new Error(`Streaming failed: ${error.message}`))\n    }\n  }\n\n  /**\n   * Transform API conversation response to internal format\n   */\n  transformConversation(apiConversation: ConversationResponse): AIConversation {\n    return {\n      id: apiConversation.conversation_id,\n      title: apiConversation.title,\n      messages: [], // Messages will be loaded separately\n      totalTokens: 0,\n      createdAt: new Date(apiConversation.created_at),\n      updatedAt: new Date(apiConversation.created_at),\n      projectId: this.getProjectId()\n    }\n  }\n\n  /**\n   * Transform API message response to internal format\n   */\n  transformMessage(apiMessage: MessageResponse): AIMessage {\n    return {\n      id: apiMessage.id,\n      content: apiMessage.content,\n      type: apiMessage.role as 'user' | 'assistant' | 'system',\n      timestamp: new Date(apiMessage.timestamp),\n      tokens: apiMessage.tokens\n    }\n  }\n}\n\nexport default new AIAssistantService()\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/services/ai-assistant.service.ts b/src/services/ai-assistant.service.ts
--- a/src/services/ai-assistant.service.ts	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/services/ai-assistant.service.ts	(date 1751346090162)
@@ -11,6 +11,7 @@
   CreateConversationRequest,
   TeamRunResponseContentEvent
 } from '../modules/_shared/ai/types'
+import { EventStreamContentType, fetchEventSource } from '@microsoft/fetch-event-source'
 // Note: EventStreamService not needed since we're using fetch directly
 
 export interface AIApiResponse<T = any> {
@@ -228,13 +229,14 @@
     onChunk: (event: TeamRunResponseContentEvent) => void,
     onComplete: () => void,
     onError: (error: Error) => void
-  ): Promise<void> {
+  ): Promise<AbortController> {
+    const controller = new AbortController();
     try {
       // Get auth token and project code for headers
       const accessToken = localStorage.getItem('accessToken')
       const projectCode = extractProjectCode()
 
-      const response = await fetch(url, {
+      fetchEventSource(url, {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json',
@@ -244,42 +246,18 @@
           'Accept': 'text/event-stream',
           'Cache-Control': 'no-cache'
         },
-        body: JSON.stringify(messageRequest)
+        body: JSON.stringify(messageRequest),
+        signal: controller.signal,
+        onmessage: (event) => {
+          onChunk(JSON.parse(event.data) as TeamRunResponseContentEvent)
+        },
+        onclose: onComplete,
+        onerror: onError,
       })
-
-      if (!response.ok) {
-        throw new Error(`HTTP error! status: ${response.status}`)
-      }
-
-      if (!response.body) {
-        throw new Error('No response body for streaming')
-      }
-
-      const reader = response.body.getReader()
-      const decoder = new TextDecoder()
-
-      try {
-        while (true) {
-          const { done, value } = await reader.read()
-
-          if (done) {
-            onComplete()
-            break
-          }
-
-          // Decode the chunk
-          const chunk = decoder.decode(value, { stream: true })
-
-          // Parse event stream data
-          this.parseEventStreamChunk(chunk, onChunk)
-        }
-      } finally {
-        reader.releaseLock()
-      }
-
     } catch (error: any) {
       onError(new Error(`Streaming request failed: ${error.message}`))
     }
+    return controller;
   }
 
   /**
@@ -358,7 +336,9 @@
       id: apiConversation.conversation_id,
       title: apiConversation.title,
       messages: [], // Messages will be loaded separately
-      totalTokens: 0,
+      receiveToken: 0,
+      sentToken: 0,
+      totalCost: 0,
       createdAt: new Date(apiConversation.created_at),
       updatedAt: new Date(apiConversation.created_at),
       projectId: this.getProjectId()
Index: src/modules/_shared/ai/components/chatbox.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import React, { useEffect, useState, useRef } from 'react'\nimport { useDispatch, useSelector } from 'react-redux'\nimport {\n  Card,\n  Input,\n  Button,\n  Typography,\n  Space,\n  Progress,\n  Avatar,\n  Tooltip,\n  Dropdown,\n  Tag,\n  Spin,\n  Modal\n} from 'antd'\nimport {\n  RobotOutlined,\n  SendOutlined,\n  PaperClipOutlined,\n  MoreOutlined,\n  CloseOutlined,\n  UserOutlined,\n  ThunderboltOutlined,\n  FileTextOutlined,\n  PlusOutlined,\n  HistoryOutlined,\n  DeleteOutlined\n} from '@ant-design/icons'\nimport moment from 'moment'\nimport { AIAssistantState, AIMessage, DEFAULT_PROMPTS, AgentType } from '../types'\nimport {\n  sendMessageRequest,\n  fetchConversationsRequest,\n  createConversationRequest,\n  setCurrentConversation,\n  clearSuggestions,\n  deleteConversationRequest\n} from '../actions'\nimport AppState from '../../../../store/types'\nimport { extractProjectCode } from '../../../../helper/share'\nimport { MessageContent } from './message-content'\nimport './styles.less'\n\nconst { Text, Title } = Typography\nconst { TextArea } = Input\n\ninterface AIAssistantProps {\n  isVisible: boolean\n  onClose: () => void\n  hideHeader?: boolean\n}\n\nexport const AIChatBox: React.FC<AIAssistantProps> = ({ isVisible, onClose, hideHeader = false }) => {\n  const dispatch = useDispatch()\n  const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState\n  const [messageInput, setMessageInput] = useState('')\n  const [selectedPrompt, setSelectedPrompt] = useState<string>('')\n  const [showConversationModal, setShowConversationModal] = useState(false)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n\n  // Load AI settings from localStorage\n  const getAISettings = () => {\n    try {\n      const saved = localStorage.getItem('ai-assistant-settings')\n      return saved ? JSON.parse(saved) : {\n        agentType: 'master_agent' as AgentType,\n        model: 'gpt-4.1' as const,\n        stream: true\n      }\n    } catch {\n      return {\n        agentType: 'master_agent' as AgentType,\n        model: 'gpt-4.1' as const,\n        stream: true\n      }\n    }\n  }\n\n  useEffect(() => {\n    if (isVisible) {\n      dispatch(fetchConversationsRequest())\n    }\n  }, [isVisible, dispatch])\n\n  useEffect(() => {\n    // Create default conversation if none exists and we're visible\n    if (isVisible && aiState.conversations.length === 0 && !aiState.isLoading) {\n      dispatch(createConversationRequest({\n        title: 'New Chat',\n        projectId: extractProjectCode() || 'default-project'\n      }))\n    }\n  }, [isVisible, aiState.conversations.length, aiState.isLoading, dispatch])\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [aiState.currentConversation?.messages])\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  const handleSendMessage = () => {\n    if (messageInput.trim() && aiState.currentConversation?.id) {\n      const content = selectedPrompt\n        ? `${selectedPrompt}\\n\\n${messageInput.trim()}`\n        : messageInput.trim()\n\n      const settings = getAISettings()\n\n      dispatch(sendMessageRequest({\n        content,\n        conversationId: aiState.currentConversation.id,\n        agentType: settings.agentType,\n        model: settings.model,\n        projectId: extractProjectCode() || 'default-project',\n        stream: settings.stream\n      }))\n      setMessageInput('')\n      setSelectedPrompt('')\n      dispatch(clearSuggestions())\n    }\n  }\n\n  const handlePromptSelect = (prompt: string) => {\n    setSelectedPrompt(prompt)\n    setMessageInput('')\n  }\n\n  const handleNewConversation = () => {\n    dispatch(createConversationRequest({\n      title: 'New Chat',\n      projectId: extractProjectCode() || 'default-project'\n    }))\n  }\n\n  const handleSelectConversation = (conversationId: string) => {\n    dispatch(setCurrentConversation(conversationId))\n    setShowConversationModal(false)\n  }\n\n  const handleDeleteConversation = (conversationId: string) => {\n    dispatch(deleteConversationRequest(conversationId))\n  }\n\n  const formatMessageTime = (timestamp: Date) => {\n    const now = moment()\n    const messageTime = moment(timestamp)\n\n    if (now.diff(messageTime, 'minutes') < 1) {\n      return 'now'\n    } else if (now.diff(messageTime, 'hours') < 1) {\n      return `${now.diff(messageTime, 'minutes')}m ago`\n    } else if (now.diff(messageTime, 'days') === 0) {\n      return messageTime.format('HH:mm')\n    } else {\n      return messageTime.format('DD/MM HH:mm')\n    }\n  }\n\n  const renderMessage = (message: AIMessage) => {\n    const isUser = message.type === 'user'\n\n    return (\n      <div\n        key={message.id}\n        className={`ai-message ${isUser ? 'user-message' : 'assistant-message'}`}\n      >\n        <div className=\"message-header\">\n          <Avatar\n            size=\"small\"\n            icon={isUser ? <UserOutlined /> : <RobotOutlined />}\n            className={isUser ? 'user-avatar' : 'ai-avatar'}\n          />\n          <div className=\"message-meta\">\n            {!isUser && message.agentName && (\n              <Text strong className=\"agent-name\">\n                {message.agentName}\n              </Text>\n            )}\n            {!isUser && message.teamName && message.teamName !== message.agentName && (\n              <Text type=\"secondary\" className=\"team-name\">\n                ({message.teamName})\n              </Text>\n            )}\n            <Text type=\"secondary\" className=\"message-time\">\n              {formatMessageTime(message.timestamp)}\n            </Text>\n          </div>\n          {message.tokens && (\n            <Tag color=\"blue\">\n              {message.tokens} tokens\n            </Tag>\n          )}\n        </div>\n        <div className=\"message-content\">\n          <MessageContent content={message.content} />\n        </div>\n      </div>\n    )\n  }\n\n  const tokenUsagePercentage = (aiState.totalTokensUsed / aiState.availableTokens) * 100\n\n  if (!isVisible) return null\n\n  return (\n    <div className=\"ai-assistant-container\">\n      <Card className=\"ai-assistant-card\" bodyStyle={{ padding: 0 }}>\n        {/* Header - only show when not in tabbed interface */}\n        {!hideHeader && (\n          <div className=\"ai-assistant-header\">\n            <div className=\"header-left\">\n              <RobotOutlined className=\"ai-icon\" />\n              <Title level={5} style={{ margin: 0, color: 'white' }}>\n                BA Vista\n              </Title>\n            </div>\n            <div className=\"header-right\">\n              <Tooltip title=\"Settings\">\n                <Button\n                  type=\"text\"\n                  icon={<MoreOutlined />}\n                  style={{ color: 'white' }}\n                />\n              </Tooltip>\n              <Tooltip title=\"Close\">\n                <Button\n                  type=\"text\"\n                  icon={<CloseOutlined />}\n                  onClick={onClose}\n                  style={{ color: 'white' }}\n                />\n              </Tooltip>\n            </div>\n          </div>\n        )}\n\n        {/* Current Task */}\n        <div className=\"current-task\">\n          <Title level={5}>{aiState.currentConversation?.title ?? \"New Chat\"}</Title>\n          <div className=\"task-actions\">\n            <Tooltip title=\"New Conversation\" placement=\"topRight\">\n              <Button\n                type=\"text\"\n                icon={<PlusOutlined />}\n                size=\"small\"\n                onClick={handleNewConversation}\n              />\n            </Tooltip>\n            <Tooltip title=\"Conversation History\" placement=\"topRight\">\n              <Button\n                type=\"text\"\n                icon={<HistoryOutlined />}\n                size=\"small\"\n                onClick={() => setShowConversationModal(true)}\n              />\n            </Tooltip>\n          </div>\n        </div>\n\n\n\n        {/* Token Usage */}\n        <div className=\"token-usage\">\n          <div className=\"token-info\">\n            <Text strong>Token:</Text>\n            <Space>\n              <ThunderboltOutlined style={{ color: '#52c41a' }} />\n              <Text>{aiState.totalTokensUsed}</Text>\n              <ThunderboltOutlined style={{ color: '#ff4d4f' }} />\n              <Text>{aiState.availableTokens - aiState.totalTokensUsed}</Text>\n            </Space>\n            <Text strong>{aiState.availableTokens - aiState.totalTokensUsed} USD</Text>\n          </div>\n          <Progress\n            percent={tokenUsagePercentage}\n            size=\"small\"\n            strokeColor={tokenUsagePercentage > 80 ? '#ff4d4f' : '#1890ff'}\n            showInfo={false}\n          />\n        </div>\n\n\n\n        {/* Messages */}\n        <div className=\"ai-messages\">\n          {aiState.currentConversation?.messages.length === 0 && !aiState.isTyping ? (\n            <div className=\"empty-conversation\">\n              <div className=\"empty-conversation-content\">\n                <RobotOutlined className=\"empty-icon\" />\n                <Title level={4}>Welcome to BA Vista AI Assistant</Title>\n                <Text type=\"secondary\">\n                  I'm here to help you analyze requirements, generate documentation, and improve your business analysis workflow.\n                </Text>\n                <div className=\"suggested-prompts\">\n                  <Text strong>Try asking me to:</Text>\n                  <ul>\n                    <li>Analyze this requirement for completeness</li>\n                    <li>Generate use case scenarios</li>\n                    <li>Review this document for clarity</li>\n                    <li>Create test cases for this feature</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          ) : (\n            <>\n              {aiState.currentConversation?.messages.map(renderMessage)}\n              {aiState.isTyping && (\n                <div className=\"ai-message assistant-message\">\n                  <div className=\"message-header\">\n                    <Avatar size=\"small\" icon={<RobotOutlined />} className=\"ai-avatar\" />\n                    <Text type=\"secondary\">AI is thinking...</Text>\n                  </div>\n                  <div className=\"message-content\">\n                    <Spin size=\"small\" />\n                  </div>\n                </div>\n              )}\n            </>\n          )}\n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Quick Actions */}\n        {aiState.suggestions.length > 0 && (\n          <div className=\"quick-actions\">\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>Suggestions:</Text>\n            <Space wrap>\n              {aiState.suggestions.map((suggestion, index) => (\n                <Button\n                  key={index}\n                  size=\"small\"\n                  type=\"dashed\"\n                  onClick={() => setMessageInput(suggestion)}\n                >\n                  {suggestion}\n                </Button>\n              ))}\n            </Space>\n          </div>\n        )}\n\n        {/* Input Area */}\n        <div className=\"ai-input-area\">\n          {selectedPrompt && (\n            <div className=\"selected-prompt\">\n              <Tag color=\"blue\" closable onClose={() => setSelectedPrompt('')}>\n                <FileTextOutlined /> {DEFAULT_PROMPTS.find(p => p.prompt === selectedPrompt)?.title || 'Custom Prompt'}\n              </Tag>\n            </div>\n          )}\n\n          <div className=\"input-container\">\n            <TextArea\n              value={messageInput}\n              onChange={(e) => setMessageInput(e.target.value)}\n              placeholder={selectedPrompt ? \"Add your specific requirements...\" : \"Input a prompt, or type @ to call reference doc/ artefacts\"}\n              autoSize={{ minRows: 1, maxRows: 4 }}\n              onPressEnter={(e) => {\n                if (!e.shiftKey) {\n                  e.preventDefault()\n                  handleSendMessage()\n                }\n              }}\n              className=\"message-input\"\n            />\n            <div className=\"input-actions\">\n              <Dropdown\n                menu={{\n                  items: DEFAULT_PROMPTS.map(prompt => ({\n                    key: prompt.prompt,\n                    label: (\n                      <div>\n                        <Text strong>{prompt.title}</Text>\n                        <br />\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          {prompt.description} ({prompt.tokens} tokens)\n                        </Text>\n                      </div>\n                    )\n                  })),\n                  onClick: ({ key }) => handlePromptSelect(key as string)\n                }}\n                trigger={['click']}\n              >\n                <Button\n                  type=\"text\"\n                  icon={<FileTextOutlined />}\n                  size=\"small\"\n                />\n              </Dropdown>\n              <Button\n                type=\"text\"\n                icon={<PaperClipOutlined />}\n                size=\"small\"\n              />\n              <Button\n                type=\"primary\"\n                icon={<SendOutlined />}\n                onClick={handleSendMessage}\n                disabled={!messageInput.trim() && !selectedPrompt}\n                size=\"small\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Conversation History Modal */}\n        <Modal\n          title=\"Conversation History\"\n          open={showConversationModal}\n          onCancel={() => setShowConversationModal(false)}\n          footer={null}\n          width={600}\n          className=\"conversation-history-modal\"\n        >\n          <div className=\"conversation-modal-content\">\n            {aiState.conversations.length === 0 ? (\n              <div className=\"empty-conversations\">\n                <Text type=\"secondary\">No conversations found</Text>\n              </div>\n            ) : (\n              <div className=\"conversation-items\">\n                {aiState.conversations.map((conversation) => (\n                  <div\n                    key={conversation.id}\n                    className={`conversation-item ${conversation.id === aiState.currentConversation?.id ? 'active' : ''}`}\n                    onClick={() => handleSelectConversation(conversation.id)}\n                  >\n                    <div className=\"conversation-info\">\n                      <Text className=\"conversation-title\">{conversation.title}</Text>\n                      <Text type=\"secondary\" className=\"conversation-time\">\n                        {moment(conversation.updatedAt).format('DD/MM HH:mm')}\n                      </Text>\n                    </div>\n                    <Button\n                      type=\"text\"\n                      icon={<DeleteOutlined />}\n                      size=\"small\"\n                      onClick={(e) => {\n                        e.stopPropagation()\n                        handleDeleteConversation(conversation.id)\n                      }}\n                      className=\"delete-conversation-btn\"\n                      danger\n                    />\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </Modal>\n      </Card>\n    </div>\n  )\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/components/chatbox.tsx b/src/modules/_shared/ai/components/chatbox.tsx
--- a/src/modules/_shared/ai/components/chatbox.tsx	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/modules/_shared/ai/components/chatbox.tsx	(date 1751346090161)
@@ -21,11 +21,12 @@
   MoreOutlined,
   CloseOutlined,
   UserOutlined,
-  ThunderboltOutlined,
   FileTextOutlined,
   PlusOutlined,
   HistoryOutlined,
-  DeleteOutlined
+  DeleteOutlined,
+  ArrowUpOutlined,
+  ArrowDownOutlined
 } from '@ant-design/icons'
 import moment from 'moment'
 import { AIAssistantState, AIMessage, DEFAULT_PROMPTS, AgentType } from '../types'
@@ -41,6 +42,7 @@
 import { extractProjectCode } from '../../../../helper/share'
 import { MessageContent } from './message-content'
 import './styles.less'
+import intl from '../../../../config/locale.config'
 
 const { Text, Title } = Typography
 const { TextArea } = Input
@@ -161,6 +163,7 @@
 
   const renderMessage = (message: AIMessage) => {
     const isUser = message.type === 'user'
+    if (!message.content) return
 
     return (
       <div
@@ -174,16 +177,19 @@
             className={isUser ? 'user-avatar' : 'ai-avatar'}
           />
           <div className="message-meta">
-            {!isUser && message.agentName && (
-              <Text strong className="agent-name">
-                {message.agentName}
-              </Text>
-            )}
-            {!isUser && message.teamName && message.teamName !== message.agentName && (
-              <Text type="secondary" className="team-name">
-                ({message.teamName})
-              </Text>
+            {!isUser && (
+              <>
+                <Text strong className="agent-name">
+                  {intl.formatMessage({ id: 'ai.agent.name' })}
+                </Text>
+                {message.teamName && (
+                  <Text type="secondary" className="team-name">
+                    ({message.teamName})
+                  </Text>
+                )}
+              </>
             )}
+
             <Text type="secondary" className="message-time">
               {formatMessageTime(message.timestamp)}
             </Text>
@@ -201,8 +207,6 @@
     )
   }
 
-  const tokenUsagePercentage = (aiState.totalTokensUsed / aiState.availableTokens) * 100
-
   if (!isVisible) return null
 
   return (
@@ -214,7 +218,7 @@
             <div className="header-left">
               <RobotOutlined className="ai-icon" />
               <Title level={5} style={{ margin: 0, color: 'white' }}>
-                BA Vista
+                {intl.formatMessage({ id: 'ai.agent.name' })}
               </Title>
             </div>
             <div className="header-right">
@@ -267,19 +271,17 @@
           <div className="token-info">
             <Text strong>Token:</Text>
             <Space>
-              <ThunderboltOutlined style={{ color: '#52c41a' }} />
-              <Text>{aiState.totalTokensUsed}</Text>
-              <ThunderboltOutlined style={{ color: '#ff4d4f' }} />
-              <Text>{aiState.availableTokens - aiState.totalTokensUsed}</Text>
+              <Tooltip title={intl.formatMessage({ id: 'ai.send-token.description' })}>
+                <ArrowUpOutlined />
+                <Text>{aiState.currentConversation?.sentToken}</Text>
+              </Tooltip>
+              <Tooltip title={intl.formatMessage({ id: 'ai.receive-token.description' })}>
+                <ArrowDownOutlined />
+                <Text>{aiState.currentConversation?.receiveToken}</Text>
+              </Tooltip>
             </Space>
-            <Text strong>{aiState.availableTokens - aiState.totalTokensUsed} USD</Text>
+            <Text strong>{aiState.currentConversation?.totalCost} USD</Text>
           </div>
-          <Progress
-            percent={tokenUsagePercentage}
-            size="small"
-            strokeColor={tokenUsagePercentage > 80 ? '#ff4d4f' : '#1890ff'}
-            showInfo={false}
-          />
         </div>
 
 
@@ -290,19 +292,10 @@
             <div className="empty-conversation">
               <div className="empty-conversation-content">
                 <RobotOutlined className="empty-icon" />
-                <Title level={4}>Welcome to BA Vista AI Assistant</Title>
+                <Title level={4}>{intl.formatMessage({ id: 'ai.welcome-message.title' })}</Title>
                 <Text type="secondary">
-                  I'm here to help you analyze requirements, generate documentation, and improve your business analysis workflow.
+                  {intl.formatMessage({ id: 'ai.welcome-message.description' })}
                 </Text>
-                <div className="suggested-prompts">
-                  <Text strong>Try asking me to:</Text>
-                  <ul>
-                    <li>Analyze this requirement for completeness</li>
-                    <li>Generate use case scenarios</li>
-                    <li>Review this document for clarity</li>
-                    <li>Create test cases for this feature</li>
-                  </ul>
-                </div>
               </div>
             </div>
           ) : (
@@ -368,30 +361,6 @@
               className="message-input"
             />
             <div className="input-actions">
-              <Dropdown
-                menu={{
-                  items: DEFAULT_PROMPTS.map(prompt => ({
-                    key: prompt.prompt,
-                    label: (
-                      <div>
-                        <Text strong>{prompt.title}</Text>
-                        <br />
-                        <Text type="secondary" style={{ fontSize: '12px' }}>
-                          {prompt.description} ({prompt.tokens} tokens)
-                        </Text>
-                      </div>
-                    )
-                  })),
-                  onClick: ({ key }) => handlePromptSelect(key as string)
-                }}
-                trigger={['click']}
-              >
-                <Button
-                  type="text"
-                  icon={<FileTextOutlined />}
-                  size="small"
-                />
-              </Dropdown>
               <Button
                 type="text"
                 icon={<PaperClipOutlined />}
Index: src/modules/_shared/ai/reducer.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { createReducer } from '@reduxjs/toolkit'\nimport { AIAssistantState } from './types'\nimport {\n  setCurrentConversation,\n  fetchConversationsRequest,\n  fetchConversationsSuccess,\n  fetchConversationsFailure,\n  createConversationRequest,\n  createConversationSuccess,\n  createConversationFailure,\n  deleteConversationRequest,\n  deleteConversationSuccess,\n  deleteConversationFailure,\n  sendMessageRequest,\n  sendMessageSuccess,\n  sendMessageFailure,\n  receiveAIResponse,\n  startStreamingResponse,\n  receiveStreamingChunk,\n  endStreamingResponse,\n  streamingError,\n  updateTokenUsage,\n  fetchTokenUsageRequest,\n  fetchTokenUsageSuccess,\n  fetchTokenUsageFailure,\n  setAITyping,\n  setSuggestions,\n  clearSuggestions,\n  clearAIError,\n  toggleAIChatPanel,\n  toggleAISetingsPanel,\n  setActiveTab,\n  closeAllPanels\n} from './actions'\n\nconst initialState: AIAssistantState = {\n  isOpen: false,\n  isLoading: false,\n  currentConversation: undefined,\n  conversations: [],\n  totalTokensUsed: 0,\n  availableTokens: 1000, // Default token limit\n  error: undefined,\n  isTyping: false,\n  suggestions: [],\n  // UI Panel States\n  isAiPanelOpen: false,\n  activeTab: null\n}\n\nconst handleTogglePanel = (tab: AIAssistantState[\"activeTab\"]) => (state: AIAssistantState) => {\n  const wasOpen = state.isAiPanelOpen\n  state.isAiPanelOpen = !state.isAiPanelOpen\n  state.activeTab = tab\n}\n\nexport const aiAssistantReducer = createReducer(initialState, (builder) => {\n  builder\n    // UI Actions\n    .addCase(setCurrentConversation, (state, action) => {\n      const conversation = state.conversations.find(c => c.id === action.payload)\n      if (conversation) {\n        state.currentConversation = conversation\n      }\n    })\n\n    // Conversation Actions\n    .addCase(fetchConversationsRequest, (state) => {\n      state.isLoading = true\n      state.error = undefined\n    })\n    .addCase(fetchConversationsSuccess, (state, action) => {\n      state.isLoading = false\n      state.conversations = action.payload\n    })\n    .addCase(fetchConversationsFailure, (state, action) => {\n      state.isLoading = false\n      state.error = action.payload\n    })\n\n    .addCase(createConversationRequest, (state) => {\n      state.isLoading = true\n      state.error = undefined\n    })\n    .addCase(createConversationSuccess, (state, action) => {\n      state.isLoading = false\n      state.conversations.unshift(action.payload)\n      state.currentConversation = action.payload\n    })\n    .addCase(createConversationFailure, (state, action) => {\n      state.isLoading = false\n      state.error = action.payload\n    })\n\n    .addCase(deleteConversationSuccess, (state, action) => {\n      state.conversations = state.conversations.filter(c => c.id !== action.payload)\n      if (state.currentConversation?.id === action.payload) {\n        state.currentConversation = state.conversations[0] || undefined\n      }\n    })\n\n    // Message Actions\n    .addCase(sendMessageRequest, (state) => {\n      state.isLoading = true\n      state.error = undefined\n    })\n    .addCase(sendMessageSuccess, (state, action) => {\n      state.isLoading = false\n      if (state.currentConversation) {\n        state.currentConversation.messages.push(action.payload)\n        state.currentConversation.updatedAt = action.payload.timestamp\n\n        // Update conversation in the list\n        const conversationIndex = state.conversations.findIndex(\n          c => c.id === state.currentConversation?.id\n        )\n        if (conversationIndex !== -1) {\n          state.conversations[conversationIndex] = state.currentConversation\n        }\n      }\n    })\n    .addCase(sendMessageFailure, (state, action) => {\n      state.isLoading = false\n      state.error = action.payload\n    })\n\n    .addCase(receiveAIResponse, (state, action) => {\n      state.isTyping = false\n      if (state.currentConversation) {\n        const aiMessage = {\n          id: Date.now().toString(),\n          content: action.payload.content,\n          type: 'assistant' as const,\n          timestamp: new Date(),\n          tokens: action.payload.tokens\n        }\n\n        state.currentConversation.messages.push(aiMessage)\n        state.currentConversation.totalTokens += action.payload.tokens\n        state.currentConversation.updatedAt = aiMessage.timestamp\n        state.totalTokensUsed += action.payload.tokens\n\n        // Update suggestions if provided\n        if (action.payload.suggestions) {\n          state.suggestions = action.payload.suggestions\n        }\n\n        // Update conversation in the list\n        const conversationIndex = state.conversations.findIndex(\n          c => c.id === state.currentConversation?.id\n        )\n        if (conversationIndex !== -1) {\n          state.conversations[conversationIndex] = state.currentConversation\n        }\n      }\n    })\n\n    // Streaming Actions\n    .addCase(startStreamingResponse, (state, action) => {\n      state.isTyping = true\n      if (state.currentConversation) {\n        const streamingMessage = {\n          id: action.payload,\n          content: '',\n          type: 'assistant' as const,\n          timestamp: new Date(),\n          isLoading: true\n        }\n        state.currentConversation.messages.push(streamingMessage)\n      }\n    })\n    .addCase(receiveStreamingChunk, (state, action) => {\n      if (state.currentConversation) {\n        const messageIndex = state.currentConversation.messages.findIndex(\n          m => m.id === action.payload.messageId\n        )\n        if (messageIndex !== -1) {\n          const message = state.currentConversation.messages[messageIndex]\n          message.content += action.payload.chunk\n\n          // Update agent information if provided\n          if (action.payload.agentName) {\n            message.agentName = action.payload.agentName\n          }\n          if (action.payload.runId) {\n            message.runId = action.payload.runId\n          }\n          if (action.payload.teamId) {\n            message.teamId = action.payload.teamId\n          }\n          if (action.payload.teamName) {\n            message.teamName = action.payload.teamName\n          }\n        }\n      }\n    })\n    .addCase(endStreamingResponse, (state, action) => {\n      state.isTyping = false\n      if (state.currentConversation) {\n        const messageIndex = state.currentConversation.messages.findIndex(\n          m => m.id === action.payload.messageId\n        )\n        if (messageIndex !== -1) {\n          state.currentConversation.messages[messageIndex].isLoading = false\n          state.currentConversation.messages[messageIndex].tokens = action.payload.totalTokens\n          state.currentConversation.totalTokens += action.payload.totalTokens\n          state.totalTokensUsed += action.payload.totalTokens\n          state.currentConversation.updatedAt = new Date()\n        }\n      }\n    })\n    .addCase(streamingError, (state, action) => {\n      state.isTyping = false\n      state.error = action.payload.error\n      if (state.currentConversation) {\n        const messageIndex = state.currentConversation.messages.findIndex(\n          m => m.id === action.payload.messageId\n        )\n        if (messageIndex !== -1) {\n          state.currentConversation.messages[messageIndex].isLoading = false\n          state.currentConversation.messages[messageIndex].content = 'Error: Failed to receive response'\n        }\n      }\n    })\n\n    // Token Actions\n    .addCase(updateTokenUsage, (state, action) => {\n      state.totalTokensUsed = action.payload.used\n      state.availableTokens = action.payload.available\n    })\n    .addCase(fetchTokenUsageSuccess, (state, action) => {\n      state.totalTokensUsed = action.payload.used\n      state.availableTokens = action.payload.available\n    })\n\n    // Typing Actions\n    .addCase(setAITyping, (state, action) => {\n      state.isTyping = action.payload\n    })\n\n    // Suggestions Actions\n    .addCase(setSuggestions, (state, action) => {\n      state.suggestions = action.payload\n    })\n    .addCase(clearSuggestions, (state) => {\n      state.suggestions = []\n    })\n\n    // Error Actions\n    .addCase(clearAIError, (state) => {\n      state.error = undefined\n    })\n    .addCase(toggleAIChatPanel, handleTogglePanel('ai-chat'))\n    .addCase(toggleAISetingsPanel, handleTogglePanel('settings'))\n    .addCase(setActiveTab, (state, action) => {\n      state.activeTab = action.payload\n    })\n    .addCase(closeAllPanels, (state) => {\n      state.isAiPanelOpen = false\n      state.activeTab = null\n    })\n})\n\nexport { initialState as AIState }\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/reducer.ts b/src/modules/_shared/ai/reducer.ts
--- a/src/modules/_shared/ai/reducer.ts	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/modules/_shared/ai/reducer.ts	(date 1751346090161)
@@ -1,5 +1,5 @@
 import { createReducer } from '@reduxjs/toolkit'
-import { AIAssistantState } from './types'
+import { AIAssistantState, AIErrorState } from './types'
 import {
   setCurrentConversation,
   fetchConversationsRequest,
@@ -38,9 +38,15 @@
   isLoading: false,
   currentConversation: undefined,
   conversations: [],
-  totalTokensUsed: 0,
-  availableTokens: 1000, // Default token limit
+  sentToken: 0,
+  receiveToken: 0,
+  totalCost: 0,
   error: undefined,
+  errorState: {
+    current: undefined,
+    history: [],
+    isRetrying: false
+  },
   isTyping: false,
   suggestions: [],
   // UI Panel States
@@ -92,12 +98,20 @@
       state.error = action.payload
     })
 
+    .addCase(deleteConversationRequest, (state) => {
+      state.isLoading = true
+      state.error = undefined
+    })
     .addCase(deleteConversationSuccess, (state, action) => {
       state.conversations = state.conversations.filter(c => c.id !== action.payload)
       if (state.currentConversation?.id === action.payload) {
         state.currentConversation = state.conversations[0] || undefined
       }
     })
+    .addCase(deleteConversationFailure, (state, action) => {
+      state.isLoading = false
+      state.error = action.payload
+    })
 
     // Message Actions
     .addCase(sendMessageRequest, (state) => {
@@ -135,10 +149,9 @@
           tokens: action.payload.tokens
         }
 
+        // TODO: Add token usage change
         state.currentConversation.messages.push(aiMessage)
-        state.currentConversation.totalTokens += action.payload.tokens
         state.currentConversation.updatedAt = aiMessage.timestamp
-        state.totalTokensUsed += action.payload.tokens
 
         // Update suggestions if provided
         if (action.payload.suggestions) {
@@ -202,10 +215,8 @@
         )
         if (messageIndex !== -1) {
           state.currentConversation.messages[messageIndex].isLoading = false
-          state.currentConversation.messages[messageIndex].tokens = action.payload.totalTokens
-          state.currentConversation.totalTokens += action.payload.totalTokens
-          state.totalTokensUsed += action.payload.totalTokens
           state.currentConversation.updatedAt = new Date()
+          // TODO: Add token usage change
         }
       }
     })
@@ -225,12 +236,10 @@
 
     // Token Actions
     .addCase(updateTokenUsage, (state, action) => {
-      state.totalTokensUsed = action.payload.used
-      state.availableTokens = action.payload.available
+      // TODO: Add token usage
     })
     .addCase(fetchTokenUsageSuccess, (state, action) => {
-      state.totalTokensUsed = action.payload.used
-      state.availableTokens = action.payload.available
+      // TODO: Add token usage
     })
 
     // Typing Actions
Index: src/modules/_shared/ai/saga.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { Action } from '@reduxjs/toolkit'\nimport { all, call, fork, put, takeLatest, delay, take } from 'redux-saga/effects'\nimport { eventChannel, EventChannel } from 'redux-saga'\nimport { ShowAppMessage } from '../../../helper/share'\nimport { MESSAGE_TYPE } from '../../../constants'\nimport { extractProjectCode } from '../../../helper/share'\nimport aiAssistantService from '../../../services/ai-assistant.service'\nimport {\n  fetchConversationsRequest,\n  fetchConversationsSuccess,\n  fetchConversationsFailure,\n  createConversationRequest,\n  createConversationSuccess,\n  createConversationFailure,\n  deleteConversationRequest,\n  deleteConversationSuccess,\n  deleteConversationFailure,\n  sendMessageRequest,\n  sendMessageSuccess,\n  sendMessageFailure,\n  receiveAIResponse,\n  startStreamingResponse,\n  receiveStreamingChunk,\n  endStreamingResponse,\n  streamingError,\n  fetchTokenUsageRequest,\n  fetchTokenUsageSuccess,\n  fetchTokenUsageFailure,\n  setAITyping,\n  setSuggestions\n} from './actions'\nimport {\n  AIConversation,\n  AIMessage,\n  SendMessageRequest,\n  CreateConversationRequest,\n  AIResponse,\n  TokenUsage,\n  AgentType,\n  TeamRunResponseContentEvent\n} from './types'\n\n// Default agent type and project configuration\nconst getDefaultAgentType = (): AgentType => 'master_agent'\nconst getProjectId = (): string => extractProjectCode() || 'default-project'\n\n// Create event channel for streaming from POST /v1/conversations/:id/messages\nfunction createStreamingChannel(apiRequest: SendMessageRequest): EventChannel<any> {\n  return eventChannel(emitter => {\n    // Start the streaming request\n    aiAssistantService.sendStreamingMessage(\n      apiRequest,\n      // onChunk callback\n      (event: TeamRunResponseContentEvent) => {\n        emitter({\n          type: 'CHUNK',\n          payload: event\n        })\n      },\n      // onComplete callback\n      () => {\n        emitter({\n          type: 'COMPLETE'\n        })\n        emitter('END')\n      },\n      // onError callback\n      (error: Error) => {\n        emitter({\n          type: 'ERROR',\n          payload: error\n        })\n        emitter('END')\n      }\n    )\n\n    // Return unsubscribe function\n    return () => {\n      aiAssistantService.stopStreaming()\n    }\n  })\n}\n\n// Streaming response handler saga\nfunction* handleStreamingResponse(apiRequest: SendMessageRequest, streamingMessageId: string) {\n  try {\n    const streamingChannel = yield call(createStreamingChannel, apiRequest)\n\n    while (true) {\n      const action = yield take(streamingChannel)\n\n      if (action.type === 'CHUNK') {\n        const event = action.payload as TeamRunResponseContentEvent\n        yield put(receiveStreamingChunk({\n          messageId: streamingMessageId,\n          chunk: event.content,\n          agentName: event.agent_name,\n          runId: event.run_id,\n          teamId: event.team_id,\n          teamName: event.team_name\n        }))\n      } else if (action.type === 'COMPLETE') {\n        yield put(endStreamingResponse({\n          messageId: streamingMessageId,\n          totalTokens: 0 // Will be updated when we get token info\n        }))\n        break\n      } else if (action.type === 'ERROR') {\n        yield put(streamingError({\n          messageId: streamingMessageId,\n          error: action.payload.message\n        }))\n        break\n      }\n    }\n\n    streamingChannel.close()\n\n  } catch (error: any) {\n    yield put(streamingError({\n      messageId: streamingMessageId,\n      error: error.message\n    }))\n  }\n}\n\n// Mock data generators for development\nconst generateMockConversations = (): AIConversation[] => [\n  {\n    id: '1',\n    title: 'Analyst this requirement for me',\n    messages: [\n      {\n        id: '1',\n        content: 'I need to summary requirement',\n        type: 'user',\n        timestamp: new Date(Date.now() - 3600000)\n      },\n      {\n        id: '2',\n        content: 'Please provide me a requirement file (doc, image, pdf...) of give me some some information about ABC project.',\n        type: 'assistant',\n        timestamp: new Date(Date.now() - 3500000),\n        tokens: 25\n      }\n    ],\n    totalTokens: 25,\n    createdAt: new Date(Date.now() - 3600000),\n    updatedAt: new Date(Date.now() - 3500000),\n    projectId: 'current-project'\n  }\n]\n\nconst generateAIResponse = (userMessage: string): AIResponse => {\n  // Mock AI responses based on user input\n  const responses = [\n    {\n      content: \"I'd be happy to help you analyze this requirement. To provide the most accurate analysis, could you please share the specific requirement document or details you'd like me to review?\",\n      tokens: 35,\n      suggestions: [\n        \"Upload requirement document\",\n        \"Describe the requirement\",\n        \"Check for completeness\"\n      ]\n    },\n    {\n      content: \"Based on your request, I can help summarize requirements. Please provide the requirement documents or specific details you'd like me to analyze and summarize.\",\n      tokens: 28,\n      suggestions: [\n        \"Upload documents\",\n        \"List key requirements\",\n        \"Generate summary\"\n      ]\n    },\n    {\n      content: \"I can assist with requirement analysis including completeness checks, clarity assessment, and improvement suggestions. What specific aspect would you like me to focus on?\",\n      tokens: 32,\n      suggestions: [\n        \"Check completeness\",\n        \"Improve clarity\",\n        \"Generate test cases\"\n      ]\n    }\n  ]\n\n  return responses[Math.floor(Math.random() * responses.length)]\n}\n\nfunction* handleFetchConversations(action: Action) {\n  if (fetchConversationsRequest.match(action)) {\n    try {\n      const response = yield call(aiAssistantService.listConversations, 50, 0)\n\n      if (response.status === 200) {\n        // The API returns a different structure, so we need to handle it properly\n        let conversations: AIConversation[] = []\n\n        if (response.data && Array.isArray(response.data)) {\n          // If data is directly an array of conversations\n          conversations = response.data.map((conv: any) => aiAssistantService.transformConversation(conv))\n        } else if (response.data && response.data.conversations) {\n          // If data has a conversations property\n          conversations = response.data.conversations.map((conv: any) => aiAssistantService.transformConversation(conv))\n        }\n\n        yield put(fetchConversationsSuccess(conversations))\n      } else {\n        // Fallback to mock data if API fails\n        const conversations = generateMockConversations()\n        yield put(fetchConversationsSuccess(conversations))\n      }\n    } catch (error: any) {\n      // Fallback to mock data on error\n      const conversations = generateMockConversations()\n      yield put(fetchConversationsSuccess(conversations))\n      console.warn('AI API not available, using mock data:', error.message)\n    }\n  }\n}\n\nfunction* handleCreateConversation(action: Action) {\n  if (createConversationRequest.match(action)) {\n    try {\n      const request = action.payload as CreateConversationRequest\n      const response = yield call(aiAssistantService.createConversation, request)\n\n      if (response.status === 200) {\n        const newConversation = aiAssistantService.transformConversation(response.data)\n        // Set a default title if none provided\n        if (request.title) {\n          newConversation.title = request.title\n        }\n        yield put(createConversationSuccess(newConversation))\n        ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'New conversation created')\n      } else {\n        throw new Error('Failed to create conversation')\n      }\n    } catch (error: any) {\n      yield put(createConversationFailure(error.message))\n      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to create conversation')\n    }\n  }\n}\n\nfunction* handleSendMessage(action: Action) {\n  if (sendMessageRequest.match(action)) {\n    try {\n      const request = action.payload as SendMessageRequest\n\n      // Create user message\n      const userMessage: AIMessage = {\n        id: Date.now().toString(),\n        content: request.content,\n        type: 'user',\n        timestamp: new Date()\n      }\n\n      yield put(sendMessageSuccess(userMessage))\n\n      // Show AI typing indicator\n      yield put(setAITyping(true))\n\n      try {\n        // Send message to AI API\n        const apiRequest: SendMessageRequest = {\n          ...request,\n          agentType: request.agentType || getDefaultAgentType(),\n          projectId: request.projectId || getProjectId()\n        }\n\n        if (request.stream) {\n          // Handle real streaming response with SSE\n          const streamingMessageId = `streaming_${Date.now()}`\n          yield put(startStreamingResponse(streamingMessageId))\n\n          try {\n            // Start the streaming saga\n            yield fork(handleStreamingResponse, apiRequest, streamingMessageId)\n          } catch (streamError: any) {\n            yield put(streamingError({ messageId: streamingMessageId, error: streamError.message }))\n          }\n        } else {\n          // Handle regular response\n          const response = yield call(aiAssistantService.sendMessage, apiRequest)\n\n          if (response.status === 200) {\n            let content = ''\n            let tokensUsed = 0\n\n            if (response.data && response.data.message) {\n              content = response.data.message.content || response.data.message\n              tokensUsed = response.data.tokens_used || response.data.message.tokens || 0\n            } else if (typeof response.data === 'string') {\n              content = response.data\n            }\n\n            const aiResponse: AIResponse = {\n              content,\n              tokens: tokensUsed,\n              suggestions: [] // Add suggestions logic if needed\n            }\n            yield put(receiveAIResponse(aiResponse))\n          } else {\n            throw new Error('Failed to get AI response')\n          }\n        }\n      } catch (apiError: any) {\n        // Fallback to mock response if API fails\n        console.warn('AI API failed, using mock response:', apiError.message)\n        const aiResponse = generateAIResponse(request.content)\n        yield put(receiveAIResponse(aiResponse))\n      }\n\n    } catch (error: any) {\n      yield put(sendMessageFailure(error.message))\n      yield put(setAITyping(false))\n      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to send message')\n    }\n  }\n}\n\nfunction* handleDeleteConversation(action: Action) {\n  if (deleteConversationRequest.match(action)) {\n    try {\n      const conversationId = action.payload as string\n      const response = yield call(aiAssistantService.deleteConversation, conversationId)\n\n      if (response.status === 200) {\n        yield put(deleteConversationSuccess(conversationId))\n        ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'Conversation deleted')\n      } else {\n        throw new Error('Failed to delete conversation')\n      }\n    } catch (error: any) {\n      yield put(deleteConversationFailure(error.message))\n      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to delete conversation')\n    }\n  }\n}\n\nfunction* handleFetchTokenUsage(action: Action) {\n  if (fetchTokenUsageRequest.match(action)) {\n    try {\n      // const response = yield call(apiCall, 'GET', AI_API_URLS.TOKEN_USAGE)\n      yield delay(300)\n\n      const tokenUsage: TokenUsage = {\n        used: 150,\n        available: 1000,\n        percentage: 15\n      }\n\n      yield put(fetchTokenUsageSuccess(tokenUsage))\n    } catch (error) {\n      yield put(fetchTokenUsageFailure('Failed to fetch token usage'))\n    }\n  }\n}\n\nfunction* watchAIAssistantRequests() {\n  yield takeLatest(fetchConversationsRequest.type, handleFetchConversations)\n  yield takeLatest(createConversationRequest.type, handleCreateConversation)\n  yield takeLatest(sendMessageRequest.type, handleSendMessage)\n  yield takeLatest(deleteConversationRequest.type, handleDeleteConversation)\n  yield takeLatest(fetchTokenUsageRequest.type, handleFetchTokenUsage)\n}\n\nexport default function* aiAssistantSaga() {\n  yield all([fork(watchAIAssistantRequests)])\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/saga.ts b/src/modules/_shared/ai/saga.ts
--- a/src/modules/_shared/ai/saga.ts	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/modules/_shared/ai/saga.ts	(date 1751346090161)
@@ -27,7 +27,7 @@
   fetchTokenUsageSuccess,
   fetchTokenUsageFailure,
   setAITyping,
-  setSuggestions
+  setSuggestions,
 } from './actions'
 import {
   AIConversation,
@@ -91,18 +91,20 @@
 
       if (action.type === 'CHUNK') {
         const event = action.payload as TeamRunResponseContentEvent
-        yield put(receiveStreamingChunk({
-          messageId: streamingMessageId,
-          chunk: event.content,
-          agentName: event.agent_name,
-          runId: event.run_id,
-          teamId: event.team_id,
-          teamName: event.team_name
-        }))
+        if (event.event === 'TeamRunResponseContent') {
+          yield put(receiveStreamingChunk({
+            messageId: streamingMessageId,
+            chunk: event.content,
+            agentName: event.agent_name,
+            runId: event.run_id,
+            teamId: event.team_id,
+            teamName: event.team_name
+          }))
+        }
       } else if (action.type === 'COMPLETE') {
         yield put(endStreamingResponse({
           messageId: streamingMessageId,
-          totalTokens: 0 // Will be updated when we get token info
+          totalTokens: 0 // TODO: Will be updated when we get token info
         }))
         break
       } else if (action.type === 'ERROR') {
@@ -124,96 +126,27 @@
   }
 }
 
-// Mock data generators for development
-const generateMockConversations = (): AIConversation[] => [
-  {
-    id: '1',
-    title: 'Analyst this requirement for me',
-    messages: [
-      {
-        id: '1',
-        content: 'I need to summary requirement',
-        type: 'user',
-        timestamp: new Date(Date.now() - 3600000)
-      },
-      {
-        id: '2',
-        content: 'Please provide me a requirement file (doc, image, pdf...) of give me some some information about ABC project.',
-        type: 'assistant',
-        timestamp: new Date(Date.now() - 3500000),
-        tokens: 25
-      }
-    ],
-    totalTokens: 25,
-    createdAt: new Date(Date.now() - 3600000),
-    updatedAt: new Date(Date.now() - 3500000),
-    projectId: 'current-project'
-  }
-]
-
-const generateAIResponse = (userMessage: string): AIResponse => {
-  // Mock AI responses based on user input
-  const responses = [
-    {
-      content: "I'd be happy to help you analyze this requirement. To provide the most accurate analysis, could you please share the specific requirement document or details you'd like me to review?",
-      tokens: 35,
-      suggestions: [
-        "Upload requirement document",
-        "Describe the requirement",
-        "Check for completeness"
-      ]
-    },
-    {
-      content: "Based on your request, I can help summarize requirements. Please provide the requirement documents or specific details you'd like me to analyze and summarize.",
-      tokens: 28,
-      suggestions: [
-        "Upload documents",
-        "List key requirements",
-        "Generate summary"
-      ]
-    },
-    {
-      content: "I can assist with requirement analysis including completeness checks, clarity assessment, and improvement suggestions. What specific aspect would you like me to focus on?",
-      tokens: 32,
-      suggestions: [
-        "Check completeness",
-        "Improve clarity",
-        "Generate test cases"
-      ]
-    }
-  ]
-
-  return responses[Math.floor(Math.random() * responses.length)]
-}
-
 function* handleFetchConversations(action: Action) {
   if (fetchConversationsRequest.match(action)) {
     try {
       const response = yield call(aiAssistantService.listConversations, 50, 0)
 
-      if (response.status === 200) {
-        // The API returns a different structure, so we need to handle it properly
-        let conversations: AIConversation[] = []
+      // The API returns a different structure, so we need to handle it properly
+      let conversations: AIConversation[] = []
 
-        if (response.data && Array.isArray(response.data)) {
-          // If data is directly an array of conversations
-          conversations = response.data.map((conv: any) => aiAssistantService.transformConversation(conv))
-        } else if (response.data && response.data.conversations) {
-          // If data has a conversations property
-          conversations = response.data.conversations.map((conv: any) => aiAssistantService.transformConversation(conv))
-        }
+      if (response.data && Array.isArray(response.data)) {
+        // If data is directly an array of conversations
+        conversations = response.data.map((conv: any) => aiAssistantService.transformConversation(conv))
+      } else if (response.data && response.data.conversations) {
+        // If data has a conversations property
+        conversations = response.data.conversations.map((conv: any) => aiAssistantService.transformConversation(conv))
+      }
 
-        yield put(fetchConversationsSuccess(conversations))
-      } else {
-        // Fallback to mock data if API fails
-        const conversations = generateMockConversations()
-        yield put(fetchConversationsSuccess(conversations))
-      }
+      yield put(fetchConversationsSuccess(conversations))
     } catch (error: any) {
       // Fallback to mock data on error
-      const conversations = generateMockConversations()
-      yield put(fetchConversationsSuccess(conversations))
-      console.warn('AI API not available, using mock data:', error.message)
+      yield put(fetchConversationsFailure(error.message))
+      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to fetch conversations')
     }
   }
 }
@@ -224,17 +157,16 @@
       const request = action.payload as CreateConversationRequest
       const response = yield call(aiAssistantService.createConversation, request)
 
-      if (response.status === 200) {
-        const newConversation = aiAssistantService.transformConversation(response.data)
-        // Set a default title if none provided
-        if (request.title) {
-          newConversation.title = request.title
-        }
-        yield put(createConversationSuccess(newConversation))
-        ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'New conversation created')
-      } else {
-        throw new Error('Failed to create conversation')
-      }
+      const newConversation = aiAssistantService.transformConversation(response.data)
+      // Set a default title if none provided
+      if (request.title) {
+        newConversation.title = request.title
+      }
+      newConversation.sentToken = 0
+      newConversation.receiveToken = 0
+      newConversation.totalCost = 0
+      yield put(createConversationSuccess(newConversation))
+      // ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'New conversation created')
     } catch (error: any) {
       yield put(createConversationFailure(error.message))
       ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to create conversation')
@@ -244,9 +176,10 @@
 
 function* handleSendMessage(action: Action) {
   if (sendMessageRequest.match(action)) {
-    try {
-      const request = action.payload as SendMessageRequest
+    const request = action.payload as SendMessageRequest
 
+    try {
+
       // Create user message
       const userMessage: AIMessage = {
         id: Date.now().toString(),
@@ -260,61 +193,51 @@
       // Show AI typing indicator
       yield put(setAITyping(true))
 
-      try {
-        // Send message to AI API
-        const apiRequest: SendMessageRequest = {
-          ...request,
-          agentType: request.agentType || getDefaultAgentType(),
-          projectId: request.projectId || getProjectId()
-        }
+      // Send message to AI API
+      const apiRequest: SendMessageRequest = {
+        ...request,
+        agentType: request.agentType || getDefaultAgentType(),
+        projectId: request.projectId || getProjectId()
+      }
 
-        if (request.stream) {
-          // Handle real streaming response with SSE
-          const streamingMessageId = `streaming_${Date.now()}`
-          yield put(startStreamingResponse(streamingMessageId))
+      if (request.stream) {
+        // Handle real streaming response with SSE
+        const streamingMessageId = `streaming_${Date.now()}`
+        yield put(startStreamingResponse(streamingMessageId))
 
-          try {
-            // Start the streaming saga
-            yield fork(handleStreamingResponse, apiRequest, streamingMessageId)
-          } catch (streamError: any) {
-            yield put(streamingError({ messageId: streamingMessageId, error: streamError.message }))
-          }
-        } else {
-          // Handle regular response
-          const response = yield call(aiAssistantService.sendMessage, apiRequest)
+        try {
+          // Start the streaming saga
+          yield fork(handleStreamingResponse, apiRequest, streamingMessageId)
+        } catch (streamError: any) {
+          yield put(streamingError({ messageId: streamingMessageId, error: streamError.message }))
+        }
+      } else {
+        // Handle regular response
+        const response = yield call(aiAssistantService.sendMessage, apiRequest)
 
-          if (response.status === 200) {
-            let content = ''
-            let tokensUsed = 0
+        if (response.status === 200) {
+          let content = ''
+          let tokensUsed = 0
 
-            if (response.data && response.data.message) {
-              content = response.data.message.content || response.data.message
-              tokensUsed = response.data.tokens_used || response.data.message.tokens || 0
-            } else if (typeof response.data === 'string') {
-              content = response.data
-            }
+          if (response.data && response.data.message) {
+            content = response.data.message.content || response.data.message
+            tokensUsed = response.data.tokens_used || response.data.message.tokens || 0
+          } else if (typeof response.data === 'string') {
+            content = response.data
+          }
 
-            const aiResponse: AIResponse = {
-              content,
-              tokens: tokensUsed,
-              suggestions: [] // Add suggestions logic if needed
-            }
-            yield put(receiveAIResponse(aiResponse))
-          } else {
-            throw new Error('Failed to get AI response')
-          }
+          const aiResponse: AIResponse = {
+            content,
+            tokens: tokensUsed,
+            suggestions: [] // Add suggestions logic if needed
+          }
+          yield put(receiveAIResponse(aiResponse))
+        } else {
+          throw new Error('Failed to get AI response')
         }
-      } catch (apiError: any) {
-        // Fallback to mock response if API fails
-        console.warn('AI API failed, using mock response:', apiError.message)
-        const aiResponse = generateAIResponse(request.content)
-        yield put(receiveAIResponse(aiResponse))
       }
-
     } catch (error: any) {
       yield put(sendMessageFailure(error.message))
-      yield put(setAITyping(false))
-      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to send message')
     }
   }
 }
@@ -323,17 +246,11 @@
   if (deleteConversationRequest.match(action)) {
     try {
       const conversationId = action.payload as string
-      const response = yield call(aiAssistantService.deleteConversation, conversationId)
+      yield call(aiAssistantService.deleteConversation, conversationId)
 
-      if (response.status === 200) {
-        yield put(deleteConversationSuccess(conversationId))
-        ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'Conversation deleted')
-      } else {
-        throw new Error('Failed to delete conversation')
-      }
+      yield put(deleteConversationSuccess(conversationId))
     } catch (error: any) {
       yield put(deleteConversationFailure(error.message))
-      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to delete conversation')
     }
   }
 }
@@ -341,6 +258,7 @@
 function* handleFetchTokenUsage(action: Action) {
   if (fetchTokenUsageRequest.match(action)) {
     try {
+      // TODO: Implement real apis
       // const response = yield call(apiCall, 'GET', AI_API_URLS.TOKEN_USAGE)
       yield delay(300)
 
@@ -351,8 +269,8 @@
       }
 
       yield put(fetchTokenUsageSuccess(tokenUsage))
-    } catch (error) {
-      yield put(fetchTokenUsageFailure('Failed to fetch token usage'))
+    } catch (error: any) {
+      yield put(fetchTokenUsageFailure(error.message))
     }
   }
 }
Index: src/modules/_shared/ai/actions.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { createAction } from '@reduxjs/toolkit'\nimport {\n  AIActionTypes,\n  AIConversation,\n  AIMessage,\n  SendMessageRequest,\n  CreateConversationRequest,\n  AIResponse,\n  TokenUsage,\n  StreamingChunkData\n} from './types'\n\n// UI Actions\nexport const setCurrentConversation = createAction<string>(AIActionTypes.SET_CURRENT_CONVERSATION)\n\n// Conversation Actions\nexport const fetchConversationsRequest = createAction(AIActionTypes.FETCH_CONVERSATIONS_REQUEST)\nexport const fetchConversationsSuccess = createAction<AIConversation[]>(AIActionTypes.FETCH_CONVERSATIONS_SUCCESS)\nexport const fetchConversationsFailure = createAction<string>(AIActionTypes.FETCH_CONVERSATIONS_FAILURE)\n\nexport const createConversationRequest = createAction<CreateConversationRequest>(AIActionTypes.CREATE_CONVERSATION_REQUEST)\nexport const createConversationSuccess = createAction<AIConversation>(AIActionTypes.CREATE_CONVERSATION_SUCCESS)\nexport const createConversationFailure = createAction<string>(AIActionTypes.CREATE_CONVERSATION_FAILURE)\n\nexport const deleteConversationRequest = createAction<string>(AIActionTypes.DELETE_CONVERSATION_REQUEST)\nexport const deleteConversationSuccess = createAction<string>(AIActionTypes.DELETE_CONVERSATION_SUCCESS)\nexport const deleteConversationFailure = createAction<string>(AIActionTypes.DELETE_CONVERSATION_FAILURE)\n\n// Message Actions\nexport const sendMessageRequest = createAction<SendMessageRequest>(AIActionTypes.SEND_MESSAGE_REQUEST)\nexport const sendMessageSuccess = createAction<AIMessage>(AIActionTypes.SEND_MESSAGE_SUCCESS)\nexport const sendMessageFailure = createAction<string>(AIActionTypes.SEND_MESSAGE_FAILURE)\n\nexport const receiveAIResponse = createAction<AIResponse>(AIActionTypes.RECEIVE_AI_RESPONSE)\n\n// Streaming Actions\nexport const startStreamingResponse = createAction<string>(AIActionTypes.START_STREAMING_RESPONSE) // messageId\nexport const receiveStreamingChunk = createAction<StreamingChunkData>(AIActionTypes.RECEIVE_STREAMING_CHUNK)\nexport const endStreamingResponse = createAction<{ messageId: string; totalTokens: number }>(AIActionTypes.END_STREAMING_RESPONSE)\nexport const streamingError = createAction<{ messageId: string; error: string }>(AIActionTypes.STREAMING_ERROR)\n\n// Token Actions\nexport const updateTokenUsage = createAction<TokenUsage>(AIActionTypes.UPDATE_TOKEN_USAGE)\nexport const fetchTokenUsageRequest = createAction(AIActionTypes.FETCH_TOKEN_USAGE_REQUEST)\nexport const fetchTokenUsageSuccess = createAction<TokenUsage>(AIActionTypes.FETCH_TOKEN_USAGE_SUCCESS)\nexport const fetchTokenUsageFailure = createAction<string>(AIActionTypes.FETCH_TOKEN_USAGE_FAILURE)\n\n// Typing Actions\nexport const setAITyping = createAction<boolean>(AIActionTypes.SET_AI_TYPING)\n\n// Suggestions Actions\nexport const setSuggestions = createAction<string[]>(AIActionTypes.SET_SUGGESTIONS)\nexport const clearSuggestions = createAction(AIActionTypes.CLEAR_SUGGESTIONS)\n\n// Error Actions\nexport const clearAIError = createAction(AIActionTypes.CLEAR_AI_ERROR)\n\n// Panel Management Actions\nexport const toggleAIChatPanel = createAction(AIActionTypes.TOGGLE_AI_CHAT_PANEL)\nexport const toggleAISetingsPanel = createAction(AIActionTypes.TOGGLE_SETTINGS_PANEL)\nexport const setActiveTab = createAction<'ai-chat' | 'settings' | null>(AIActionTypes.SET_ACTIVE_TAB)\nexport const closeAllPanels = createAction(AIActionTypes.CLOSE_ALL_PANELS)\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/actions.ts b/src/modules/_shared/ai/actions.ts
--- a/src/modules/_shared/ai/actions.ts	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/modules/_shared/ai/actions.ts	(date 1751346090160)
@@ -7,7 +7,8 @@
   CreateConversationRequest,
   AIResponse,
   TokenUsage,
-  StreamingChunkData
+  StreamingChunkData,
+  AIError
 } from './types'
 
 // UI Actions
Index: src/modules/_shared/ai/components/ai-settings.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import React, { useState } from 'react'\nimport { useDispatch, useSelector } from 'react-redux'\nimport {\n  Card,\n  Select,\n  Form,\n  Typography,\n  Space,\n  Divider,\n  Tag,\n  Tooltip,\n  Switch,\n  InputNumber,\n  Button\n} from 'antd'\nimport {\n  RobotOutlined,\n  SettingOutlined,\n  ThunderboltOutlined,\n  SaveOutlined\n} from '@ant-design/icons'\nimport { AgentType, AvaiModel, AIAssistantState } from '../types'\nimport AppState from '../../../../store/types'\nimport './ai-settings.less'\n\nconst { Title, Text } = Typography\nconst { Option } = Select\n\ninterface AISettingsProps {\n  onSettingsChange?: (settings: AISettings) => void\n}\n\nexport interface AISettings {\n  agentType: AgentType\n  model: AvaiModel\n  stream: boolean\n  maxTokens: number\n  temperature: number\n}\n\nconst AGENT_TYPES: { value: AgentType; label: string; description: string; color: string }[] = [\n  {\n    value: 'master_agent',\n    label: 'Master Agent',\n    description: 'Strategic analysis and high-level decision making',\n    color: 'blue'\n  },\n  {\n    value: 'ur_agent',\n    label: 'User Requirement Agent',\n    description: 'Specialized in user requirements analysis',\n    color: 'green'\n  },\n  {\n    value: 'hlr_agent',\n    label: 'High-Level Requirement Agent',\n    description: 'Focus on high-level requirement specification',\n    color: 'purple'\n  }\n]\n\nconst AI_MODELS: { value: AvaiModel; label: string; description: string; performance: string }[] = [\n  {\n    value: 'gpt-4.1-mini',\n    label: 'GPT-4.1 Mini',\n    description: 'Fast and efficient for simple tasks',\n    performance: 'Fast'\n  },\n  {\n    value: 'gpt-4.1',\n    label: 'GPT-4.1',\n    description: 'Balanced performance and capability',\n    performance: 'Balanced'\n  },\n  {\n    value: 'gpt-o4',\n    label: 'GPT-O4',\n    description: 'Most advanced model for complex analysis',\n    performance: 'Advanced'\n  }\n]\n\nexport const AISettings: React.FC<AISettingsProps> = ({ onSettingsChange }) => {\n  const dispatch = useDispatch()\n  const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState\n\n  const [settings, setSettings] = useState<AISettings>({\n    agentType: 'leader_agent',\n    model: 'gpt-4.1',\n    stream: true,\n    maxTokens: 2000,\n    temperature: 0.7\n  })\n\n  const handleSettingChange = (key: keyof AISettings, value: any) => {\n    const newSettings = { ...settings, [key]: value }\n    setSettings(newSettings)\n    onSettingsChange?.(newSettings)\n  }\n\n  const handleSaveSettings = () => {\n    // Save settings to localStorage or dispatch to Redux\n    localStorage.setItem('ai-assistant-settings', JSON.stringify(settings))\n    // You could also dispatch an action to save to Redux store\n  }\n\n  const renderAgentTypeOption = (agent: typeof AGENT_TYPES[0]) => (\n    <Option key={agent.value} value={agent.value}>\n      <div className=\"agent-option\">\n        <div className=\"agent-header\">\n          <Tag color={agent.color}>{agent.label}</Tag>\n        </div>\n        <Text type=\"secondary\" className=\"agent-description\">\n          {agent.description}\n        </Text>\n      </div>\n    </Option>\n  )\n\n  const renderModelOption = (model: typeof AI_MODELS[0]) => (\n    <Option key={model.value} value={model.value}>\n      <div className=\"model-option\">\n        <div className=\"model-header\">\n          <span className=\"model-name\">{model.label}</span>\n          <Tag color={model.performance === 'Fast' ? 'green' : model.performance === 'Balanced' ? 'blue' : 'gold'}>\n            {model.performance}\n          </Tag>\n        </div>\n        <Text type=\"secondary\" className=\"model-description\">\n          {model.description}\n        </Text>\n      </div>\n    </Option>\n  )\n\n  return (\n    <div className=\"ai-settings\">\n      <div className=\"settings-header\">\n        <Title level={4}>\n          <SettingOutlined /> AI Assistant Settings\n        </Title>\n      </div>\n\n      <Form layout=\"vertical\" className=\"settings-form\">\n        <Form.Item label=\"Agent Type\" className=\"agent-selection\">\n          <Select\n            value={settings.agentType}\n            onChange={(value) => handleSettingChange('agentType', value)}\n            placeholder=\"Select an agent type\"\n            optionLabelProp=\"label\"\n          >\n            {AGENT_TYPES.map(renderAgentTypeOption)}\n          </Select>\n          <Text type=\"secondary\" className=\"help-text\">\n            Choose the type of AI agent based on your analysis needs\n          </Text>\n        </Form.Item>\n\n        <Form.Item label=\"AI Model\" className=\"model-selection\">\n          <Select\n            value={settings.model}\n            onChange={(value) => handleSettingChange('model', value)}\n            placeholder=\"Select an AI model\"\n            optionLabelProp=\"label\"\n          >\n            {AI_MODELS.map(renderModelOption)}\n          </Select>\n          <Text type=\"secondary\" className=\"help-text\">\n            Select the AI model that best fits your task complexity\n          </Text>\n        </Form.Item>\n\n        <Divider />\n\n        <Form.Item label=\"Advanced Settings\">\n          <Space direction=\"vertical\" style={{ width: '100%' }}>\n            <div className=\"setting-row\">\n              <div className=\"setting-label\">\n                <Text>Streaming Response</Text>\n                <Tooltip title=\"Enable real-time streaming of AI responses\">\n                  <ThunderboltOutlined style={{ marginLeft: 4, color: '#1890ff' }} />\n                </Tooltip>\n              </div>\n              <Switch\n                checked={settings.stream}\n                onChange={(checked) => handleSettingChange('stream', checked)}\n              />\n            </div>\n\n            <div className=\"setting-row\">\n              <div className=\"setting-label\">\n                <Text>Max Tokens</Text>\n                <Tooltip title=\"Maximum number of tokens in the response\">\n                  <ThunderboltOutlined style={{ marginLeft: 4, color: '#1890ff' }} />\n                </Tooltip>\n              </div>\n              <InputNumber\n                min={100}\n                max={4000}\n                value={settings.maxTokens}\n                onChange={(value) => handleSettingChange('maxTokens', value || 2000)}\n                style={{ width: 120 }}\n              />\n            </div>\n\n            <div className=\"setting-row\">\n              <div className=\"setting-label\">\n                <Text>Temperature</Text>\n                <Tooltip title=\"Controls randomness in responses (0.0 = deterministic, 1.0 = creative)\">\n                  <ThunderboltOutlined style={{ marginLeft: 4, color: '#1890ff' }} />\n                </Tooltip>\n              </div>\n              <InputNumber\n                min={0}\n                max={1}\n                step={0.1}\n                value={settings.temperature}\n                onChange={(value) => handleSettingChange('temperature', value || 0.7)}\n                style={{ width: 120 }}\n              />\n            </div>\n          </Space>\n        </Form.Item>\n\n        <Form.Item>\n          <Button\n            type=\"primary\"\n            icon={<SaveOutlined />}\n            onClick={handleSaveSettings}\n            block\n          >\n            Save Settings\n          </Button>\n        </Form.Item>\n      </Form>\n\n      <div className=\"settings-info\">\n        <Card size=\"small\" className=\"info-card\">\n          <Title level={5}>Current Configuration</Title>\n          <Space direction=\"vertical\" size=\"small\">\n            <div>\n              <Text strong>Agent: </Text>\n              <Tag color={AGENT_TYPES.find(a => a.value === settings.agentType)?.color}>\n                {AGENT_TYPES.find(a => a.value === settings.agentType)?.label}\n              </Tag>\n            </div>\n            <div>\n              <Text strong>Model: </Text>\n              <Tag color=\"blue\">{settings.model}</Tag>\n            </div>\n            <div>\n              <Text strong>Streaming: </Text>\n              <Tag color={settings.stream ? 'green' : 'red'}>\n                {settings.stream ? 'Enabled' : 'Disabled'}\n              </Tag>\n            </div>\n          </Space>\n        </Card>\n      </div>\n    </div>\n  )\n}\n\nexport default AISettings\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/components/ai-settings.tsx b/src/modules/_shared/ai/components/ai-settings.tsx
--- a/src/modules/_shared/ai/components/ai-settings.tsx	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/modules/_shared/ai/components/ai-settings.tsx	(date 1751346090160)
@@ -85,7 +85,7 @@
   const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState
 
   const [settings, setSettings] = useState<AISettings>({
-    agentType: 'leader_agent',
+    agentType: 'master_agent',
     model: 'gpt-4.1',
     stream: true,
     maxTokens: 2000,
Index: src/modules/_shared/ai/types.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>// API Schema Types based on OpenAPI specification\nexport type AgentType = 'master_agent' | 'ur_agent' | 'hlr_agent'\nexport type AvaiModel = 'gpt-4.1-mini' | 'gpt-4.1' | 'gpt-o4'\n\nexport interface MessageRequest extends Record<string, any> {\n  message: string\n  stream?: boolean\n  model?: AvaiModel\n  agent_type: AgentType\n  project_id: string\n}\n\nexport interface ValidationError {\n  loc: (string | number)[]\n  msg: string\n  type: string\n}\n\nexport interface HTTPValidationError {\n  detail: ValidationError[]\n}\n\nexport interface AIMessage {\n  id: string\n  content: string\n  type: 'user' | 'assistant' | 'system'\n  timestamp: Date\n  isLoading?: boolean\n  tokens?: number\n  attachments?: AIAttachment[]\n  agentName?: string\n  runId?: string\n  teamId?: string\n  teamName?: string\n}\n\nexport interface AIAttachment {\n  id: string\n  name: string\n  type: 'document' | 'image' | 'requirement' | 'artefact'\n  url?: string\n  content?: string\n  size?: number\n}\n\nexport interface AIConversation {\n  id: string\n  title: string\n  messages: AIMessage[]\n  totalTokens: number\n  createdAt: Date\n  updatedAt: Date\n  projectId?: string\n}\n\nexport interface AIAssistantState {\n  isOpen: boolean\n  isLoading: boolean\n  currentConversation?: AIConversation\n  conversations: AIConversation[]\n  totalTokensUsed: number\n  availableTokens: number\n  error?: string\n  isTyping: boolean\n  suggestions: string[]\n  // UI Panel States\n  isAiPanelOpen: boolean\n  activeTab: 'ai-chat' | 'settings' | null\n}\n\nexport interface SendMessageRequest {\n  content: string\n  conversationId: string\n  attachments?: File[]\n  referenceArtefacts?: string[]\n  agentType: AgentType\n  model?: AvaiModel\n  projectId: string\n  stream?: boolean\n}\n\nexport interface CreateConversationRequest {\n  title?: string\n  initialMessage?: string\n  projectId: string\n}\n\nexport interface AIResponse {\n  content: string\n  tokens: number\n  suggestions?: string[]\n  references?: AIReference[]\n}\n\nexport interface AIReference {\n  id: string\n  type: 'requirement' | 'document' | 'artefact'\n  title: string\n  url: string\n  excerpt?: string\n}\n\nexport interface TokenUsage {\n  used: number\n  available: number\n  percentage: number\n}\n\n// Event Stream Types for TeamRunResponseContent\nexport interface TeamRunResponseContentEvent {\n  type: 'agent'\n  event: 'TeamRunResponseContent'\n  agent_id: string | null\n  agent_name: string\n  run_id: string\n  conversation_id: string\n  content: string\n  content_type: 'str'\n  thinking: string\n  team_id: string\n  team_name: string\n}\n\nexport interface StreamingChunkData {\n  messageId: string\n  chunk: string\n  agentName?: string\n  runId?: string\n  teamId?: string\n  teamName?: string\n}\n\nexport enum AIActionTypes {\n  // UI Actions\n  SET_CURRENT_CONVERSATION = 'SET_CURRENT_CONVERSATION',\n\n  // Conversation Actions\n  FETCH_CONVERSATIONS_REQUEST = 'FETCH_CONVERSATIONS_REQUEST',\n  FETCH_CONVERSATIONS_SUCCESS = 'FETCH_CONVERSATIONS_SUCCESS',\n  FETCH_CONVERSATIONS_FAILURE = 'FETCH_CONVERSATIONS_FAILURE',\n\n  CREATE_CONVERSATION_REQUEST = 'CREATE_CONVERSATION_REQUEST',\n  CREATE_CONVERSATION_SUCCESS = 'CREATE_CONVERSATION_SUCCESS',\n  CREATE_CONVERSATION_FAILURE = 'CREATE_CONVERSATION_FAILURE',\n\n  DELETE_CONVERSATION_REQUEST = 'DELETE_CONVERSATION_REQUEST',\n  DELETE_CONVERSATION_SUCCESS = 'DELETE_CONVERSATION_SUCCESS',\n  DELETE_CONVERSATION_FAILURE = 'DELETE_CONVERSATION_FAILURE',\n\n  // Message Actions\n  SEND_MESSAGE_REQUEST = 'SEND_MESSAGE_REQUEST',\n  SEND_MESSAGE_SUCCESS = 'SEND_MESSAGE_SUCCESS',\n  SEND_MESSAGE_FAILURE = 'SEND_MESSAGE_FAILURE',\n\n  RECEIVE_AI_RESPONSE = 'RECEIVE_AI_RESPONSE',\n\n  // Streaming Actions\n  START_STREAMING_RESPONSE = 'START_STREAMING_RESPONSE',\n  RECEIVE_STREAMING_CHUNK = 'RECEIVE_STREAMING_CHUNK',\n  END_STREAMING_RESPONSE = 'END_STREAMING_RESPONSE',\n  STREAMING_ERROR = 'STREAMING_ERROR',\n\n  // Token Actions\n  UPDATE_TOKEN_USAGE = 'UPDATE_TOKEN_USAGE',\n  FETCH_TOKEN_USAGE_REQUEST = 'FETCH_TOKEN_USAGE_REQUEST',\n  FETCH_TOKEN_USAGE_SUCCESS = 'FETCH_TOKEN_USAGE_SUCCESS',\n  FETCH_TOKEN_USAGE_FAILURE = 'FETCH_TOKEN_USAGE_FAILURE',\n\n  // Typing Actions\n  SET_AI_TYPING = 'SET_AI_TYPING',\n\n  // Suggestions Actions\n  SET_SUGGESTIONS = 'SET_SUGGESTIONS',\n  CLEAR_SUGGESTIONS = 'CLEAR_SUGGESTIONS',\n\n  // Error Actions\n  CLEAR_AI_ERROR = 'CLEAR_AI_ERROR',\n\n  // Panel Management Actions\n  TOGGLE_AI_CHAT_PANEL = 'TOGGLE_AI_CHAT_PANEL',\n  TOGGLE_SETTINGS_PANEL = 'TOGGLE_SETTINGS_PANEL',\n  SET_ACTIVE_TAB = 'SET_ACTIVE_TAB',\n  CLOSE_ALL_PANELS = 'CLOSE_ALL_PANELS'\n}\n\nexport interface AIPromptTemplate {\n  id: string\n  title: string\n  description: string\n  prompt: string\n  category: 'summarize' | 'high-level-requirement' | 'details-requirement' | 'review'\n  tokens: number\n}\n\nexport const DEFAULT_PROMPTS: AIPromptTemplate[] = [\n  {\n    id: 'summarize-requirement',\n    title: 'Summarize Requirement',\n    description: 'Summarize this requirement for completeness and clarity',\n    prompt: 'Please summarize this requirement for completeness, clarity, and potential issues. Provide suggestions for improvement.',\n    category: 'summarize',\n    tokens: 50\n  },\n  {\n    id: 'generate-high-level-requirement',\n    title: 'Generate High-Level Requirement',\n    description: 'Generate high-level requirement for this requirement',\n    prompt: 'Based on this requirement, please generate high-level requirement including positive, negative, and edge cases.',\n    category: 'high-level-requirement',\n    tokens: 75\n  },\n  {\n    id: 'use-case-diagram',\n    title: 'Generate Use Case Diagram',\n    description: 'Generate use case diagram for this requirement',\n    prompt: 'Based on this requirement, please generate use case diagram including positive, negative, and edge cases.',\n    category: 'details-requirement',\n    tokens: 60\n  },\n  {\n    id: 'review-requirement',\n    title: 'Review Requirement',\n    description: 'Review this requirement for completeness and clarity',\n    prompt: 'Please review this requirement for completeness, clarity, and adherence to best practices. Suggest improvements.',\n    category: 'review',\n    tokens: 40\n  },\n  {\n    id: 'cr-impact-analysis',\n    title: 'Change Request Impact Analysis',\n    description: 'Analyze the impact of proposed changes on existing requirements and system components',\n    prompt: 'Please analyze the impact of this change request on existing requirements, system architecture, and related components. Identify potential risks, dependencies, and areas that may be affected by implementing this change.',\n    category: 'review',\n    tokens: 40\n  }\n]\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/types.ts b/src/modules/_shared/ai/types.ts
--- a/src/modules/_shared/ai/types.ts	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/modules/_shared/ai/types.ts	(date 1751346090161)
@@ -20,6 +20,52 @@
   detail: ValidationError[]
 }
 
+// Enhanced Error Types
+export enum AIErrorType {
+  NETWORK_ERROR = 'NETWORK_ERROR',
+  API_ERROR = 'API_ERROR',
+  VALIDATION_ERROR = 'VALIDATION_ERROR',
+  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
+  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
+  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
+  STREAMING_ERROR = 'STREAMING_ERROR',
+  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
+  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
+}
+
+export enum AIErrorSeverity {
+  LOW = 'LOW',
+  MEDIUM = 'MEDIUM',
+  HIGH = 'HIGH',
+  CRITICAL = 'CRITICAL'
+}
+
+export interface AIError {
+  id: string
+  type: AIErrorType
+  severity: AIErrorSeverity
+  message: string
+  details?: string
+  timestamp: Date
+  context?: {
+    action?: string
+    conversationId?: string
+    messageId?: string
+    userId?: string
+    projectId?: string
+  }
+  originalError?: any
+  retryable: boolean
+  retryCount?: number
+  maxRetries?: number
+}
+
+export interface AIErrorState {
+  current?: AIError
+  history: AIError[]
+  isRetrying: boolean
+}
+
 export interface AIMessage {
   id: string
   content: string
@@ -47,7 +93,9 @@
   id: string
   title: string
   messages: AIMessage[]
-  totalTokens: number
+  sentToken: number
+  receiveToken: number
+  totalCost: number
   createdAt: Date
   updatedAt: Date
   projectId?: string
@@ -58,9 +106,11 @@
   isLoading: boolean
   currentConversation?: AIConversation
   conversations: AIConversation[]
-  totalTokensUsed: number
-  availableTokens: number
-  error?: string
+  error?: string // Keep for backward compatibility
+  errorState: AIErrorState
+  sentToken: number
+  receiveToken: number
+  totalCost: number
   isTyping: boolean
   suggestions: string[]
   // UI Panel States
@@ -119,6 +169,7 @@
   thinking: string
   team_id: string
   team_name: string
+  tool_call_id?: string // Optional field for tool calls
 }
 
 export interface StreamingChunkData {
@@ -175,6 +226,10 @@
 
   // Error Actions
   CLEAR_AI_ERROR = 'CLEAR_AI_ERROR',
+  SET_AI_ERROR = 'SET_AI_ERROR',
+  ADD_ERROR_TO_HISTORY = 'ADD_ERROR_TO_HISTORY',
+  CLEAR_ERROR_HISTORY = 'CLEAR_ERROR_HISTORY',
+  SET_RETRY_STATE = 'SET_RETRY_STATE',
 
   // Panel Management Actions
   TOGGLE_AI_CHAT_PANEL = 'TOGGLE_AI_CHAT_PANEL',
Index: src/modules/_shared/ai/README.md
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/README.md b/src/modules/_shared/ai/README.md
deleted file mode 100644
--- a/src/modules/_shared/ai/README.md	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ /dev/null	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
@@ -1,214 +0,0 @@
-# AI Chat Assistant Integration
-
-This module provides a complete integration with the AI Chat Assistant backend API based on the OpenAPI specification.
-
-## Features
-
-- ✅ **Real API Integration**: Connects to the actual AI backend API
-- ✅ **Multiple Agent Types**: Support for leader_agent, ur_agent, and hlr_agent
-- ✅ **Multiple AI Models**: Support for gpt-4.1-mini, gpt-4.1, and gpt-o4
-- ✅ **Streaming Support**: Real-time streaming responses from the AI
-- ✅ **Settings Panel**: User-configurable agent types and models
-- ✅ **Error Handling**: Graceful fallback to mock responses when API is unavailable
-- ✅ **Type Safety**: Full TypeScript support with OpenAPI-based types
-- ✅ **Comprehensive Testing**: Unit tests, integration tests, and manual test runner
-
-## Architecture
-
-### Service Layer (`ai-assistant.service.ts`)
-- Handles all API communication
-- Transforms API responses to internal format
-- Provides streaming support
-- Includes error handling and fallbacks
-
-### Redux Integration
-- **Actions**: All AI-related actions including streaming support
-- **Reducer**: State management for conversations, messages, and UI state
-- **Saga**: Handles async operations and API calls
-
-### UI Components
-- **AIChatBox**: Main chat interface
-- **AISettings**: Configuration panel for agent types and models
-- **AIAssistant**: Tabbed interface combining chat and settings
-
-## API Endpoints
-
-Based on the OpenAPI specification:
-
-- `GET /v1/health` - Health check
-- `GET /v1/agents` - List available agents
-- `GET /v1/conversations` - List conversations
-- `POST /v1/conversations/{conversation_id}/messages` - Send message
-- `DELETE /v1/conversations/{conversation_id}` - Delete conversation
-
-## Configuration
-
-### Environment Variables
-
-Add to your `.env` file:
-```
-REACT_APP_API_AI_ASSISTANT=https://ai-assistant-api.ops-ai.dev
-```
-
-### Agent Types
-
-- **leader_agent**: Strategic analysis and high-level decision making
-- **ur_agent**: Specialized in user requirements analysis  
-- **hlr_agent**: Focus on high-level requirement specification
-
-### AI Models
-
-- **gpt-4.1-mini**: Fast and efficient for simple tasks
-- **gpt-4.1**: Balanced performance and capability
-- **gpt-o4**: Most advanced model for complex analysis
-
-## Usage
-
-### Basic Chat Integration
-
-```tsx
-import { AIChatBox } from './modules/_shared/ai/components/chatbox'
-
-function MyComponent() {
-  return (
-    <AIChatBox 
-      isVisible={true} 
-      onClose={() => {}} 
-    />
-  )
-}
-```
-
-### Settings Panel
-
-```tsx
-import AISettings from './modules/_shared/ai/components/ai-settings'
-
-function SettingsPage() {
-  return (
-    <AISettings 
-      onSettingsChange={(settings) => console.log(settings)} 
-    />
-  )
-}
-```
-
-### Redux Integration
-
-```tsx
-import { useDispatch } from 'react-redux'
-import { sendMessageRequest } from './modules/_shared/ai/actions'
-
-function sendMessage() {
-  dispatch(sendMessageRequest({
-    content: 'Hello AI',
-    conversationId: 'conv-123',
-    agentType: 'leader_agent',
-    projectId: 'my-project'
-  }))
-}
-```
-
-## Testing
-
-### Running Tests
-
-```bash
-# Run all AI tests
-npm test -- --testPathPattern=ai
-
-# Run specific test files
-npm test ai-assistant.service.test.ts
-npm test reducer.test.ts
-npm test integration.test.ts
-```
-
-### Manual Testing
-
-Use the built-in test runner:
-
-```typescript
-import { runAIIntegrationTests } from './modules/_shared/ai/test-runner'
-
-// Run all integration tests
-await runAIIntegrationTests()
-```
-
-Or in browser console:
-```javascript
-window.runAITests()
-```
-
-## Error Handling
-
-The integration includes multiple layers of error handling:
-
-1. **API Level**: Service layer catches and transforms API errors
-2. **Saga Level**: Fallback to mock responses when API is unavailable
-3. **UI Level**: User-friendly error messages and loading states
-
-## Streaming Support
-
-The integration supports real-time streaming responses:
-
-```typescript
-// Enable streaming in settings
-const settings = {
-  agentType: 'leader_agent',
-  model: 'gpt-4.1',
-  stream: true
-}
-
-// Messages will be streamed in real-time to the UI
-```
-
-## Development
-
-### Adding New Agent Types
-
-1. Update the `AgentType` enum in `types.ts`
-2. Add the new agent to the `AGENT_TYPES` array in `ai-settings.tsx`
-3. Update tests to include the new agent type
-
-### Adding New Models
-
-1. Update the `AvaiModel` enum in `types.ts`
-2. Add the new model to the `AI_MODELS` array in `ai-settings.tsx`
-3. Update tests to include the new model
-
-### Extending API Functionality
-
-1. Add new methods to `ai-assistant.service.ts`
-2. Create corresponding actions in `actions.ts`
-3. Update the reducer to handle new actions
-4. Add saga handlers for async operations
-5. Write tests for new functionality
-
-## Troubleshooting
-
-### API Connection Issues
-
-1. Check environment variables are set correctly
-2. Verify API endpoint is accessible
-3. Check network connectivity
-4. Review browser console for CORS issues
-
-### Authentication Issues
-
-1. Ensure access tokens are valid
-2. Check project code is set correctly
-3. Verify API permissions
-
-### Performance Issues
-
-1. Consider disabling streaming for slower connections
-2. Reduce message history length
-3. Optimize component re-renders
-
-## Contributing
-
-1. Follow the existing code structure
-2. Add tests for new functionality
-3. Update documentation
-4. Ensure TypeScript types are correct
-5. Test with both real API and fallback modes
Index: src/locales/en-US.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import request from './en-US/request'\nimport viewobject from './en-US/viewObject'\nimport actor from './en-US/actor'\nimport common from './en-US/commonlocale'\nimport msg from './en-US/message'\nimport viewobjectSpecification from './en-US/viewObjectSpecification'\nimport createObject from './en-US/createObject'\nimport updateObject from './en-US/updateObject'\nimport viewScreenList from './en-US/viewScreenList'\nimport viewUseCaseDetail from './en-US/viewUseCaseDetail'\nimport viewScreenDetails from './en-US/viewScreenDetails'\nimport viewFunction from './en-US/view-function'\nimport createFunction from './en-US/create-function'\nimport createScreen from './en-US/createScreen'\nimport workFlow from './en-US/viewWorkFlow'\nimport viewStateTransition from './en-US/view-state-transition'\nimport createStateTransition from './en-US/create-state-transition'\nimport mess from './en-US/mess'\nimport mail from './en-US/email'\nimport viewDataMigration from './en-US/view-data-migration'\nimport viewNonFunctional from './en-US/view-non-functional'\nimport createNonFunctional from './en-US/create-non-functional'\nimport userRequirement from './en-US/user-requirement'\nimport viewObjectRelationship from './en-US/view-object-relationship'\nimport meeting from './en-US/meeting'\nimport viewBusinessRule from './en-US/view-business-rule'\nimport viewUsecaseDiagram from './en-US/view-usecase-diagram'\nimport reference_document from './en-US/reference_document'\nimport viewOtherRequirement from './en-US/view-other-requirement'\nimport generateSrs from './en-US/generate-srs'\nimport dashboard from './en-US/dashboard'\nimport projectManagement from './en-US/project-management'\nimport common_committee from './en-US/common_committee'\nimport common_usercase from './en-US/common_usercase'\nimport commonComponent from './en-US/common-component'\nimport commonObject from './en-US/common-object'\nimport commonScreen from './en-US/common-screen'\nimport menu from './en-US/menu'\nimport myAssignedTask from './en-US/my_assigned_task'\nimport validate_srs from './en-US/validate-srs'\nimport select_common_component from './en-US/select-common-component'\nimport myPendingReviewTask from './en-US/my-pending-review-task'\nimport recommend_common_component from './en-US/recommend-common-component'\nimport related_links from './en-US/related-links'\nimport qualityReport from './en-US/quality-report'\nimport common_nonFunctionalRequirement from './en-US/common_non-functional-requirement'\nimport commonmessage from './en-US/common-message'\nimport effortEstimation from './en-US/effort_estimation'\nimport epicManagement from './en-US/epic-management'\nimport sprint_management from './en-US/sprint_management'\nimport user_story from './en-US/user-story'\nimport recommended from './en-US/recommenedcommonrequirement'\nimport glossary from './en-US/glossary'\nimport viewVersionHistory from './en-US/viewVersionHistory'\nexport default {\n  ...viewVersionHistory,\n  ...glossary,\n  ...recommended,\n  ...commonmessage,\n  ...projectManagement,\n  ...request,\n  ...viewobject,\n  ...actor,\n  ...msg,\n  ...common,\n  ...viewobjectSpecification,\n  ...createObject,\n  ...updateObject,\n  ...viewScreenList,\n  ...viewUseCaseDetail,\n  ...viewFunction,\n  ...createFunction,\n  ...createScreen,\n  ...viewScreenDetails,\n  ...workFlow,\n  ...viewStateTransition,\n  ...createStateTransition,\n  ...mess,\n  ...mail,\n  ...viewDataMigration,\n  ...viewNonFunctional,\n  ...createNonFunctional,\n  ...userRequirement,\n  ...viewObjectRelationship,\n  ...meeting,\n  ...viewBusinessRule,\n  ...viewUsecaseDiagram,\n  ...reference_document,\n  ...viewOtherRequirement,\n  ...generateSrs,\n  ...dashboard,\n  ...commonObject,\n  ...common_committee,\n  ...commonComponent,\n  ...commonScreen,\n  ...common_usercase,\n  ...common_nonFunctionalRequirement,\n  ...menu,\n  ...myAssignedTask,\n  ...myPendingReviewTask,\n  ...validate_srs,\n  ...select_common_component,\n  ...recommend_common_component,\n  ...related_links,\n  ...qualityReport,\n  ...effortEstimation,\n  ...epicManagement,\n  ...sprint_management,\n  ...user_story\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/locales/en-US.ts b/src/locales/en-US.ts
--- a/src/locales/en-US.ts	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/locales/en-US.ts	(date 1751346090160)
@@ -52,6 +52,7 @@
 import recommended from './en-US/recommenedcommonrequirement'
 import glossary from './en-US/glossary'
 import viewVersionHistory from './en-US/viewVersionHistory'
+import ai from './en-US/ai'
 export default {
   ...viewVersionHistory,
   ...glossary,
@@ -106,5 +107,6 @@
   ...effortEstimation,
   ...epicManagement,
   ...sprint_management,
-  ...user_story
+  ...user_story,
+  ...ai
 }
Index: src/services/ai-assistant.service.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { apiCall } from '../helper/api/aloApi'\nimport { API_URLS } from '../constants'\nimport { extractProjectCode } from '../helper/share'\nimport {\n  MessageRequest,\n  AgentType,\n  AvaiModel,\n  AIConversation,\n  AIMessage,\n  SendMessageRequest,\n  CreateConversationRequest,\n  TeamRunResponseContentEvent\n} from '../modules/_shared/ai/types'\n// Note: EventStreamService not needed since we're using fetch directly\n\nexport interface AIApiResponse<T = any> {\n  data: T\n  status: number\n  message?: string\n}\n\nexport interface ConversationResponse {\n  status_code: number\n  message: string\n  conversation_id: string\n  title: string\n  created_at: string\n}\n\nexport interface MessageResponse {\n  id: string\n  content: string\n  role: 'user' | 'assistant' | 'system'\n  timestamp: string\n  tokens?: number\n}\n\nexport interface ListConversationsResponse {\n  conversations: ConversationResponse[]\n  total: number\n  limit: number\n  offset: number\n}\n\nexport interface ListMessagesResponse {\n  messages: MessageResponse[]\n  total: number\n  limit: number\n  offset: number\n}\n\nexport interface SendMessageResponse {\n  message: MessageResponse\n  conversation_id: string\n  tokens_used: number\n}\n\nclass AIAssistantService {\n  private getProjectId(): string {\n    return extractProjectCode() || 'default-project'\n  }\n\n  /**\n   * Check AI API health\n   */\n  async checkHealth(): Promise<AIApiResponse> {\n    try {\n      const response = await apiCall('GET', API_URLS.AI_HEALTH)\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Health check failed: ${error.message}`)\n    }\n  }\n\n  /**\n   * Get available agents\n   */\n  async getAgents(): Promise<AIApiResponse<string[]>> {\n    try {\n      const response = await apiCall('GET', API_URLS.AI_AGENTS)\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to fetch agents: ${error.message}`)\n    }\n  }\n\n  /**\n   * List all conversations\n   */\n  async listConversations(limit: number = 50, offset: number = 0): Promise<AIApiResponse<ListConversationsResponse>> {\n    try {\n      const params = { limit, offset }\n      const response = await apiCall('GET', API_URLS.AI_CONVERSATIONS, params)\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to fetch conversations: ${error.message}`)\n    }\n  }\n\n  /**\n   * Create a new conversation\n   */\n  async createConversation(request: CreateConversationRequest): Promise<AIApiResponse<ConversationResponse>> {\n    try {\n      const requestBody = {\n        project_id: request.projectId\n      }\n\n      const response = await apiCall('POST', API_URLS.AI_CONVERSATIONS, requestBody)\n\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to create conversation: ${error.message}`)\n    }\n  }\n\n  /**\n   * Delete a conversation\n   */\n  async deleteConversation(conversationId: string): Promise<AIApiResponse> {\n    try {\n      const response = await apiCall('DELETE', API_URLS.AI_DELETE_CONVERSATION(conversationId))\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to delete conversation: ${error.message}`)\n    }\n  }\n\n  /**\n   * List messages in a conversation\n   */\n  async listMessages(\n    conversationId: string,\n    limit: number = 100,\n    offset: number = 0\n  ): Promise<AIApiResponse<ListMessagesResponse>> {\n    try {\n      const params = { limit, offset }\n      const response = await apiCall('GET', API_URLS.AI_CONVERSATION_MESSAGES(conversationId), params)\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to fetch messages: ${error.message}`)\n    }\n  }\n\n  /**\n   * Send a message to the AI assistant\n   */\n  async sendMessage(request: SendMessageRequest): Promise<AIApiResponse<any>> {\n    try {\n      const messageRequest: MessageRequest = {\n        message: request.content,\n        stream: request.stream ?? true,\n        model: request.model ?? 'gpt-4.1',\n        agent_type: request.agentType,\n        project_id: request.projectId\n      }\n\n      const response = await apiCall(\n        'POST',\n        API_URLS.AI_CONVERSATION_MESSAGES(request.conversationId),\n        messageRequest\n      )\n\n      return {\n        data: response.data,\n        status: response.status\n      }\n    } catch (error: any) {\n      throw new Error(`Failed to send message: ${error.message}`)\n    }\n  }\n\n  /**\n   * Send a streaming message to the AI assistant using Server-Sent Events\n   * The POST /v1/conversations/:id/messages endpoint returns an event stream directly\n   */\n  async sendStreamingMessage(\n    request: SendMessageRequest,\n    onChunk: (event: TeamRunResponseContentEvent) => void,\n    onComplete: () => void,\n    onError: (error: Error) => void\n  ): Promise<void> {\n    try {\n      const messageRequest: MessageRequest = {\n        message: request.content,\n        stream: true,\n        model: request.model ?? 'gpt-4.1',\n        agent_type: request.agentType,\n        project_id: request.projectId\n      }\n\n      // Create EventSource directly to the messages endpoint\n      const url = API_URLS.AI_CONVERSATION_MESSAGES(request.conversationId)\n\n      // We need to make a streaming request to the same endpoint\n      this.createStreamingRequest(url, messageRequest, onChunk, onComplete, onError)\n\n    } catch (error: any) {\n      onError(new Error(`Streaming failed: ${error.message}`))\n    }\n  }\n\n  /**\n   * Create a streaming request using fetch with ReadableStream\n   */\n  private async createStreamingRequest(\n    url: string,\n    messageRequest: MessageRequest,\n    onChunk: (event: TeamRunResponseContentEvent) => void,\n    onComplete: () => void,\n    onError: (error: Error) => void\n  ): Promise<void> {\n    try {\n      // Get auth token and project code for headers\n      const accessToken = localStorage.getItem('accessToken')\n      const projectCode = extractProjectCode()\n\n      const response = await fetch(url, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'ProjectCode': projectCode || '',\n          'Authentication': `Bearer ${accessToken}`,\n          'Authorization': `Bearer ${accessToken}`,\n          'Accept': 'text/event-stream',\n          'Cache-Control': 'no-cache'\n        },\n        body: JSON.stringify(messageRequest)\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`)\n      }\n\n      if (!response.body) {\n        throw new Error('No response body for streaming')\n      }\n\n      const reader = response.body.getReader()\n      const decoder = new TextDecoder()\n\n      try {\n        while (true) {\n          const { done, value } = await reader.read()\n\n          if (done) {\n            onComplete()\n            break\n          }\n\n          // Decode the chunk\n          const chunk = decoder.decode(value, { stream: true })\n\n          // Parse event stream data\n          this.parseEventStreamChunk(chunk, onChunk)\n        }\n      } finally {\n        reader.releaseLock()\n      }\n\n    } catch (error: any) {\n      onError(new Error(`Streaming request failed: ${error.message}`))\n    }\n  }\n\n  /**\n   * Parse event stream chunk and extract TeamRunResponseContent events\n   */\n  private parseEventStreamChunk(\n    chunk: string,\n    onChunk: (event: TeamRunResponseContentEvent) => void\n  ): void {\n    const lines = chunk.split('\\n')\n\n    for (const line of lines) {\n      if (line.startsWith('data: ')) {\n        try {\n          const jsonData = line.substring(6) // Remove 'data: ' prefix\n          const eventData = JSON.parse(jsonData) as TeamRunResponseContentEvent\n\n          // Only handle TeamRunResponseContent events\n          if (eventData.type === 'agent' && eventData.event === 'TeamRunResponseContent') {\n            onChunk(eventData)\n          }\n        } catch (parseError) {\n          console.warn('Failed to parse event line:', line, parseError)\n        }\n      }\n    }\n  }\n\n  /**\n   * Stop any active streaming connection\n   * Note: With fetch streams, the connection is managed by the reader\n   */\n  stopStreaming(): void {\n    // The streaming connection is managed by the fetch reader\n    // and will be automatically closed when the component unmounts\n    // or when the reader is released\n    console.log('Streaming stopped')\n  }\n\n  /**\n   * Legacy streaming method for backward compatibility\n   */\n  async sendStreamingMessageLegacy(\n    request: SendMessageRequest,\n    onChunk: (chunk: string) => void,\n    onComplete: (response: SendMessageResponse) => void,\n    onError: (error: Error) => void\n  ): Promise<void> {\n    try {\n      // For now, simulate streaming with a regular API call\n      // In a real implementation, this would use Server-Sent Events or WebSocket\n      const response = await this.sendMessage(request)\n\n      // Simulate streaming by sending chunks\n      const content = response.data.message.content\n      const chunks = content.split(' ')\n\n      for (let i = 0; i < chunks.length; i++) {\n        setTimeout(() => {\n          onChunk(chunks[i] + ' ')\n          if (i === chunks.length - 1) {\n            onComplete(response.data)\n          }\n        }, i * 100) // 100ms delay between chunks\n      }\n    } catch (error: any) {\n      onError(new Error(`Streaming failed: ${error.message}`))\n    }\n  }\n\n  /**\n   * Transform API conversation response to internal format\n   */\n  transformConversation(apiConversation: ConversationResponse): AIConversation {\n    return {\n      id: apiConversation.conversation_id,\n      title: apiConversation.title,\n      messages: [], // Messages will be loaded separately\n      totalTokens: 0,\n      createdAt: new Date(apiConversation.created_at),\n      updatedAt: new Date(apiConversation.created_at),\n      projectId: this.getProjectId()\n    }\n  }\n\n  /**\n   * Transform API message response to internal format\n   */\n  transformMessage(apiMessage: MessageResponse): AIMessage {\n    return {\n      id: apiMessage.id,\n      content: apiMessage.content,\n      type: apiMessage.role as 'user' | 'assistant' | 'system',\n      timestamp: new Date(apiMessage.timestamp),\n      tokens: apiMessage.tokens\n    }\n  }\n}\n\nexport default new AIAssistantService()\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/services/ai-assistant.service.ts b/src/services/ai-assistant.service.ts
--- a/src/services/ai-assistant.service.ts	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/services/ai-assistant.service.ts	(date 1751346090162)
@@ -11,6 +11,7 @@
   CreateConversationRequest,
   TeamRunResponseContentEvent
 } from '../modules/_shared/ai/types'
+import { EventStreamContentType, fetchEventSource } from '@microsoft/fetch-event-source'
 // Note: EventStreamService not needed since we're using fetch directly
 
 export interface AIApiResponse<T = any> {
@@ -228,13 +229,14 @@
     onChunk: (event: TeamRunResponseContentEvent) => void,
     onComplete: () => void,
     onError: (error: Error) => void
-  ): Promise<void> {
+  ): Promise<AbortController> {
+    const controller = new AbortController();
     try {
       // Get auth token and project code for headers
       const accessToken = localStorage.getItem('accessToken')
       const projectCode = extractProjectCode()
 
-      const response = await fetch(url, {
+      fetchEventSource(url, {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json',
@@ -244,42 +246,18 @@
           'Accept': 'text/event-stream',
           'Cache-Control': 'no-cache'
         },
-        body: JSON.stringify(messageRequest)
+        body: JSON.stringify(messageRequest),
+        signal: controller.signal,
+        onmessage: (event) => {
+          onChunk(JSON.parse(event.data) as TeamRunResponseContentEvent)
+        },
+        onclose: onComplete,
+        onerror: onError,
       })
-
-      if (!response.ok) {
-        throw new Error(`HTTP error! status: ${response.status}`)
-      }
-
-      if (!response.body) {
-        throw new Error('No response body for streaming')
-      }
-
-      const reader = response.body.getReader()
-      const decoder = new TextDecoder()
-
-      try {
-        while (true) {
-          const { done, value } = await reader.read()
-
-          if (done) {
-            onComplete()
-            break
-          }
-
-          // Decode the chunk
-          const chunk = decoder.decode(value, { stream: true })
-
-          // Parse event stream data
-          this.parseEventStreamChunk(chunk, onChunk)
-        }
-      } finally {
-        reader.releaseLock()
-      }
-
     } catch (error: any) {
       onError(new Error(`Streaming request failed: ${error.message}`))
     }
+    return controller;
   }
 
   /**
@@ -358,7 +336,9 @@
       id: apiConversation.conversation_id,
       title: apiConversation.title,
       messages: [], // Messages will be loaded separately
-      totalTokens: 0,
+      receiveToken: 0,
+      sentToken: 0,
+      totalCost: 0,
       createdAt: new Date(apiConversation.created_at),
       updatedAt: new Date(apiConversation.created_at),
       projectId: this.getProjectId()
Index: src/modules/_shared/ai/components/chatbox.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import React, { useEffect, useState, useRef } from 'react'\nimport { useDispatch, useSelector } from 'react-redux'\nimport {\n  Card,\n  Input,\n  Button,\n  Typography,\n  Space,\n  Progress,\n  Avatar,\n  Tooltip,\n  Dropdown,\n  Tag,\n  Spin,\n  Modal\n} from 'antd'\nimport {\n  RobotOutlined,\n  SendOutlined,\n  PaperClipOutlined,\n  MoreOutlined,\n  CloseOutlined,\n  UserOutlined,\n  ThunderboltOutlined,\n  FileTextOutlined,\n  PlusOutlined,\n  HistoryOutlined,\n  DeleteOutlined\n} from '@ant-design/icons'\nimport moment from 'moment'\nimport { AIAssistantState, AIMessage, DEFAULT_PROMPTS, AgentType } from '../types'\nimport {\n  sendMessageRequest,\n  fetchConversationsRequest,\n  createConversationRequest,\n  setCurrentConversation,\n  clearSuggestions,\n  deleteConversationRequest\n} from '../actions'\nimport AppState from '../../../../store/types'\nimport { extractProjectCode } from '../../../../helper/share'\nimport { MessageContent } from './message-content'\nimport './styles.less'\n\nconst { Text, Title } = Typography\nconst { TextArea } = Input\n\ninterface AIAssistantProps {\n  isVisible: boolean\n  onClose: () => void\n  hideHeader?: boolean\n}\n\nexport const AIChatBox: React.FC<AIAssistantProps> = ({ isVisible, onClose, hideHeader = false }) => {\n  const dispatch = useDispatch()\n  const aiState = useSelector<AppState>((state) => state?.aiAssistant) as AIAssistantState\n  const [messageInput, setMessageInput] = useState('')\n  const [selectedPrompt, setSelectedPrompt] = useState<string>('')\n  const [showConversationModal, setShowConversationModal] = useState(false)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n\n  // Load AI settings from localStorage\n  const getAISettings = () => {\n    try {\n      const saved = localStorage.getItem('ai-assistant-settings')\n      return saved ? JSON.parse(saved) : {\n        agentType: 'master_agent' as AgentType,\n        model: 'gpt-4.1' as const,\n        stream: true\n      }\n    } catch {\n      return {\n        agentType: 'master_agent' as AgentType,\n        model: 'gpt-4.1' as const,\n        stream: true\n      }\n    }\n  }\n\n  useEffect(() => {\n    if (isVisible) {\n      dispatch(fetchConversationsRequest())\n    }\n  }, [isVisible, dispatch])\n\n  useEffect(() => {\n    // Create default conversation if none exists and we're visible\n    if (isVisible && aiState.conversations.length === 0 && !aiState.isLoading) {\n      dispatch(createConversationRequest({\n        title: 'New Chat',\n        projectId: extractProjectCode() || 'default-project'\n      }))\n    }\n  }, [isVisible, aiState.conversations.length, aiState.isLoading, dispatch])\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [aiState.currentConversation?.messages])\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  const handleSendMessage = () => {\n    if (messageInput.trim() && aiState.currentConversation?.id) {\n      const content = selectedPrompt\n        ? `${selectedPrompt}\\n\\n${messageInput.trim()}`\n        : messageInput.trim()\n\n      const settings = getAISettings()\n\n      dispatch(sendMessageRequest({\n        content,\n        conversationId: aiState.currentConversation.id,\n        agentType: settings.agentType,\n        model: settings.model,\n        projectId: extractProjectCode() || 'default-project',\n        stream: settings.stream\n      }))\n      setMessageInput('')\n      setSelectedPrompt('')\n      dispatch(clearSuggestions())\n    }\n  }\n\n  const handlePromptSelect = (prompt: string) => {\n    setSelectedPrompt(prompt)\n    setMessageInput('')\n  }\n\n  const handleNewConversation = () => {\n    dispatch(createConversationRequest({\n      title: 'New Chat',\n      projectId: extractProjectCode() || 'default-project'\n    }))\n  }\n\n  const handleSelectConversation = (conversationId: string) => {\n    dispatch(setCurrentConversation(conversationId))\n    setShowConversationModal(false)\n  }\n\n  const handleDeleteConversation = (conversationId: string) => {\n    dispatch(deleteConversationRequest(conversationId))\n  }\n\n  const formatMessageTime = (timestamp: Date) => {\n    const now = moment()\n    const messageTime = moment(timestamp)\n\n    if (now.diff(messageTime, 'minutes') < 1) {\n      return 'now'\n    } else if (now.diff(messageTime, 'hours') < 1) {\n      return `${now.diff(messageTime, 'minutes')}m ago`\n    } else if (now.diff(messageTime, 'days') === 0) {\n      return messageTime.format('HH:mm')\n    } else {\n      return messageTime.format('DD/MM HH:mm')\n    }\n  }\n\n  const renderMessage = (message: AIMessage) => {\n    const isUser = message.type === 'user'\n\n    return (\n      <div\n        key={message.id}\n        className={`ai-message ${isUser ? 'user-message' : 'assistant-message'}`}\n      >\n        <div className=\"message-header\">\n          <Avatar\n            size=\"small\"\n            icon={isUser ? <UserOutlined /> : <RobotOutlined />}\n            className={isUser ? 'user-avatar' : 'ai-avatar'}\n          />\n          <div className=\"message-meta\">\n            {!isUser && message.agentName && (\n              <Text strong className=\"agent-name\">\n                {message.agentName}\n              </Text>\n            )}\n            {!isUser && message.teamName && message.teamName !== message.agentName && (\n              <Text type=\"secondary\" className=\"team-name\">\n                ({message.teamName})\n              </Text>\n            )}\n            <Text type=\"secondary\" className=\"message-time\">\n              {formatMessageTime(message.timestamp)}\n            </Text>\n          </div>\n          {message.tokens && (\n            <Tag color=\"blue\">\n              {message.tokens} tokens\n            </Tag>\n          )}\n        </div>\n        <div className=\"message-content\">\n          <MessageContent content={message.content} />\n        </div>\n      </div>\n    )\n  }\n\n  const tokenUsagePercentage = (aiState.totalTokensUsed / aiState.availableTokens) * 100\n\n  if (!isVisible) return null\n\n  return (\n    <div className=\"ai-assistant-container\">\n      <Card className=\"ai-assistant-card\" bodyStyle={{ padding: 0 }}>\n        {/* Header - only show when not in tabbed interface */}\n        {!hideHeader && (\n          <div className=\"ai-assistant-header\">\n            <div className=\"header-left\">\n              <RobotOutlined className=\"ai-icon\" />\n              <Title level={5} style={{ margin: 0, color: 'white' }}>\n                BA Vista\n              </Title>\n            </div>\n            <div className=\"header-right\">\n              <Tooltip title=\"Settings\">\n                <Button\n                  type=\"text\"\n                  icon={<MoreOutlined />}\n                  style={{ color: 'white' }}\n                />\n              </Tooltip>\n              <Tooltip title=\"Close\">\n                <Button\n                  type=\"text\"\n                  icon={<CloseOutlined />}\n                  onClick={onClose}\n                  style={{ color: 'white' }}\n                />\n              </Tooltip>\n            </div>\n          </div>\n        )}\n\n        {/* Current Task */}\n        <div className=\"current-task\">\n          <Title level={5}>{aiState.currentConversation?.title ?? \"New Chat\"}</Title>\n          <div className=\"task-actions\">\n            <Tooltip title=\"New Conversation\" placement=\"topRight\">\n              <Button\n                type=\"text\"\n                icon={<PlusOutlined />}\n                size=\"small\"\n                onClick={handleNewConversation}\n              />\n            </Tooltip>\n            <Tooltip title=\"Conversation History\" placement=\"topRight\">\n              <Button\n                type=\"text\"\n                icon={<HistoryOutlined />}\n                size=\"small\"\n                onClick={() => setShowConversationModal(true)}\n              />\n            </Tooltip>\n          </div>\n        </div>\n\n\n\n        {/* Token Usage */}\n        <div className=\"token-usage\">\n          <div className=\"token-info\">\n            <Text strong>Token:</Text>\n            <Space>\n              <ThunderboltOutlined style={{ color: '#52c41a' }} />\n              <Text>{aiState.totalTokensUsed}</Text>\n              <ThunderboltOutlined style={{ color: '#ff4d4f' }} />\n              <Text>{aiState.availableTokens - aiState.totalTokensUsed}</Text>\n            </Space>\n            <Text strong>{aiState.availableTokens - aiState.totalTokensUsed} USD</Text>\n          </div>\n          <Progress\n            percent={tokenUsagePercentage}\n            size=\"small\"\n            strokeColor={tokenUsagePercentage > 80 ? '#ff4d4f' : '#1890ff'}\n            showInfo={false}\n          />\n        </div>\n\n\n\n        {/* Messages */}\n        <div className=\"ai-messages\">\n          {aiState.currentConversation?.messages.length === 0 && !aiState.isTyping ? (\n            <div className=\"empty-conversation\">\n              <div className=\"empty-conversation-content\">\n                <RobotOutlined className=\"empty-icon\" />\n                <Title level={4}>Welcome to BA Vista AI Assistant</Title>\n                <Text type=\"secondary\">\n                  I'm here to help you analyze requirements, generate documentation, and improve your business analysis workflow.\n                </Text>\n                <div className=\"suggested-prompts\">\n                  <Text strong>Try asking me to:</Text>\n                  <ul>\n                    <li>Analyze this requirement for completeness</li>\n                    <li>Generate use case scenarios</li>\n                    <li>Review this document for clarity</li>\n                    <li>Create test cases for this feature</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          ) : (\n            <>\n              {aiState.currentConversation?.messages.map(renderMessage)}\n              {aiState.isTyping && (\n                <div className=\"ai-message assistant-message\">\n                  <div className=\"message-header\">\n                    <Avatar size=\"small\" icon={<RobotOutlined />} className=\"ai-avatar\" />\n                    <Text type=\"secondary\">AI is thinking...</Text>\n                  </div>\n                  <div className=\"message-content\">\n                    <Spin size=\"small\" />\n                  </div>\n                </div>\n              )}\n            </>\n          )}\n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Quick Actions */}\n        {aiState.suggestions.length > 0 && (\n          <div className=\"quick-actions\">\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>Suggestions:</Text>\n            <Space wrap>\n              {aiState.suggestions.map((suggestion, index) => (\n                <Button\n                  key={index}\n                  size=\"small\"\n                  type=\"dashed\"\n                  onClick={() => setMessageInput(suggestion)}\n                >\n                  {suggestion}\n                </Button>\n              ))}\n            </Space>\n          </div>\n        )}\n\n        {/* Input Area */}\n        <div className=\"ai-input-area\">\n          {selectedPrompt && (\n            <div className=\"selected-prompt\">\n              <Tag color=\"blue\" closable onClose={() => setSelectedPrompt('')}>\n                <FileTextOutlined /> {DEFAULT_PROMPTS.find(p => p.prompt === selectedPrompt)?.title || 'Custom Prompt'}\n              </Tag>\n            </div>\n          )}\n\n          <div className=\"input-container\">\n            <TextArea\n              value={messageInput}\n              onChange={(e) => setMessageInput(e.target.value)}\n              placeholder={selectedPrompt ? \"Add your specific requirements...\" : \"Input a prompt, or type @ to call reference doc/ artefacts\"}\n              autoSize={{ minRows: 1, maxRows: 4 }}\n              onPressEnter={(e) => {\n                if (!e.shiftKey) {\n                  e.preventDefault()\n                  handleSendMessage()\n                }\n              }}\n              className=\"message-input\"\n            />\n            <div className=\"input-actions\">\n              <Dropdown\n                menu={{\n                  items: DEFAULT_PROMPTS.map(prompt => ({\n                    key: prompt.prompt,\n                    label: (\n                      <div>\n                        <Text strong>{prompt.title}</Text>\n                        <br />\n                        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                          {prompt.description} ({prompt.tokens} tokens)\n                        </Text>\n                      </div>\n                    )\n                  })),\n                  onClick: ({ key }) => handlePromptSelect(key as string)\n                }}\n                trigger={['click']}\n              >\n                <Button\n                  type=\"text\"\n                  icon={<FileTextOutlined />}\n                  size=\"small\"\n                />\n              </Dropdown>\n              <Button\n                type=\"text\"\n                icon={<PaperClipOutlined />}\n                size=\"small\"\n              />\n              <Button\n                type=\"primary\"\n                icon={<SendOutlined />}\n                onClick={handleSendMessage}\n                disabled={!messageInput.trim() && !selectedPrompt}\n                size=\"small\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Conversation History Modal */}\n        <Modal\n          title=\"Conversation History\"\n          open={showConversationModal}\n          onCancel={() => setShowConversationModal(false)}\n          footer={null}\n          width={600}\n          className=\"conversation-history-modal\"\n        >\n          <div className=\"conversation-modal-content\">\n            {aiState.conversations.length === 0 ? (\n              <div className=\"empty-conversations\">\n                <Text type=\"secondary\">No conversations found</Text>\n              </div>\n            ) : (\n              <div className=\"conversation-items\">\n                {aiState.conversations.map((conversation) => (\n                  <div\n                    key={conversation.id}\n                    className={`conversation-item ${conversation.id === aiState.currentConversation?.id ? 'active' : ''}`}\n                    onClick={() => handleSelectConversation(conversation.id)}\n                  >\n                    <div className=\"conversation-info\">\n                      <Text className=\"conversation-title\">{conversation.title}</Text>\n                      <Text type=\"secondary\" className=\"conversation-time\">\n                        {moment(conversation.updatedAt).format('DD/MM HH:mm')}\n                      </Text>\n                    </div>\n                    <Button\n                      type=\"text\"\n                      icon={<DeleteOutlined />}\n                      size=\"small\"\n                      onClick={(e) => {\n                        e.stopPropagation()\n                        handleDeleteConversation(conversation.id)\n                      }}\n                      className=\"delete-conversation-btn\"\n                      danger\n                    />\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </Modal>\n      </Card>\n    </div>\n  )\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/components/chatbox.tsx b/src/modules/_shared/ai/components/chatbox.tsx
--- a/src/modules/_shared/ai/components/chatbox.tsx	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/modules/_shared/ai/components/chatbox.tsx	(date 1751346090161)
@@ -21,11 +21,12 @@
   MoreOutlined,
   CloseOutlined,
   UserOutlined,
-  ThunderboltOutlined,
   FileTextOutlined,
   PlusOutlined,
   HistoryOutlined,
-  DeleteOutlined
+  DeleteOutlined,
+  ArrowUpOutlined,
+  ArrowDownOutlined
 } from '@ant-design/icons'
 import moment from 'moment'
 import { AIAssistantState, AIMessage, DEFAULT_PROMPTS, AgentType } from '../types'
@@ -41,6 +42,7 @@
 import { extractProjectCode } from '../../../../helper/share'
 import { MessageContent } from './message-content'
 import './styles.less'
+import intl from '../../../../config/locale.config'
 
 const { Text, Title } = Typography
 const { TextArea } = Input
@@ -161,6 +163,7 @@
 
   const renderMessage = (message: AIMessage) => {
     const isUser = message.type === 'user'
+    if (!message.content) return
 
     return (
       <div
@@ -174,16 +177,19 @@
             className={isUser ? 'user-avatar' : 'ai-avatar'}
           />
           <div className="message-meta">
-            {!isUser && message.agentName && (
-              <Text strong className="agent-name">
-                {message.agentName}
-              </Text>
-            )}
-            {!isUser && message.teamName && message.teamName !== message.agentName && (
-              <Text type="secondary" className="team-name">
-                ({message.teamName})
-              </Text>
+            {!isUser && (
+              <>
+                <Text strong className="agent-name">
+                  {intl.formatMessage({ id: 'ai.agent.name' })}
+                </Text>
+                {message.teamName && (
+                  <Text type="secondary" className="team-name">
+                    ({message.teamName})
+                  </Text>
+                )}
+              </>
             )}
+
             <Text type="secondary" className="message-time">
               {formatMessageTime(message.timestamp)}
             </Text>
@@ -201,8 +207,6 @@
     )
   }
 
-  const tokenUsagePercentage = (aiState.totalTokensUsed / aiState.availableTokens) * 100
-
   if (!isVisible) return null
 
   return (
@@ -214,7 +218,7 @@
             <div className="header-left">
               <RobotOutlined className="ai-icon" />
               <Title level={5} style={{ margin: 0, color: 'white' }}>
-                BA Vista
+                {intl.formatMessage({ id: 'ai.agent.name' })}
               </Title>
             </div>
             <div className="header-right">
@@ -267,19 +271,17 @@
           <div className="token-info">
             <Text strong>Token:</Text>
             <Space>
-              <ThunderboltOutlined style={{ color: '#52c41a' }} />
-              <Text>{aiState.totalTokensUsed}</Text>
-              <ThunderboltOutlined style={{ color: '#ff4d4f' }} />
-              <Text>{aiState.availableTokens - aiState.totalTokensUsed}</Text>
+              <Tooltip title={intl.formatMessage({ id: 'ai.send-token.description' })}>
+                <ArrowUpOutlined />
+                <Text>{aiState.currentConversation?.sentToken}</Text>
+              </Tooltip>
+              <Tooltip title={intl.formatMessage({ id: 'ai.receive-token.description' })}>
+                <ArrowDownOutlined />
+                <Text>{aiState.currentConversation?.receiveToken}</Text>
+              </Tooltip>
             </Space>
-            <Text strong>{aiState.availableTokens - aiState.totalTokensUsed} USD</Text>
+            <Text strong>{aiState.currentConversation?.totalCost} USD</Text>
           </div>
-          <Progress
-            percent={tokenUsagePercentage}
-            size="small"
-            strokeColor={tokenUsagePercentage > 80 ? '#ff4d4f' : '#1890ff'}
-            showInfo={false}
-          />
         </div>
 
 
@@ -290,19 +292,10 @@
             <div className="empty-conversation">
               <div className="empty-conversation-content">
                 <RobotOutlined className="empty-icon" />
-                <Title level={4}>Welcome to BA Vista AI Assistant</Title>
+                <Title level={4}>{intl.formatMessage({ id: 'ai.welcome-message.title' })}</Title>
                 <Text type="secondary">
-                  I'm here to help you analyze requirements, generate documentation, and improve your business analysis workflow.
+                  {intl.formatMessage({ id: 'ai.welcome-message.description' })}
                 </Text>
-                <div className="suggested-prompts">
-                  <Text strong>Try asking me to:</Text>
-                  <ul>
-                    <li>Analyze this requirement for completeness</li>
-                    <li>Generate use case scenarios</li>
-                    <li>Review this document for clarity</li>
-                    <li>Create test cases for this feature</li>
-                  </ul>
-                </div>
               </div>
             </div>
           ) : (
@@ -368,30 +361,6 @@
               className="message-input"
             />
             <div className="input-actions">
-              <Dropdown
-                menu={{
-                  items: DEFAULT_PROMPTS.map(prompt => ({
-                    key: prompt.prompt,
-                    label: (
-                      <div>
-                        <Text strong>{prompt.title}</Text>
-                        <br />
-                        <Text type="secondary" style={{ fontSize: '12px' }}>
-                          {prompt.description} ({prompt.tokens} tokens)
-                        </Text>
-                      </div>
-                    )
-                  })),
-                  onClick: ({ key }) => handlePromptSelect(key as string)
-                }}
-                trigger={['click']}
-              >
-                <Button
-                  type="text"
-                  icon={<FileTextOutlined />}
-                  size="small"
-                />
-              </Dropdown>
               <Button
                 type="text"
                 icon={<PaperClipOutlined />}
Index: src/modules/_shared/ai/reducer.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { createReducer } from '@reduxjs/toolkit'\nimport { AIAssistantState } from './types'\nimport {\n  setCurrentConversation,\n  fetchConversationsRequest,\n  fetchConversationsSuccess,\n  fetchConversationsFailure,\n  createConversationRequest,\n  createConversationSuccess,\n  createConversationFailure,\n  deleteConversationRequest,\n  deleteConversationSuccess,\n  deleteConversationFailure,\n  sendMessageRequest,\n  sendMessageSuccess,\n  sendMessageFailure,\n  receiveAIResponse,\n  startStreamingResponse,\n  receiveStreamingChunk,\n  endStreamingResponse,\n  streamingError,\n  updateTokenUsage,\n  fetchTokenUsageRequest,\n  fetchTokenUsageSuccess,\n  fetchTokenUsageFailure,\n  setAITyping,\n  setSuggestions,\n  clearSuggestions,\n  clearAIError,\n  toggleAIChatPanel,\n  toggleAISetingsPanel,\n  setActiveTab,\n  closeAllPanels\n} from './actions'\n\nconst initialState: AIAssistantState = {\n  isOpen: false,\n  isLoading: false,\n  currentConversation: undefined,\n  conversations: [],\n  totalTokensUsed: 0,\n  availableTokens: 1000, // Default token limit\n  error: undefined,\n  isTyping: false,\n  suggestions: [],\n  // UI Panel States\n  isAiPanelOpen: false,\n  activeTab: null\n}\n\nconst handleTogglePanel = (tab: AIAssistantState[\"activeTab\"]) => (state: AIAssistantState) => {\n  const wasOpen = state.isAiPanelOpen\n  state.isAiPanelOpen = !state.isAiPanelOpen\n  state.activeTab = tab\n}\n\nexport const aiAssistantReducer = createReducer(initialState, (builder) => {\n  builder\n    // UI Actions\n    .addCase(setCurrentConversation, (state, action) => {\n      const conversation = state.conversations.find(c => c.id === action.payload)\n      if (conversation) {\n        state.currentConversation = conversation\n      }\n    })\n\n    // Conversation Actions\n    .addCase(fetchConversationsRequest, (state) => {\n      state.isLoading = true\n      state.error = undefined\n    })\n    .addCase(fetchConversationsSuccess, (state, action) => {\n      state.isLoading = false\n      state.conversations = action.payload\n    })\n    .addCase(fetchConversationsFailure, (state, action) => {\n      state.isLoading = false\n      state.error = action.payload\n    })\n\n    .addCase(createConversationRequest, (state) => {\n      state.isLoading = true\n      state.error = undefined\n    })\n    .addCase(createConversationSuccess, (state, action) => {\n      state.isLoading = false\n      state.conversations.unshift(action.payload)\n      state.currentConversation = action.payload\n    })\n    .addCase(createConversationFailure, (state, action) => {\n      state.isLoading = false\n      state.error = action.payload\n    })\n\n    .addCase(deleteConversationSuccess, (state, action) => {\n      state.conversations = state.conversations.filter(c => c.id !== action.payload)\n      if (state.currentConversation?.id === action.payload) {\n        state.currentConversation = state.conversations[0] || undefined\n      }\n    })\n\n    // Message Actions\n    .addCase(sendMessageRequest, (state) => {\n      state.isLoading = true\n      state.error = undefined\n    })\n    .addCase(sendMessageSuccess, (state, action) => {\n      state.isLoading = false\n      if (state.currentConversation) {\n        state.currentConversation.messages.push(action.payload)\n        state.currentConversation.updatedAt = action.payload.timestamp\n\n        // Update conversation in the list\n        const conversationIndex = state.conversations.findIndex(\n          c => c.id === state.currentConversation?.id\n        )\n        if (conversationIndex !== -1) {\n          state.conversations[conversationIndex] = state.currentConversation\n        }\n      }\n    })\n    .addCase(sendMessageFailure, (state, action) => {\n      state.isLoading = false\n      state.error = action.payload\n    })\n\n    .addCase(receiveAIResponse, (state, action) => {\n      state.isTyping = false\n      if (state.currentConversation) {\n        const aiMessage = {\n          id: Date.now().toString(),\n          content: action.payload.content,\n          type: 'assistant' as const,\n          timestamp: new Date(),\n          tokens: action.payload.tokens\n        }\n\n        state.currentConversation.messages.push(aiMessage)\n        state.currentConversation.totalTokens += action.payload.tokens\n        state.currentConversation.updatedAt = aiMessage.timestamp\n        state.totalTokensUsed += action.payload.tokens\n\n        // Update suggestions if provided\n        if (action.payload.suggestions) {\n          state.suggestions = action.payload.suggestions\n        }\n\n        // Update conversation in the list\n        const conversationIndex = state.conversations.findIndex(\n          c => c.id === state.currentConversation?.id\n        )\n        if (conversationIndex !== -1) {\n          state.conversations[conversationIndex] = state.currentConversation\n        }\n      }\n    })\n\n    // Streaming Actions\n    .addCase(startStreamingResponse, (state, action) => {\n      state.isTyping = true\n      if (state.currentConversation) {\n        const streamingMessage = {\n          id: action.payload,\n          content: '',\n          type: 'assistant' as const,\n          timestamp: new Date(),\n          isLoading: true\n        }\n        state.currentConversation.messages.push(streamingMessage)\n      }\n    })\n    .addCase(receiveStreamingChunk, (state, action) => {\n      if (state.currentConversation) {\n        const messageIndex = state.currentConversation.messages.findIndex(\n          m => m.id === action.payload.messageId\n        )\n        if (messageIndex !== -1) {\n          const message = state.currentConversation.messages[messageIndex]\n          message.content += action.payload.chunk\n\n          // Update agent information if provided\n          if (action.payload.agentName) {\n            message.agentName = action.payload.agentName\n          }\n          if (action.payload.runId) {\n            message.runId = action.payload.runId\n          }\n          if (action.payload.teamId) {\n            message.teamId = action.payload.teamId\n          }\n          if (action.payload.teamName) {\n            message.teamName = action.payload.teamName\n          }\n        }\n      }\n    })\n    .addCase(endStreamingResponse, (state, action) => {\n      state.isTyping = false\n      if (state.currentConversation) {\n        const messageIndex = state.currentConversation.messages.findIndex(\n          m => m.id === action.payload.messageId\n        )\n        if (messageIndex !== -1) {\n          state.currentConversation.messages[messageIndex].isLoading = false\n          state.currentConversation.messages[messageIndex].tokens = action.payload.totalTokens\n          state.currentConversation.totalTokens += action.payload.totalTokens\n          state.totalTokensUsed += action.payload.totalTokens\n          state.currentConversation.updatedAt = new Date()\n        }\n      }\n    })\n    .addCase(streamingError, (state, action) => {\n      state.isTyping = false\n      state.error = action.payload.error\n      if (state.currentConversation) {\n        const messageIndex = state.currentConversation.messages.findIndex(\n          m => m.id === action.payload.messageId\n        )\n        if (messageIndex !== -1) {\n          state.currentConversation.messages[messageIndex].isLoading = false\n          state.currentConversation.messages[messageIndex].content = 'Error: Failed to receive response'\n        }\n      }\n    })\n\n    // Token Actions\n    .addCase(updateTokenUsage, (state, action) => {\n      state.totalTokensUsed = action.payload.used\n      state.availableTokens = action.payload.available\n    })\n    .addCase(fetchTokenUsageSuccess, (state, action) => {\n      state.totalTokensUsed = action.payload.used\n      state.availableTokens = action.payload.available\n    })\n\n    // Typing Actions\n    .addCase(setAITyping, (state, action) => {\n      state.isTyping = action.payload\n    })\n\n    // Suggestions Actions\n    .addCase(setSuggestions, (state, action) => {\n      state.suggestions = action.payload\n    })\n    .addCase(clearSuggestions, (state) => {\n      state.suggestions = []\n    })\n\n    // Error Actions\n    .addCase(clearAIError, (state) => {\n      state.error = undefined\n    })\n    .addCase(toggleAIChatPanel, handleTogglePanel('ai-chat'))\n    .addCase(toggleAISetingsPanel, handleTogglePanel('settings'))\n    .addCase(setActiveTab, (state, action) => {\n      state.activeTab = action.payload\n    })\n    .addCase(closeAllPanels, (state) => {\n      state.isAiPanelOpen = false\n      state.activeTab = null\n    })\n})\n\nexport { initialState as AIState }\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/reducer.ts b/src/modules/_shared/ai/reducer.ts
--- a/src/modules/_shared/ai/reducer.ts	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/modules/_shared/ai/reducer.ts	(date 1751346090161)
@@ -1,5 +1,5 @@
 import { createReducer } from '@reduxjs/toolkit'
-import { AIAssistantState } from './types'
+import { AIAssistantState, AIErrorState } from './types'
 import {
   setCurrentConversation,
   fetchConversationsRequest,
@@ -38,9 +38,15 @@
   isLoading: false,
   currentConversation: undefined,
   conversations: [],
-  totalTokensUsed: 0,
-  availableTokens: 1000, // Default token limit
+  sentToken: 0,
+  receiveToken: 0,
+  totalCost: 0,
   error: undefined,
+  errorState: {
+    current: undefined,
+    history: [],
+    isRetrying: false
+  },
   isTyping: false,
   suggestions: [],
   // UI Panel States
@@ -92,12 +98,20 @@
       state.error = action.payload
     })
 
+    .addCase(deleteConversationRequest, (state) => {
+      state.isLoading = true
+      state.error = undefined
+    })
     .addCase(deleteConversationSuccess, (state, action) => {
       state.conversations = state.conversations.filter(c => c.id !== action.payload)
       if (state.currentConversation?.id === action.payload) {
         state.currentConversation = state.conversations[0] || undefined
       }
     })
+    .addCase(deleteConversationFailure, (state, action) => {
+      state.isLoading = false
+      state.error = action.payload
+    })
 
     // Message Actions
     .addCase(sendMessageRequest, (state) => {
@@ -135,10 +149,9 @@
           tokens: action.payload.tokens
         }
 
+        // TODO: Add token usage change
         state.currentConversation.messages.push(aiMessage)
-        state.currentConversation.totalTokens += action.payload.tokens
         state.currentConversation.updatedAt = aiMessage.timestamp
-        state.totalTokensUsed += action.payload.tokens
 
         // Update suggestions if provided
         if (action.payload.suggestions) {
@@ -202,10 +215,8 @@
         )
         if (messageIndex !== -1) {
           state.currentConversation.messages[messageIndex].isLoading = false
-          state.currentConversation.messages[messageIndex].tokens = action.payload.totalTokens
-          state.currentConversation.totalTokens += action.payload.totalTokens
-          state.totalTokensUsed += action.payload.totalTokens
           state.currentConversation.updatedAt = new Date()
+          // TODO: Add token usage change
         }
       }
     })
@@ -225,12 +236,10 @@
 
     // Token Actions
     .addCase(updateTokenUsage, (state, action) => {
-      state.totalTokensUsed = action.payload.used
-      state.availableTokens = action.payload.available
+      // TODO: Add token usage
     })
     .addCase(fetchTokenUsageSuccess, (state, action) => {
-      state.totalTokensUsed = action.payload.used
-      state.availableTokens = action.payload.available
+      // TODO: Add token usage
     })
 
     // Typing Actions
Index: src/modules/_shared/ai/saga.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { Action } from '@reduxjs/toolkit'\nimport { all, call, fork, put, takeLatest, delay, take } from 'redux-saga/effects'\nimport { eventChannel, EventChannel } from 'redux-saga'\nimport { ShowAppMessage } from '../../../helper/share'\nimport { MESSAGE_TYPE } from '../../../constants'\nimport { extractProjectCode } from '../../../helper/share'\nimport aiAssistantService from '../../../services/ai-assistant.service'\nimport {\n  fetchConversationsRequest,\n  fetchConversationsSuccess,\n  fetchConversationsFailure,\n  createConversationRequest,\n  createConversationSuccess,\n  createConversationFailure,\n  deleteConversationRequest,\n  deleteConversationSuccess,\n  deleteConversationFailure,\n  sendMessageRequest,\n  sendMessageSuccess,\n  sendMessageFailure,\n  receiveAIResponse,\n  startStreamingResponse,\n  receiveStreamingChunk,\n  endStreamingResponse,\n  streamingError,\n  fetchTokenUsageRequest,\n  fetchTokenUsageSuccess,\n  fetchTokenUsageFailure,\n  setAITyping,\n  setSuggestions\n} from './actions'\nimport {\n  AIConversation,\n  AIMessage,\n  SendMessageRequest,\n  CreateConversationRequest,\n  AIResponse,\n  TokenUsage,\n  AgentType,\n  TeamRunResponseContentEvent\n} from './types'\n\n// Default agent type and project configuration\nconst getDefaultAgentType = (): AgentType => 'master_agent'\nconst getProjectId = (): string => extractProjectCode() || 'default-project'\n\n// Create event channel for streaming from POST /v1/conversations/:id/messages\nfunction createStreamingChannel(apiRequest: SendMessageRequest): EventChannel<any> {\n  return eventChannel(emitter => {\n    // Start the streaming request\n    aiAssistantService.sendStreamingMessage(\n      apiRequest,\n      // onChunk callback\n      (event: TeamRunResponseContentEvent) => {\n        emitter({\n          type: 'CHUNK',\n          payload: event\n        })\n      },\n      // onComplete callback\n      () => {\n        emitter({\n          type: 'COMPLETE'\n        })\n        emitter('END')\n      },\n      // onError callback\n      (error: Error) => {\n        emitter({\n          type: 'ERROR',\n          payload: error\n        })\n        emitter('END')\n      }\n    )\n\n    // Return unsubscribe function\n    return () => {\n      aiAssistantService.stopStreaming()\n    }\n  })\n}\n\n// Streaming response handler saga\nfunction* handleStreamingResponse(apiRequest: SendMessageRequest, streamingMessageId: string) {\n  try {\n    const streamingChannel = yield call(createStreamingChannel, apiRequest)\n\n    while (true) {\n      const action = yield take(streamingChannel)\n\n      if (action.type === 'CHUNK') {\n        const event = action.payload as TeamRunResponseContentEvent\n        yield put(receiveStreamingChunk({\n          messageId: streamingMessageId,\n          chunk: event.content,\n          agentName: event.agent_name,\n          runId: event.run_id,\n          teamId: event.team_id,\n          teamName: event.team_name\n        }))\n      } else if (action.type === 'COMPLETE') {\n        yield put(endStreamingResponse({\n          messageId: streamingMessageId,\n          totalTokens: 0 // Will be updated when we get token info\n        }))\n        break\n      } else if (action.type === 'ERROR') {\n        yield put(streamingError({\n          messageId: streamingMessageId,\n          error: action.payload.message\n        }))\n        break\n      }\n    }\n\n    streamingChannel.close()\n\n  } catch (error: any) {\n    yield put(streamingError({\n      messageId: streamingMessageId,\n      error: error.message\n    }))\n  }\n}\n\n// Mock data generators for development\nconst generateMockConversations = (): AIConversation[] => [\n  {\n    id: '1',\n    title: 'Analyst this requirement for me',\n    messages: [\n      {\n        id: '1',\n        content: 'I need to summary requirement',\n        type: 'user',\n        timestamp: new Date(Date.now() - 3600000)\n      },\n      {\n        id: '2',\n        content: 'Please provide me a requirement file (doc, image, pdf...) of give me some some information about ABC project.',\n        type: 'assistant',\n        timestamp: new Date(Date.now() - 3500000),\n        tokens: 25\n      }\n    ],\n    totalTokens: 25,\n    createdAt: new Date(Date.now() - 3600000),\n    updatedAt: new Date(Date.now() - 3500000),\n    projectId: 'current-project'\n  }\n]\n\nconst generateAIResponse = (userMessage: string): AIResponse => {\n  // Mock AI responses based on user input\n  const responses = [\n    {\n      content: \"I'd be happy to help you analyze this requirement. To provide the most accurate analysis, could you please share the specific requirement document or details you'd like me to review?\",\n      tokens: 35,\n      suggestions: [\n        \"Upload requirement document\",\n        \"Describe the requirement\",\n        \"Check for completeness\"\n      ]\n    },\n    {\n      content: \"Based on your request, I can help summarize requirements. Please provide the requirement documents or specific details you'd like me to analyze and summarize.\",\n      tokens: 28,\n      suggestions: [\n        \"Upload documents\",\n        \"List key requirements\",\n        \"Generate summary\"\n      ]\n    },\n    {\n      content: \"I can assist with requirement analysis including completeness checks, clarity assessment, and improvement suggestions. What specific aspect would you like me to focus on?\",\n      tokens: 32,\n      suggestions: [\n        \"Check completeness\",\n        \"Improve clarity\",\n        \"Generate test cases\"\n      ]\n    }\n  ]\n\n  return responses[Math.floor(Math.random() * responses.length)]\n}\n\nfunction* handleFetchConversations(action: Action) {\n  if (fetchConversationsRequest.match(action)) {\n    try {\n      const response = yield call(aiAssistantService.listConversations, 50, 0)\n\n      if (response.status === 200) {\n        // The API returns a different structure, so we need to handle it properly\n        let conversations: AIConversation[] = []\n\n        if (response.data && Array.isArray(response.data)) {\n          // If data is directly an array of conversations\n          conversations = response.data.map((conv: any) => aiAssistantService.transformConversation(conv))\n        } else if (response.data && response.data.conversations) {\n          // If data has a conversations property\n          conversations = response.data.conversations.map((conv: any) => aiAssistantService.transformConversation(conv))\n        }\n\n        yield put(fetchConversationsSuccess(conversations))\n      } else {\n        // Fallback to mock data if API fails\n        const conversations = generateMockConversations()\n        yield put(fetchConversationsSuccess(conversations))\n      }\n    } catch (error: any) {\n      // Fallback to mock data on error\n      const conversations = generateMockConversations()\n      yield put(fetchConversationsSuccess(conversations))\n      console.warn('AI API not available, using mock data:', error.message)\n    }\n  }\n}\n\nfunction* handleCreateConversation(action: Action) {\n  if (createConversationRequest.match(action)) {\n    try {\n      const request = action.payload as CreateConversationRequest\n      const response = yield call(aiAssistantService.createConversation, request)\n\n      if (response.status === 200) {\n        const newConversation = aiAssistantService.transformConversation(response.data)\n        // Set a default title if none provided\n        if (request.title) {\n          newConversation.title = request.title\n        }\n        yield put(createConversationSuccess(newConversation))\n        ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'New conversation created')\n      } else {\n        throw new Error('Failed to create conversation')\n      }\n    } catch (error: any) {\n      yield put(createConversationFailure(error.message))\n      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to create conversation')\n    }\n  }\n}\n\nfunction* handleSendMessage(action: Action) {\n  if (sendMessageRequest.match(action)) {\n    try {\n      const request = action.payload as SendMessageRequest\n\n      // Create user message\n      const userMessage: AIMessage = {\n        id: Date.now().toString(),\n        content: request.content,\n        type: 'user',\n        timestamp: new Date()\n      }\n\n      yield put(sendMessageSuccess(userMessage))\n\n      // Show AI typing indicator\n      yield put(setAITyping(true))\n\n      try {\n        // Send message to AI API\n        const apiRequest: SendMessageRequest = {\n          ...request,\n          agentType: request.agentType || getDefaultAgentType(),\n          projectId: request.projectId || getProjectId()\n        }\n\n        if (request.stream) {\n          // Handle real streaming response with SSE\n          const streamingMessageId = `streaming_${Date.now()}`\n          yield put(startStreamingResponse(streamingMessageId))\n\n          try {\n            // Start the streaming saga\n            yield fork(handleStreamingResponse, apiRequest, streamingMessageId)\n          } catch (streamError: any) {\n            yield put(streamingError({ messageId: streamingMessageId, error: streamError.message }))\n          }\n        } else {\n          // Handle regular response\n          const response = yield call(aiAssistantService.sendMessage, apiRequest)\n\n          if (response.status === 200) {\n            let content = ''\n            let tokensUsed = 0\n\n            if (response.data && response.data.message) {\n              content = response.data.message.content || response.data.message\n              tokensUsed = response.data.tokens_used || response.data.message.tokens || 0\n            } else if (typeof response.data === 'string') {\n              content = response.data\n            }\n\n            const aiResponse: AIResponse = {\n              content,\n              tokens: tokensUsed,\n              suggestions: [] // Add suggestions logic if needed\n            }\n            yield put(receiveAIResponse(aiResponse))\n          } else {\n            throw new Error('Failed to get AI response')\n          }\n        }\n      } catch (apiError: any) {\n        // Fallback to mock response if API fails\n        console.warn('AI API failed, using mock response:', apiError.message)\n        const aiResponse = generateAIResponse(request.content)\n        yield put(receiveAIResponse(aiResponse))\n      }\n\n    } catch (error: any) {\n      yield put(sendMessageFailure(error.message))\n      yield put(setAITyping(false))\n      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to send message')\n    }\n  }\n}\n\nfunction* handleDeleteConversation(action: Action) {\n  if (deleteConversationRequest.match(action)) {\n    try {\n      const conversationId = action.payload as string\n      const response = yield call(aiAssistantService.deleteConversation, conversationId)\n\n      if (response.status === 200) {\n        yield put(deleteConversationSuccess(conversationId))\n        ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'Conversation deleted')\n      } else {\n        throw new Error('Failed to delete conversation')\n      }\n    } catch (error: any) {\n      yield put(deleteConversationFailure(error.message))\n      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to delete conversation')\n    }\n  }\n}\n\nfunction* handleFetchTokenUsage(action: Action) {\n  if (fetchTokenUsageRequest.match(action)) {\n    try {\n      // const response = yield call(apiCall, 'GET', AI_API_URLS.TOKEN_USAGE)\n      yield delay(300)\n\n      const tokenUsage: TokenUsage = {\n        used: 150,\n        available: 1000,\n        percentage: 15\n      }\n\n      yield put(fetchTokenUsageSuccess(tokenUsage))\n    } catch (error) {\n      yield put(fetchTokenUsageFailure('Failed to fetch token usage'))\n    }\n  }\n}\n\nfunction* watchAIAssistantRequests() {\n  yield takeLatest(fetchConversationsRequest.type, handleFetchConversations)\n  yield takeLatest(createConversationRequest.type, handleCreateConversation)\n  yield takeLatest(sendMessageRequest.type, handleSendMessage)\n  yield takeLatest(deleteConversationRequest.type, handleDeleteConversation)\n  yield takeLatest(fetchTokenUsageRequest.type, handleFetchTokenUsage)\n}\n\nexport default function* aiAssistantSaga() {\n  yield all([fork(watchAIAssistantRequests)])\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/saga.ts b/src/modules/_shared/ai/saga.ts
--- a/src/modules/_shared/ai/saga.ts	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/modules/_shared/ai/saga.ts	(date 1751346090161)
@@ -27,7 +27,7 @@
   fetchTokenUsageSuccess,
   fetchTokenUsageFailure,
   setAITyping,
-  setSuggestions
+  setSuggestions,
 } from './actions'
 import {
   AIConversation,
@@ -91,18 +91,20 @@
 
       if (action.type === 'CHUNK') {
         const event = action.payload as TeamRunResponseContentEvent
-        yield put(receiveStreamingChunk({
-          messageId: streamingMessageId,
-          chunk: event.content,
-          agentName: event.agent_name,
-          runId: event.run_id,
-          teamId: event.team_id,
-          teamName: event.team_name
-        }))
+        if (event.event === 'TeamRunResponseContent') {
+          yield put(receiveStreamingChunk({
+            messageId: streamingMessageId,
+            chunk: event.content,
+            agentName: event.agent_name,
+            runId: event.run_id,
+            teamId: event.team_id,
+            teamName: event.team_name
+          }))
+        }
       } else if (action.type === 'COMPLETE') {
         yield put(endStreamingResponse({
           messageId: streamingMessageId,
-          totalTokens: 0 // Will be updated when we get token info
+          totalTokens: 0 // TODO: Will be updated when we get token info
         }))
         break
       } else if (action.type === 'ERROR') {
@@ -124,96 +126,27 @@
   }
 }
 
-// Mock data generators for development
-const generateMockConversations = (): AIConversation[] => [
-  {
-    id: '1',
-    title: 'Analyst this requirement for me',
-    messages: [
-      {
-        id: '1',
-        content: 'I need to summary requirement',
-        type: 'user',
-        timestamp: new Date(Date.now() - 3600000)
-      },
-      {
-        id: '2',
-        content: 'Please provide me a requirement file (doc, image, pdf...) of give me some some information about ABC project.',
-        type: 'assistant',
-        timestamp: new Date(Date.now() - 3500000),
-        tokens: 25
-      }
-    ],
-    totalTokens: 25,
-    createdAt: new Date(Date.now() - 3600000),
-    updatedAt: new Date(Date.now() - 3500000),
-    projectId: 'current-project'
-  }
-]
-
-const generateAIResponse = (userMessage: string): AIResponse => {
-  // Mock AI responses based on user input
-  const responses = [
-    {
-      content: "I'd be happy to help you analyze this requirement. To provide the most accurate analysis, could you please share the specific requirement document or details you'd like me to review?",
-      tokens: 35,
-      suggestions: [
-        "Upload requirement document",
-        "Describe the requirement",
-        "Check for completeness"
-      ]
-    },
-    {
-      content: "Based on your request, I can help summarize requirements. Please provide the requirement documents or specific details you'd like me to analyze and summarize.",
-      tokens: 28,
-      suggestions: [
-        "Upload documents",
-        "List key requirements",
-        "Generate summary"
-      ]
-    },
-    {
-      content: "I can assist with requirement analysis including completeness checks, clarity assessment, and improvement suggestions. What specific aspect would you like me to focus on?",
-      tokens: 32,
-      suggestions: [
-        "Check completeness",
-        "Improve clarity",
-        "Generate test cases"
-      ]
-    }
-  ]
-
-  return responses[Math.floor(Math.random() * responses.length)]
-}
-
 function* handleFetchConversations(action: Action) {
   if (fetchConversationsRequest.match(action)) {
     try {
       const response = yield call(aiAssistantService.listConversations, 50, 0)
 
-      if (response.status === 200) {
-        // The API returns a different structure, so we need to handle it properly
-        let conversations: AIConversation[] = []
+      // The API returns a different structure, so we need to handle it properly
+      let conversations: AIConversation[] = []
 
-        if (response.data && Array.isArray(response.data)) {
-          // If data is directly an array of conversations
-          conversations = response.data.map((conv: any) => aiAssistantService.transformConversation(conv))
-        } else if (response.data && response.data.conversations) {
-          // If data has a conversations property
-          conversations = response.data.conversations.map((conv: any) => aiAssistantService.transformConversation(conv))
-        }
+      if (response.data && Array.isArray(response.data)) {
+        // If data is directly an array of conversations
+        conversations = response.data.map((conv: any) => aiAssistantService.transformConversation(conv))
+      } else if (response.data && response.data.conversations) {
+        // If data has a conversations property
+        conversations = response.data.conversations.map((conv: any) => aiAssistantService.transformConversation(conv))
+      }
 
-        yield put(fetchConversationsSuccess(conversations))
-      } else {
-        // Fallback to mock data if API fails
-        const conversations = generateMockConversations()
-        yield put(fetchConversationsSuccess(conversations))
-      }
+      yield put(fetchConversationsSuccess(conversations))
     } catch (error: any) {
       // Fallback to mock data on error
-      const conversations = generateMockConversations()
-      yield put(fetchConversationsSuccess(conversations))
-      console.warn('AI API not available, using mock data:', error.message)
+      yield put(fetchConversationsFailure(error.message))
+      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to fetch conversations')
     }
   }
 }
@@ -224,17 +157,16 @@
       const request = action.payload as CreateConversationRequest
       const response = yield call(aiAssistantService.createConversation, request)
 
-      if (response.status === 200) {
-        const newConversation = aiAssistantService.transformConversation(response.data)
-        // Set a default title if none provided
-        if (request.title) {
-          newConversation.title = request.title
-        }
-        yield put(createConversationSuccess(newConversation))
-        ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'New conversation created')
-      } else {
-        throw new Error('Failed to create conversation')
-      }
+      const newConversation = aiAssistantService.transformConversation(response.data)
+      // Set a default title if none provided
+      if (request.title) {
+        newConversation.title = request.title
+      }
+      newConversation.sentToken = 0
+      newConversation.receiveToken = 0
+      newConversation.totalCost = 0
+      yield put(createConversationSuccess(newConversation))
+      // ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'New conversation created')
     } catch (error: any) {
       yield put(createConversationFailure(error.message))
       ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to create conversation')
@@ -244,9 +176,10 @@
 
 function* handleSendMessage(action: Action) {
   if (sendMessageRequest.match(action)) {
-    try {
-      const request = action.payload as SendMessageRequest
+    const request = action.payload as SendMessageRequest
 
+    try {
+
       // Create user message
       const userMessage: AIMessage = {
         id: Date.now().toString(),
@@ -260,61 +193,51 @@
       // Show AI typing indicator
       yield put(setAITyping(true))
 
-      try {
-        // Send message to AI API
-        const apiRequest: SendMessageRequest = {
-          ...request,
-          agentType: request.agentType || getDefaultAgentType(),
-          projectId: request.projectId || getProjectId()
-        }
+      // Send message to AI API
+      const apiRequest: SendMessageRequest = {
+        ...request,
+        agentType: request.agentType || getDefaultAgentType(),
+        projectId: request.projectId || getProjectId()
+      }
 
-        if (request.stream) {
-          // Handle real streaming response with SSE
-          const streamingMessageId = `streaming_${Date.now()}`
-          yield put(startStreamingResponse(streamingMessageId))
+      if (request.stream) {
+        // Handle real streaming response with SSE
+        const streamingMessageId = `streaming_${Date.now()}`
+        yield put(startStreamingResponse(streamingMessageId))
 
-          try {
-            // Start the streaming saga
-            yield fork(handleStreamingResponse, apiRequest, streamingMessageId)
-          } catch (streamError: any) {
-            yield put(streamingError({ messageId: streamingMessageId, error: streamError.message }))
-          }
-        } else {
-          // Handle regular response
-          const response = yield call(aiAssistantService.sendMessage, apiRequest)
+        try {
+          // Start the streaming saga
+          yield fork(handleStreamingResponse, apiRequest, streamingMessageId)
+        } catch (streamError: any) {
+          yield put(streamingError({ messageId: streamingMessageId, error: streamError.message }))
+        }
+      } else {
+        // Handle regular response
+        const response = yield call(aiAssistantService.sendMessage, apiRequest)
 
-          if (response.status === 200) {
-            let content = ''
-            let tokensUsed = 0
+        if (response.status === 200) {
+          let content = ''
+          let tokensUsed = 0
 
-            if (response.data && response.data.message) {
-              content = response.data.message.content || response.data.message
-              tokensUsed = response.data.tokens_used || response.data.message.tokens || 0
-            } else if (typeof response.data === 'string') {
-              content = response.data
-            }
+          if (response.data && response.data.message) {
+            content = response.data.message.content || response.data.message
+            tokensUsed = response.data.tokens_used || response.data.message.tokens || 0
+          } else if (typeof response.data === 'string') {
+            content = response.data
+          }
 
-            const aiResponse: AIResponse = {
-              content,
-              tokens: tokensUsed,
-              suggestions: [] // Add suggestions logic if needed
-            }
-            yield put(receiveAIResponse(aiResponse))
-          } else {
-            throw new Error('Failed to get AI response')
-          }
+          const aiResponse: AIResponse = {
+            content,
+            tokens: tokensUsed,
+            suggestions: [] // Add suggestions logic if needed
+          }
+          yield put(receiveAIResponse(aiResponse))
+        } else {
+          throw new Error('Failed to get AI response')
         }
-      } catch (apiError: any) {
-        // Fallback to mock response if API fails
-        console.warn('AI API failed, using mock response:', apiError.message)
-        const aiResponse = generateAIResponse(request.content)
-        yield put(receiveAIResponse(aiResponse))
       }
-
     } catch (error: any) {
       yield put(sendMessageFailure(error.message))
-      yield put(setAITyping(false))
-      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to send message')
     }
   }
 }
@@ -323,17 +246,11 @@
   if (deleteConversationRequest.match(action)) {
     try {
       const conversationId = action.payload as string
-      const response = yield call(aiAssistantService.deleteConversation, conversationId)
+      yield call(aiAssistantService.deleteConversation, conversationId)
 
-      if (response.status === 200) {
-        yield put(deleteConversationSuccess(conversationId))
-        ShowAppMessage(MESSAGE_TYPE.SUCCESS, null, 'Conversation deleted')
-      } else {
-        throw new Error('Failed to delete conversation')
-      }
+      yield put(deleteConversationSuccess(conversationId))
     } catch (error: any) {
       yield put(deleteConversationFailure(error.message))
-      ShowAppMessage(MESSAGE_TYPE.ERROR, null, 'Failed to delete conversation')
     }
   }
 }
@@ -341,6 +258,7 @@
 function* handleFetchTokenUsage(action: Action) {
   if (fetchTokenUsageRequest.match(action)) {
     try {
+      // TODO: Implement real apis
       // const response = yield call(apiCall, 'GET', AI_API_URLS.TOKEN_USAGE)
       yield delay(300)
 
@@ -351,8 +269,8 @@
       }
 
       yield put(fetchTokenUsageSuccess(tokenUsage))
-    } catch (error) {
-      yield put(fetchTokenUsageFailure('Failed to fetch token usage'))
+    } catch (error: any) {
+      yield put(fetchTokenUsageFailure(error.message))
     }
   }
 }
Index: src/modules/_shared/ai/actions.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { createAction } from '@reduxjs/toolkit'\nimport {\n  AIActionTypes,\n  AIConversation,\n  AIMessage,\n  SendMessageRequest,\n  CreateConversationRequest,\n  AIResponse,\n  TokenUsage,\n  StreamingChunkData\n} from './types'\n\n// UI Actions\nexport const setCurrentConversation = createAction<string>(AIActionTypes.SET_CURRENT_CONVERSATION)\n\n// Conversation Actions\nexport const fetchConversationsRequest = createAction(AIActionTypes.FETCH_CONVERSATIONS_REQUEST)\nexport const fetchConversationsSuccess = createAction<AIConversation[]>(AIActionTypes.FETCH_CONVERSATIONS_SUCCESS)\nexport const fetchConversationsFailure = createAction<string>(AIActionTypes.FETCH_CONVERSATIONS_FAILURE)\n\nexport const createConversationRequest = createAction<CreateConversationRequest>(AIActionTypes.CREATE_CONVERSATION_REQUEST)\nexport const createConversationSuccess = createAction<AIConversation>(AIActionTypes.CREATE_CONVERSATION_SUCCESS)\nexport const createConversationFailure = createAction<string>(AIActionTypes.CREATE_CONVERSATION_FAILURE)\n\nexport const deleteConversationRequest = createAction<string>(AIActionTypes.DELETE_CONVERSATION_REQUEST)\nexport const deleteConversationSuccess = createAction<string>(AIActionTypes.DELETE_CONVERSATION_SUCCESS)\nexport const deleteConversationFailure = createAction<string>(AIActionTypes.DELETE_CONVERSATION_FAILURE)\n\n// Message Actions\nexport const sendMessageRequest = createAction<SendMessageRequest>(AIActionTypes.SEND_MESSAGE_REQUEST)\nexport const sendMessageSuccess = createAction<AIMessage>(AIActionTypes.SEND_MESSAGE_SUCCESS)\nexport const sendMessageFailure = createAction<string>(AIActionTypes.SEND_MESSAGE_FAILURE)\n\nexport const receiveAIResponse = createAction<AIResponse>(AIActionTypes.RECEIVE_AI_RESPONSE)\n\n// Streaming Actions\nexport const startStreamingResponse = createAction<string>(AIActionTypes.START_STREAMING_RESPONSE) // messageId\nexport const receiveStreamingChunk = createAction<StreamingChunkData>(AIActionTypes.RECEIVE_STREAMING_CHUNK)\nexport const endStreamingResponse = createAction<{ messageId: string; totalTokens: number }>(AIActionTypes.END_STREAMING_RESPONSE)\nexport const streamingError = createAction<{ messageId: string; error: string }>(AIActionTypes.STREAMING_ERROR)\n\n// Token Actions\nexport const updateTokenUsage = createAction<TokenUsage>(AIActionTypes.UPDATE_TOKEN_USAGE)\nexport const fetchTokenUsageRequest = createAction(AIActionTypes.FETCH_TOKEN_USAGE_REQUEST)\nexport const fetchTokenUsageSuccess = createAction<TokenUsage>(AIActionTypes.FETCH_TOKEN_USAGE_SUCCESS)\nexport const fetchTokenUsageFailure = createAction<string>(AIActionTypes.FETCH_TOKEN_USAGE_FAILURE)\n\n// Typing Actions\nexport const setAITyping = createAction<boolean>(AIActionTypes.SET_AI_TYPING)\n\n// Suggestions Actions\nexport const setSuggestions = createAction<string[]>(AIActionTypes.SET_SUGGESTIONS)\nexport const clearSuggestions = createAction(AIActionTypes.CLEAR_SUGGESTIONS)\n\n// Error Actions\nexport const clearAIError = createAction(AIActionTypes.CLEAR_AI_ERROR)\n\n// Panel Management Actions\nexport const toggleAIChatPanel = createAction(AIActionTypes.TOGGLE_AI_CHAT_PANEL)\nexport const toggleAISetingsPanel = createAction(AIActionTypes.TOGGLE_SETTINGS_PANEL)\nexport const setActiveTab = createAction<'ai-chat' | 'settings' | null>(AIActionTypes.SET_ACTIVE_TAB)\nexport const closeAllPanels = createAction(AIActionTypes.CLOSE_ALL_PANELS)\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/modules/_shared/ai/actions.ts b/src/modules/_shared/ai/actions.ts
--- a/src/modules/_shared/ai/actions.ts	(revision 0207e1c2194c9d9f0d5f049a921970d2cbae601b)
+++ b/src/modules/_shared/ai/actions.ts	(date 1751346090160)
@@ -7,7 +7,8 @@
   CreateConversationRequest,
   AIResponse,
   TokenUsage,
-  StreamingChunkData
+  StreamingChunkData,
+  AIError
 } from './types'
 
 // UI Actions

<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="@localhost" uuid="aea397de-6e31-4b18-91e5-8e37c32f4a08">
      <driver-ref>mongo.4</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.dbschema.MongoJdbcDriver</jdbc-driver>
      <jdbc-url>mongodb://localhost:27017</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.host.port" />
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
        <property name="com.intellij.clouds.kubernetes.db.container.port" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>
<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="@localhost">
  <database-model serializer="dbm" dbms="MONGO" family-id="MONGO" format-version="4.53">
    <root id="1">
      <ServerVersion>8.0.11</ServerVersion>
    </root>
    <schema id="2" parent="1" name="admin"/>
    <schema id="3" parent="1" name="bavista">
      <LastIntrospectionLocalTimestamp>2025-07-03.18:06:15</LastIntrospectionLocalTimestamp>
    </schema>
    <schema id="4" parent="1" name="config"/>
    <schema id="5" parent="1" name="local"/>
    <schema id="6" parent="1" name="db_name">
      <LastIntrospectionLocalTimestamp>2025-07-03.18:06:15</LastIntrospectionLocalTimestamp>
    </schema>
    <schema id="7" parent="1" name="orca-ai">
      <LastIntrospectionLocalTimestamp>2025-07-04.08:44:23</LastIntrospectionLocalTimestamp>
    </schema>
    <table id="8" parent="3" name="_agno"/>
    <table id="9" parent="3" name="agent_sessions"/>
    <table id="10" parent="3" name="bavista"/>
    <table id="11" parent="3" name="conversations"/>
    <table id="12" parent="6" name="agent"/>
    <table id="13" parent="6" name="agents"/>
    <table id="14" parent="7" name="agents"/>
    <table id="15" parent="7" name="agno_sessions"/>
    <table id="16" parent="7" name="conversations"/>
    <table id="17" parent="7" name="messages"/>
    <column id="18" parent="8" name="_id">
      <Position>1</Position>
      <StoredType>ObjectId|12s</StoredType>
    </column>
    <column id="19" parent="8" name="_version">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="20" parent="8" name="created_at">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="21" parent="8" name="extra_data">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="22" parent="8" name="memory">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="23" parent="8" name="memory.memories">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="24" parent="8" name="memory.runs">
      <Position>1</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="25" parent="8" name="memory.runs.content">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="26" parent="8" name="memory.runs.content_type">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="27" parent="8" name="memory.runs.created_at">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="28" parent="8" name="memory.runs.member_responses">
      <Position>1</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="29" parent="8" name="memory.runs.messages">
      <Position>1</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="30" parent="8" name="memory.runs.messages.content">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="31" parent="8" name="memory.runs.messages.created_at">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="32" parent="8" name="memory.runs.messages.from_history">
      <Position>1</Position>
      <StoredType>Boolean|12s</StoredType>
    </column>
    <column id="33" parent="8" name="memory.runs.messages.metrics">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="34" parent="8" name="memory.runs.messages.metrics.completion_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="35" parent="8" name="memory.runs.messages.metrics.completion_tokens_details">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="36" parent="8" name="memory.runs.messages.metrics.completion_tokens_details.accepted_prediction_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="37" parent="8" name="memory.runs.messages.metrics.completion_tokens_details.audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="38" parent="8" name="memory.runs.messages.metrics.completion_tokens_details.reasoning_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="39" parent="8" name="memory.runs.messages.metrics.completion_tokens_details.rejected_prediction_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="40" parent="8" name="memory.runs.messages.metrics.input_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="41" parent="8" name="memory.runs.messages.metrics.output_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="42" parent="8" name="memory.runs.messages.metrics.prompt_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="43" parent="8" name="memory.runs.messages.metrics.prompt_tokens_details">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="44" parent="8" name="memory.runs.messages.metrics.prompt_tokens_details.audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="45" parent="8" name="memory.runs.messages.metrics.prompt_tokens_details.cached_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="46" parent="8" name="memory.runs.messages.metrics.time">
      <Position>1</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="47" parent="8" name="memory.runs.messages.metrics.time_to_first_token">
      <Position>1</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="48" parent="8" name="memory.runs.messages.metrics.total_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="49" parent="8" name="memory.runs.messages.role">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="50" parent="8" name="memory.runs.messages.stop_after_tool_call">
      <Position>1</Position>
      <StoredType>Boolean|12s</StoredType>
    </column>
    <column id="51" parent="8" name="memory.runs.metrics">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="52" parent="8" name="memory.runs.metrics.audio_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="53" parent="8" name="memory.runs.metrics.cache_write_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="54" parent="8" name="memory.runs.metrics.cached_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="55" parent="8" name="memory.runs.metrics.completion_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="56" parent="8" name="memory.runs.metrics.completion_tokens_details">
      <Position>1</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="57" parent="8" name="memory.runs.metrics.completion_tokens_details.accepted_prediction_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="58" parent="8" name="memory.runs.metrics.completion_tokens_details.audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="59" parent="8" name="memory.runs.metrics.completion_tokens_details.reasoning_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="60" parent="8" name="memory.runs.metrics.completion_tokens_details.rejected_prediction_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="61" parent="8" name="memory.runs.metrics.input_audio_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="62" parent="8" name="memory.runs.metrics.input_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="63" parent="8" name="memory.runs.metrics.output_audio_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="64" parent="8" name="memory.runs.metrics.output_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="65" parent="8" name="memory.runs.metrics.prompt_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="66" parent="8" name="memory.runs.metrics.prompt_tokens_details">
      <Position>1</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="67" parent="8" name="memory.runs.metrics.prompt_tokens_details.audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="68" parent="8" name="memory.runs.metrics.prompt_tokens_details.cached_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="69" parent="8" name="memory.runs.metrics.reasoning_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="70" parent="8" name="memory.runs.metrics.time">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="71" parent="8" name="memory.runs.metrics.time_to_first_token">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="72" parent="8" name="memory.runs.metrics.total_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="73" parent="8" name="memory.runs.model">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="74" parent="8" name="memory.runs.model_provider">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="75" parent="8" name="memory.runs.run_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="76" parent="8" name="memory.runs.session_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="77" parent="8" name="memory.runs.status">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="78" parent="8" name="memory.runs.team_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="79" parent="8" name="memory.runs.team_name">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="80" parent="8" name="memory.summaries">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="81" parent="8" name="session_data">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="82" parent="8" name="session_data.session_metrics">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="83" parent="8" name="session_data.session_metrics.additional_metrics">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="84" parent="8" name="session_data.session_metrics.audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="85" parent="8" name="session_data.session_metrics.cache_write_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="86" parent="8" name="session_data.session_metrics.cached_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="87" parent="8" name="session_data.session_metrics.completion_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="88" parent="8" name="session_data.session_metrics.completion_tokens_details">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="89" parent="8" name="session_data.session_metrics.completion_tokens_details.accepted_prediction_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="90" parent="8" name="session_data.session_metrics.completion_tokens_details.audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="91" parent="8" name="session_data.session_metrics.completion_tokens_details.reasoning_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="92" parent="8" name="session_data.session_metrics.completion_tokens_details.rejected_prediction_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="93" parent="8" name="session_data.session_metrics.input_audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="94" parent="8" name="session_data.session_metrics.input_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="95" parent="8" name="session_data.session_metrics.output_audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="96" parent="8" name="session_data.session_metrics.output_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="97" parent="8" name="session_data.session_metrics.prompt_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="98" parent="8" name="session_data.session_metrics.prompt_tokens_details">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="99" parent="8" name="session_data.session_metrics.prompt_tokens_details.audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="100" parent="8" name="session_data.session_metrics.prompt_tokens_details.cached_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="101" parent="8" name="session_data.session_metrics.reasoning_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="102" parent="8" name="session_data.session_metrics.time">
      <Position>1</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="103" parent="8" name="session_data.session_metrics.time_to_first_token">
      <Position>1</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="104" parent="8" name="session_data.session_metrics.timer">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="105" parent="8" name="session_data.session_metrics.total_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="106" parent="8" name="session_data.session_state">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="107" parent="8" name="session_data.session_state.current_session_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="108" parent="8" name="session_data.session_state.current_user_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="109" parent="8" name="session_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="110" parent="8" name="team_data">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="111" parent="8" name="team_data.mode">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="112" parent="8" name="team_data.model">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="113" parent="8" name="team_data.model.id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="114" parent="8" name="team_data.model.name">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="115" parent="8" name="team_data.model.provider">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="116" parent="8" name="team_data.name">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="117" parent="8" name="team_data.team_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="118" parent="8" name="team_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="119" parent="8" name="team_session_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="120" parent="8" name="updated_at">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="121" parent="8" name="user_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="122" parent="8" name="memory.runs.events">
      <Position>1</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <index id="123" parent="8" name="_id_">
      <ColNames>_id</ColNames>
      <Unique>1</Unique>
    </index>
    <column id="124" parent="9" name="_id">
      <Position>1</Position>
      <StoredType>ObjectId|12s</StoredType>
    </column>
    <column id="125" parent="9" name="_version">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="126" parent="9" name="created_at">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="127" parent="9" name="extra_data">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="128" parent="9" name="memory">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="129" parent="9" name="memory.memories">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="130" parent="9" name="memory.runs">
      <Position>1</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="131" parent="9" name="memory.runs.content">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="132" parent="9" name="memory.runs.content_type">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="133" parent="9" name="memory.runs.created_at">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="134" parent="9" name="memory.runs.events">
      <Position>1</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="135" parent="9" name="memory.runs.member_responses">
      <Position>1</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="136" parent="9" name="memory.runs.messages">
      <Position>1</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="137" parent="9" name="memory.runs.messages.content">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="138" parent="9" name="memory.runs.messages.created_at">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="139" parent="9" name="memory.runs.messages.from_history">
      <Position>1</Position>
      <StoredType>Boolean|12s</StoredType>
    </column>
    <column id="140" parent="9" name="memory.runs.messages.metrics">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="141" parent="9" name="memory.runs.messages.metrics.completion_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="142" parent="9" name="memory.runs.messages.metrics.completion_tokens_details">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="143" parent="9" name="memory.runs.messages.metrics.completion_tokens_details.accepted_prediction_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="144" parent="9" name="memory.runs.messages.metrics.completion_tokens_details.audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="145" parent="9" name="memory.runs.messages.metrics.completion_tokens_details.reasoning_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="146" parent="9" name="memory.runs.messages.metrics.completion_tokens_details.rejected_prediction_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="147" parent="9" name="memory.runs.messages.metrics.input_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="148" parent="9" name="memory.runs.messages.metrics.output_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="149" parent="9" name="memory.runs.messages.metrics.prompt_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="150" parent="9" name="memory.runs.messages.metrics.prompt_tokens_details">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="151" parent="9" name="memory.runs.messages.metrics.prompt_tokens_details.audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="152" parent="9" name="memory.runs.messages.metrics.prompt_tokens_details.cached_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="153" parent="9" name="memory.runs.messages.metrics.time">
      <Position>1</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="154" parent="9" name="memory.runs.messages.metrics.time_to_first_token">
      <Position>1</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="155" parent="9" name="memory.runs.messages.metrics.total_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="156" parent="9" name="memory.runs.messages.role">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="157" parent="9" name="memory.runs.messages.stop_after_tool_call">
      <Position>1</Position>
      <StoredType>Boolean|12s</StoredType>
    </column>
    <column id="158" parent="9" name="memory.runs.metrics">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="159" parent="9" name="memory.runs.metrics.audio_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="160" parent="9" name="memory.runs.metrics.cache_write_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="161" parent="9" name="memory.runs.metrics.cached_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="162" parent="9" name="memory.runs.metrics.completion_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="163" parent="9" name="memory.runs.metrics.completion_tokens_details">
      <Position>1</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="164" parent="9" name="memory.runs.metrics.completion_tokens_details.accepted_prediction_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="165" parent="9" name="memory.runs.metrics.completion_tokens_details.audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="166" parent="9" name="memory.runs.metrics.completion_tokens_details.reasoning_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="167" parent="9" name="memory.runs.metrics.completion_tokens_details.rejected_prediction_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="168" parent="9" name="memory.runs.metrics.input_audio_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="169" parent="9" name="memory.runs.metrics.input_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="170" parent="9" name="memory.runs.metrics.output_audio_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="171" parent="9" name="memory.runs.metrics.output_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="172" parent="9" name="memory.runs.metrics.prompt_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="173" parent="9" name="memory.runs.metrics.prompt_tokens_details">
      <Position>1</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="174" parent="9" name="memory.runs.metrics.prompt_tokens_details.audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="175" parent="9" name="memory.runs.metrics.prompt_tokens_details.cached_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="176" parent="9" name="memory.runs.metrics.reasoning_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="177" parent="9" name="memory.runs.metrics.time">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="178" parent="9" name="memory.runs.metrics.time_to_first_token">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="179" parent="9" name="memory.runs.metrics.total_tokens">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="180" parent="9" name="memory.runs.model">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="181" parent="9" name="memory.runs.model_provider">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="182" parent="9" name="memory.runs.run_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="183" parent="9" name="memory.runs.session_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="184" parent="9" name="memory.runs.status">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="185" parent="9" name="memory.runs.team_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="186" parent="9" name="memory.runs.team_name">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="187" parent="9" name="memory.summaries">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="188" parent="9" name="session_data">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="189" parent="9" name="session_data.session_metrics">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="190" parent="9" name="session_data.session_metrics.additional_metrics">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="191" parent="9" name="session_data.session_metrics.audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="192" parent="9" name="session_data.session_metrics.cache_write_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="193" parent="9" name="session_data.session_metrics.cached_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="194" parent="9" name="session_data.session_metrics.completion_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="195" parent="9" name="session_data.session_metrics.completion_tokens_details">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="196" parent="9" name="session_data.session_metrics.completion_tokens_details.accepted_prediction_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="197" parent="9" name="session_data.session_metrics.completion_tokens_details.audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="198" parent="9" name="session_data.session_metrics.completion_tokens_details.reasoning_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="199" parent="9" name="session_data.session_metrics.completion_tokens_details.rejected_prediction_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="200" parent="9" name="session_data.session_metrics.input_audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="201" parent="9" name="session_data.session_metrics.input_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="202" parent="9" name="session_data.session_metrics.output_audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="203" parent="9" name="session_data.session_metrics.output_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="204" parent="9" name="session_data.session_metrics.prompt_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="205" parent="9" name="session_data.session_metrics.prompt_tokens_details">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="206" parent="9" name="session_data.session_metrics.prompt_tokens_details.audio_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="207" parent="9" name="session_data.session_metrics.prompt_tokens_details.cached_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="208" parent="9" name="session_data.session_metrics.reasoning_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="209" parent="9" name="session_data.session_metrics.time">
      <Position>1</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="210" parent="9" name="session_data.session_metrics.time_to_first_token">
      <Position>1</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="211" parent="9" name="session_data.session_metrics.timer">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="212" parent="9" name="session_data.session_metrics.total_tokens">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="213" parent="9" name="session_data.session_state">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="214" parent="9" name="session_data.session_state.current_session_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="215" parent="9" name="session_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="216" parent="9" name="team_data">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="217" parent="9" name="team_data.mode">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="218" parent="9" name="team_data.model">
      <Position>1</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="219" parent="9" name="team_data.model.id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="220" parent="9" name="team_data.model.name">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="221" parent="9" name="team_data.model.provider">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="222" parent="9" name="team_data.name">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="223" parent="9" name="team_data.team_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="224" parent="9" name="team_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="225" parent="9" name="team_session_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="226" parent="9" name="updated_at">
      <Position>1</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="227" parent="9" name="user_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="228" parent="9" name="session_data.session_state.current_user_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="229" parent="9" name="memory.runs.extra_data">
      <Position>2</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="230" parent="9" name="memory.runs.messages.metrics.cached_tokens">
      <Position>2</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="231" parent="9" name="memory.runs.messages.tool_args">
      <Position>2</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="232" parent="9" name="memory.runs.reasoning_content">
      <Position>2</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="233" parent="9" name="memory.runs.tools">
      <Position>2</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="234" parent="9" name="session_data.session_state.reasoning_steps">
      <Position>2</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="235" parent="9" name="memory.runs.extra_data.reasoning_messages">
      <Position>3</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="236" parent="9" name="memory.runs.messages.tool_args.action">
      <Position>3</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="237" parent="9" name="memory.runs.tools.answered">
      <Position>3</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="238" parent="9" name="session_data.session_state.reasoning_steps.6fee021c-38d7-4740-a020-5d34d4347de5">
      <Position>3</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="239" parent="9" name="memory.runs.extra_data.reasoning_messages.content">
      <Position>4</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="240" parent="9" name="memory.runs.messages.tool_args.confidence">
      <Position>4</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="241" parent="9" name="memory.runs.tools.confirmation_note">
      <Position>4</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="242" parent="9" name="session_data.session_state.reasoning_steps.ac978975-54b6-4bbb-842e-eae9ecb4935b">
      <Position>4</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="243" parent="9" name="memory.runs.extra_data.reasoning_messages.created_at">
      <Position>5</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="244" parent="9" name="memory.runs.messages.tool_args.thought">
      <Position>5</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="245" parent="9" name="memory.runs.tools.confirmed">
      <Position>5</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="246" parent="9" name="session_data.session_state.reasoning_steps.bca32b11-7a09-437e-a751-ff2de6dae27f">
      <Position>5</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="247" parent="9" name="memory.runs.extra_data.reasoning_messages.from_history">
      <Position>6</Position>
      <StoredType>Boolean|12s</StoredType>
    </column>
    <column id="248" parent="9" name="memory.runs.messages.tool_args.title">
      <Position>6</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="249" parent="9" name="memory.runs.tools.created_at">
      <Position>6</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="250" parent="9" name="session_data.session_state.reasoning_steps.edd97826-8f86-46bb-92e4-f95ef286ecce">
      <Position>6</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="251" parent="9" name="memory.runs.extra_data.reasoning_messages.metrics">
      <Position>7</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="252" parent="9" name="memory.runs.messages.tool_call_error">
      <Position>7</Position>
      <StoredType>Boolean|12s</StoredType>
    </column>
    <column id="253" parent="9" name="memory.runs.tools.external_execution_required">
      <Position>7</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="254" parent="9" name="memory.runs.extra_data.reasoning_messages.metrics.time">
      <Position>8</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="255" parent="9" name="memory.runs.messages.tool_call_id">
      <Position>8</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="256" parent="9" name="memory.runs.tools.metrics">
      <Position>8</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="257" parent="9" name="memory.runs.extra_data.reasoning_messages.role">
      <Position>9</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="258" parent="9" name="memory.runs.messages.tool_calls">
      <Position>9</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="259" parent="9" name="memory.runs.tools.metrics.time">
      <Position>9</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="260" parent="9" name="memory.runs.extra_data.reasoning_messages.stop_after_tool_call">
      <Position>10</Position>
      <StoredType>Boolean|12s</StoredType>
    </column>
    <column id="261" parent="9" name="memory.runs.messages.tool_calls.function">
      <Position>10</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="262" parent="9" name="memory.runs.tools.requires_confirmation">
      <Position>10</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="263" parent="9" name="memory.runs.extra_data.reasoning_steps">
      <Position>11</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="264" parent="9" name="memory.runs.messages.tool_calls.function.arguments">
      <Position>11</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="265" parent="9" name="memory.runs.tools.requires_user_input">
      <Position>11</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="266" parent="9" name="memory.runs.extra_data.reasoning_steps.action">
      <Position>12</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="267" parent="9" name="memory.runs.messages.tool_calls.function.name">
      <Position>12</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="268" parent="9" name="memory.runs.tools.result">
      <Position>12</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="269" parent="9" name="memory.runs.extra_data.reasoning_steps.confidence">
      <Position>13</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="270" parent="9" name="memory.runs.messages.tool_calls.id">
      <Position>13</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="271" parent="9" name="memory.runs.tools.stop_after_tool_call">
      <Position>13</Position>
      <StoredType>Boolean|12s</StoredType>
    </column>
    <column id="272" parent="9" name="memory.runs.extra_data.reasoning_steps.next_action">
      <Position>14</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="273" parent="9" name="memory.runs.messages.tool_calls.type">
      <Position>14</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="274" parent="9" name="memory.runs.tools.tool_args">
      <Position>14</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="275" parent="9" name="memory.runs.extra_data.reasoning_steps.reasoning">
      <Position>15</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="276" parent="9" name="memory.runs.messages.tool_name">
      <Position>15</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="277" parent="9" name="memory.runs.tools.tool_args.action">
      <Position>15</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="278" parent="9" name="memory.runs.extra_data.reasoning_steps.result">
      <Position>16</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="279" parent="9" name="memory.runs.tools.tool_args.confidence">
      <Position>16</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="280" parent="9" name="memory.runs.extra_data.reasoning_steps.title">
      <Position>17</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="281" parent="9" name="memory.runs.tools.tool_args.thought">
      <Position>17</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="282" parent="9" name="memory.runs.formatted_tool_calls">
      <Position>18</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="283" parent="9" name="memory.runs.tools.tool_args.title">
      <Position>18</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="284" parent="9" name="memory.runs.tools.tool_call_error">
      <Position>19</Position>
      <StoredType>Boolean|12s</StoredType>
    </column>
    <column id="285" parent="9" name="memory.runs.tools.tool_call_id">
      <Position>20</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="286" parent="9" name="memory.runs.tools.tool_name">
      <Position>21</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="287" parent="9" name="memory.runs.tools.user_input_schema">
      <Position>23</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <index id="288" parent="9" name="_id_">
      <ColNames>_id</ColNames>
      <Unique>1</Unique>
    </index>
    <column id="289" parent="10" name="_id">
      <Position>1</Position>
      <StoredType>ObjectId|12s</StoredType>
    </column>
    <column id="290" parent="10" name="code">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="291" parent="10" name="created_at">
      <Position>1</Position>
      <StoredType>ISODate|91s</StoredType>
    </column>
    <column id="292" parent="10" name="name">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="293" parent="10" name="project_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="294" parent="10" name="tools">
      <Position>1</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="295" parent="10" name="updated_at">
      <Position>1</Position>
      <StoredType>ISODate|91s</StoredType>
    </column>
    <column id="296" parent="10" name="description">
      <Position>2</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="297" parent="10" name="prompt_template">
      <Position>2</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="298" parent="10" name="prompt_template.additional_context">
      <Position>3</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="299" parent="10" name="prompt_template.instructions">
      <Position>4</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="300" parent="10" name="role">
      <Position>5</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <index id="301" parent="10" name="_id_">
      <ColNames>_id</ColNames>
      <Unique>1</Unique>
    </index>
    <column id="302" parent="11" name="_id">
      <Position>1</Position>
      <StoredType>ObjectId|12s</StoredType>
    </column>
    <column id="303" parent="11" name="created_at">
      <Position>1</Position>
      <StoredType>ISODate|91s</StoredType>
    </column>
    <column id="304" parent="11" name="id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="305" parent="11" name="project_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="306" parent="11" name="title">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="307" parent="11" name="user_id">
      <Position>1</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <index id="308" parent="11" name="_id_">
      <ColNames>_id</ColNames>
      <Unique>1</Unique>
    </index>
    <column id="309" parent="13" name="_id">
      <Position>1</Position>
      <StoredType>ObjectId|12s</StoredType>
    </column>
    <column id="310" parent="13" name="additional_prompt">
      <Position>2</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="311" parent="13" name="code">
      <Position>3</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="312" parent="13" name="created_at">
      <Position>4</Position>
      <StoredType>ISODate|91s</StoredType>
    </column>
    <column id="313" parent="13" name="description">
      <Position>5</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="314" parent="13" name="model_id">
      <Position>6</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="315" parent="13" name="name">
      <Position>7</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="316" parent="13" name="role">
      <Position>8</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="317" parent="13" name="system_prompt">
      <Position>9</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="318" parent="13" name="tools">
      <Position>10</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="319" parent="13" name="updated_at">
      <Position>11</Position>
      <StoredType>ISODate|91s</StoredType>
    </column>
    <index id="320" parent="13" name="_id_">
      <ColNames>_id</ColNames>
      <Unique>1</Unique>
    </index>
    <index id="321" parent="13" name="code_1">
      <ColNames>code</ColNames>
      <Unique>1</Unique>
    </index>
    <column id="322" parent="14" name="_id">
      <Position>1</Position>
      <StoredType>ObjectId|12s</StoredType>
    </column>
    <column id="323" parent="14" name="additional_prompt">
      <Position>2</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="324" parent="14" name="code">
      <Position>3</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="325" parent="14" name="created_at">
      <Position>4</Position>
      <StoredType>ISODate|91s</StoredType>
    </column>
    <column id="326" parent="14" name="description">
      <Position>5</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="327" parent="14" name="model_id">
      <Position>6</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="328" parent="14" name="name">
      <Position>7</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="329" parent="14" name="role">
      <Position>8</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="330" parent="14" name="system_prompt">
      <Position>9</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="331" parent="14" name="tools">
      <Position>10</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="332" parent="14" name="updated_at">
      <Position>11</Position>
      <StoredType>ISODate|91s</StoredType>
    </column>
    <index id="333" parent="14" name="_id_">
      <ColNames>_id</ColNames>
      <Unique>1</Unique>
    </index>
    <index id="334" parent="14" name="code_1">
      <ColNames>code</ColNames>
      <Unique>1</Unique>
    </index>
    <column id="335" parent="15" name="_id">
      <Position>1</Position>
      <StoredType>ObjectId|12s</StoredType>
    </column>
    <column id="336" parent="15" name="_version">
      <Position>2</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="337" parent="15" name="created_at">
      <Position>3</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="338" parent="15" name="extra_data">
      <Position>4</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="339" parent="15" name="memory">
      <Position>5</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="340" parent="15" name="memory.memories">
      <Position>6</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="341" parent="15" name="memory.runs">
      <Position>7</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="342" parent="15" name="memory.runs.content">
      <Position>8</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="343" parent="15" name="memory.runs.content_type">
      <Position>9</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="344" parent="15" name="memory.runs.created_at">
      <Position>10</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="345" parent="15" name="memory.runs.member_responses">
      <Position>11</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="346" parent="15" name="memory.runs.messages">
      <Position>12</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="347" parent="15" name="memory.runs.messages.content">
      <Position>13</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="348" parent="15" name="memory.runs.messages.created_at">
      <Position>14</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="349" parent="15" name="memory.runs.messages.from_history">
      <Position>15</Position>
      <StoredType>Boolean|12s</StoredType>
    </column>
    <column id="350" parent="15" name="memory.runs.messages.metrics">
      <Position>16</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="351" parent="15" name="memory.runs.messages.metrics.completion_tokens">
      <Position>17</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="352" parent="15" name="memory.runs.messages.metrics.completion_tokens_details">
      <Position>18</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="353" parent="15" name="memory.runs.messages.metrics.completion_tokens_details.accepted_prediction_tokens">
      <Position>19</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="354" parent="15" name="memory.runs.messages.metrics.completion_tokens_details.audio_tokens">
      <Position>20</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="355" parent="15" name="memory.runs.messages.metrics.completion_tokens_details.reasoning_tokens">
      <Position>21</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="356" parent="15" name="memory.runs.messages.metrics.completion_tokens_details.rejected_prediction_tokens">
      <Position>22</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="357" parent="15" name="memory.runs.messages.metrics.input_tokens">
      <Position>23</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="358" parent="15" name="memory.runs.messages.metrics.output_tokens">
      <Position>24</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="359" parent="15" name="memory.runs.messages.metrics.prompt_tokens">
      <Position>25</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="360" parent="15" name="memory.runs.messages.metrics.prompt_tokens_details">
      <Position>26</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="361" parent="15" name="memory.runs.messages.metrics.prompt_tokens_details.audio_tokens">
      <Position>27</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="362" parent="15" name="memory.runs.messages.metrics.prompt_tokens_details.cached_tokens">
      <Position>28</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="363" parent="15" name="memory.runs.messages.metrics.time">
      <Position>29</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="364" parent="15" name="memory.runs.messages.metrics.time_to_first_token">
      <Position>30</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="365" parent="15" name="memory.runs.messages.metrics.total_tokens">
      <Position>31</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="366" parent="15" name="memory.runs.messages.role">
      <Position>32</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="367" parent="15" name="memory.runs.messages.stop_after_tool_call">
      <Position>33</Position>
      <StoredType>Boolean|12s</StoredType>
    </column>
    <column id="368" parent="15" name="memory.runs.metrics">
      <Position>34</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="369" parent="15" name="memory.runs.metrics.audio_tokens">
      <Position>35</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="370" parent="15" name="memory.runs.metrics.cache_write_tokens">
      <Position>36</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="371" parent="15" name="memory.runs.metrics.cached_tokens">
      <Position>37</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="372" parent="15" name="memory.runs.metrics.completion_tokens">
      <Position>38</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="373" parent="15" name="memory.runs.metrics.completion_tokens_details">
      <Position>39</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="374" parent="15" name="memory.runs.metrics.completion_tokens_details.accepted_prediction_tokens">
      <Position>40</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="375" parent="15" name="memory.runs.metrics.completion_tokens_details.audio_tokens">
      <Position>41</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="376" parent="15" name="memory.runs.metrics.completion_tokens_details.reasoning_tokens">
      <Position>42</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="377" parent="15" name="memory.runs.metrics.completion_tokens_details.rejected_prediction_tokens">
      <Position>43</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="378" parent="15" name="memory.runs.metrics.input_audio_tokens">
      <Position>44</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="379" parent="15" name="memory.runs.metrics.input_tokens">
      <Position>45</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="380" parent="15" name="memory.runs.metrics.output_audio_tokens">
      <Position>46</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="381" parent="15" name="memory.runs.metrics.output_tokens">
      <Position>47</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="382" parent="15" name="memory.runs.metrics.prompt_tokens">
      <Position>48</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="383" parent="15" name="memory.runs.metrics.prompt_tokens_details">
      <Position>49</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="384" parent="15" name="memory.runs.metrics.prompt_tokens_details.audio_tokens">
      <Position>50</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="385" parent="15" name="memory.runs.metrics.prompt_tokens_details.cached_tokens">
      <Position>51</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="386" parent="15" name="memory.runs.metrics.reasoning_tokens">
      <Position>52</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="387" parent="15" name="memory.runs.metrics.time">
      <Position>53</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="388" parent="15" name="memory.runs.metrics.time_to_first_token">
      <Position>54</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="389" parent="15" name="memory.runs.metrics.total_tokens">
      <Position>55</Position>
      <StoredType>Array|2003s</StoredType>
    </column>
    <column id="390" parent="15" name="memory.runs.model">
      <Position>56</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="391" parent="15" name="memory.runs.model_provider">
      <Position>57</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="392" parent="15" name="memory.runs.run_id">
      <Position>58</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="393" parent="15" name="memory.runs.session_id">
      <Position>59</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="394" parent="15" name="memory.runs.status">
      <Position>60</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="395" parent="15" name="memory.runs.team_id">
      <Position>61</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="396" parent="15" name="memory.runs.team_name">
      <Position>62</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="397" parent="15" name="memory.summaries">
      <Position>63</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="398" parent="15" name="session_data">
      <Position>64</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="399" parent="15" name="session_data.session_metrics">
      <Position>65</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="400" parent="15" name="session_data.session_metrics.additional_metrics">
      <Position>66</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="401" parent="15" name="session_data.session_metrics.audio_tokens">
      <Position>67</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="402" parent="15" name="session_data.session_metrics.cache_write_tokens">
      <Position>68</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="403" parent="15" name="session_data.session_metrics.cached_tokens">
      <Position>69</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="404" parent="15" name="session_data.session_metrics.completion_tokens">
      <Position>70</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="405" parent="15" name="session_data.session_metrics.completion_tokens_details">
      <Position>71</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="406" parent="15" name="session_data.session_metrics.completion_tokens_details.accepted_prediction_tokens">
      <Position>72</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="407" parent="15" name="session_data.session_metrics.completion_tokens_details.audio_tokens">
      <Position>73</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="408" parent="15" name="session_data.session_metrics.completion_tokens_details.reasoning_tokens">
      <Position>74</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="409" parent="15" name="session_data.session_metrics.completion_tokens_details.rejected_prediction_tokens">
      <Position>75</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="410" parent="15" name="session_data.session_metrics.input_audio_tokens">
      <Position>76</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="411" parent="15" name="session_data.session_metrics.input_tokens">
      <Position>77</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="412" parent="15" name="session_data.session_metrics.output_audio_tokens">
      <Position>78</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="413" parent="15" name="session_data.session_metrics.output_tokens">
      <Position>79</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="414" parent="15" name="session_data.session_metrics.prompt_tokens">
      <Position>80</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="415" parent="15" name="session_data.session_metrics.prompt_tokens_details">
      <Position>81</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="416" parent="15" name="session_data.session_metrics.prompt_tokens_details.audio_tokens">
      <Position>82</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="417" parent="15" name="session_data.session_metrics.prompt_tokens_details.cached_tokens">
      <Position>83</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="418" parent="15" name="session_data.session_metrics.reasoning_tokens">
      <Position>84</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="419" parent="15" name="session_data.session_metrics.time">
      <Position>85</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="420" parent="15" name="session_data.session_metrics.time_to_first_token">
      <Position>86</Position>
      <StoredType>Double|8s</StoredType>
    </column>
    <column id="421" parent="15" name="session_data.session_metrics.timer">
      <Position>87</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="422" parent="15" name="session_data.session_metrics.total_tokens">
      <Position>88</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="423" parent="15" name="session_data.session_state">
      <Position>89</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="424" parent="15" name="session_data.session_state.current_session_id">
      <Position>90</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="425" parent="15" name="session_data.session_state.current_user_id">
      <Position>91</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="426" parent="15" name="session_id">
      <Position>92</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="427" parent="15" name="team_data">
      <Position>93</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="428" parent="15" name="team_data.mode">
      <Position>94</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="429" parent="15" name="team_data.model">
      <Position>95</Position>
      <StoredType>Object|4999544s</StoredType>
    </column>
    <column id="430" parent="15" name="team_data.model.id">
      <Position>96</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="431" parent="15" name="team_data.model.name">
      <Position>97</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="432" parent="15" name="team_data.model.provider">
      <Position>98</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="433" parent="15" name="team_data.name">
      <Position>99</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="434" parent="15" name="team_data.team_id">
      <Position>100</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="435" parent="15" name="team_id">
      <Position>101</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="436" parent="15" name="team_session_id">
      <Position>102</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="437" parent="15" name="updated_at">
      <Position>103</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="438" parent="15" name="user_id">
      <Position>104</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <index id="439" parent="15" name="_id_">
      <ColNames>_id</ColNames>
      <Unique>1</Unique>
    </index>
    <column id="440" parent="16" name="_id">
      <Position>1</Position>
      <StoredType>ObjectId|12s</StoredType>
    </column>
    <column id="441" parent="16" name="completion_tokens">
      <Position>2</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="442" parent="16" name="created_at">
      <Position>3</Position>
      <StoredType>ISODate|91s</StoredType>
    </column>
    <column id="443" parent="16" name="project_id">
      <Position>4</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="444" parent="16" name="prompt_tokens">
      <Position>5</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="445" parent="16" name="title">
      <Position>6</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="446" parent="16" name="updated_at">
      <Position>7</Position>
      <StoredType>ISODate|91s</StoredType>
    </column>
    <column id="447" parent="16" name="user_id">
      <Position>8</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <index id="448" parent="16" name="_id_">
      <ColNames>_id</ColNames>
      <Unique>1</Unique>
    </index>
    <column id="449" parent="17" name="_id">
      <Position>1</Position>
      <StoredType>ObjectId|12s</StoredType>
    </column>
    <column id="450" parent="17" name="completion_tokens">
      <Position>2</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="451" parent="17" name="content">
      <Position>3</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="452" parent="17" name="conversation_id">
      <Position>4</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="453" parent="17" name="created_at">
      <Position>5</Position>
      <StoredType>ISODate|91s</StoredType>
    </column>
    <column id="454" parent="17" name="is_bot">
      <Position>6</Position>
      <StoredType>Boolean|12s</StoredType>
    </column>
    <column id="455" parent="17" name="prompt_tokens">
      <Position>7</Position>
      <StoredType>Int32|4s</StoredType>
    </column>
    <column id="456" parent="17" name="reasoning_content">
      <Position>8</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="457" parent="17" name="references">
      <Position>9</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="458" parent="17" name="reply_to">
      <Position>10</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="459" parent="17" name="response_time">
      <Position>11</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <column id="460" parent="17" name="steps">
      <Position>12</Position>
      <StoredType>list|4999545s</StoredType>
    </column>
    <column id="461" parent="17" name="updated_at">
      <Position>13</Position>
      <StoredType>ISODate|91s</StoredType>
    </column>
    <column id="462" parent="17" name="user_id">
      <Position>14</Position>
      <StoredType>String|12s</StoredType>
    </column>
    <index id="463" parent="17" name="_id_">
      <ColNames>_id</ColNames>
      <Unique>1</Unique>
    </index>
  </database-model>
</dataSource>
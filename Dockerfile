FROM node:16-alpine AS build
WORKDIR /app
COPY lib/ ./lib/
COPY package*.json ./
RUN npm config set strict-ssl false
RUN npm ci --legacy-peer-deps

COPY public/ ./public/
COPY ".env*" config-overrides.js craco.config.js tsconfig.json tsconfig.paths.json ./
COPY src/ ./src/
RUN npm run build-ba
 
FROM nginx:alpine AS run
COPY nginx.conf /etc/nginx/nginx.conf
COPY --from=build /app/build/ /usr/share/nginx/html/

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]